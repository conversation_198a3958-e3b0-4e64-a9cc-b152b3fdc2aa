# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/8 下午6:07
File Name:audio.py
"""
"""
音频功能测试
创建两个相同的项目：一个用my_jianying，一个用标准pyJianYingDraft
然后对比它们的差异
"""
import my_jianying as mj
import pyJianYingDraft as draft
from pyJianYingDraft import AudioSegment as StdAudioSegment, AudioMaterial as StdAudioMaterial, trange as std_trange
import os
import json


def create_my_jianying_audio_project():
    """创建my_jianying版本的音频项目"""
    print("🔄 创建my_jianying版本的音频项目...")

    try:
        # 创建输出目录
        os.makedirs("./output/audio_comparison", exist_ok=True)

        # 创建项目
        draft_folder = mj.DraftFolder("./output/audio_comparison")
        script = draft_folder.create_draft("audio_features_my", 1920, 1080, allow_replace=True)
        script.add_track("audio")

        # 使用测试音频文件
        audio_path = "./readme_assets/tutorial/audio.mp3"
        if not os.path.exists(audio_path):
            print(f"⚠️ 测试音频文件不存在: {audio_path}")
            print("请确保测试音频文件存在")
            return None

        # 1. 基础音频片段 (0-2秒)
        print("  添加基础音频片段...")
        basic_audio = mj.AudioSegment(
            audio_path,
            mj.trange("0s", "2s")
        )
        script.add_segment(basic_audio)

        # 2. 带音量调节的音频 (2-4秒)
        print("  添加带音量调节的音频...")
        volume_audio = mj.AudioSegment(
            audio_path,
            mj.trange("2s", "2s"),
            volume=0.6  # 60%音量
        )
        script.add_segment(volume_audio)

        # 3. 带速度调节的音频 (4-6秒)
        print("  添加带速度调节的音频...")
        speed_audio = mj.AudioSegment(
            audio_path,
            mj.trange("4s", "2s"),
            speed=1.5  # 1.5倍速
        )
        script.add_segment(speed_audio)

        # 4. 带source_timerange的音频 (6-8秒)
        print("  添加带source_timerange的音频...")
        source_audio = mj.AudioSegment(
            audio_path,
            mj.trange("6s", "2s"),
            source_timerange=mj.trange("1s", "3s")  # 从音频的1秒开始，取3秒
        )
        script.add_segment(source_audio)

        # 5. 带淡入淡出的音频 (8-10秒)
        print("  添加带淡入淡出的音频...")
        fade_audio = mj.AudioSegment(
            audio_path,
            mj.trange("8s", "2s"),
            volume=0.8
        )
        fade_audio.add_fade("0.5s", "0.5s")  # 0.5秒淡入，0.5秒淡出
        script.add_segment(fade_audio)

        # 保存项目
        project_path = script.save()
        print(f"✅ my_jianying音频项目已保存到: {project_path}")

        return project_path

    except Exception as e:
        print(f"❌ my_jianying音频项目创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def create_standard_audio_project():
    """创建标准pyJianYingDraft版本的音频项目"""
    print("🔄 创建标准pyJianYingDraft版本的音频项目...")

    try:
        # 创建项目
        draft_folder = draft.DraftFolder("./output/audio_comparison")
        script = draft_folder.create_draft("audio_features_std", 1920, 1080, allow_replace=True)
        script.add_track(draft.TrackType.audio)

        # 使用测试音频文件
        audio_path = "./readme_assets/tutorial/audio.mp3"

        # 1. 基础音频片段 (0-2秒)
        print("  添加基础音频片段...")
        basic_audio = StdAudioSegment(
            audio_path,
            std_trange("0s", "2s")
        )
        script.add_segment(basic_audio)
        script.save()


        # 2. 带音量调节的音频 (2-4秒)
        print("  添加带音量调节的音频...")
        volume_audio = StdAudioSegment(
            audio_path,
            std_trange("2s", "2s"),
            volume=0.6  # 60%音量
        )
        script.add_segment(volume_audio)

        # 3. 带速度调节的音频 (4-6秒)
        print("  添加带速度调节的音频...")
        speed_audio = StdAudioSegment(
            audio_path,
            std_trange("4s", "2s"),
            speed=1.5  # 1.5倍速
        )
        script.add_segment(speed_audio)

        # 4. 带source_timerange的音频 (6-8秒)
        print("  添加带source_timerange的音频...")
        source_audio = StdAudioSegment(
            audio_path,
            std_trange("6s", "2s"),
            source_timerange=std_trange("1s", "3s")  # 从音频的1秒开始，取3秒
        )
        script.add_segment(source_audio)

        # 5. 带淡入淡出的音频 (8-10秒)
        print("  添加带淡入淡出的音频...")
        fade_audio = StdAudioSegment(
            audio_path,
            std_trange("8s", "2s"),
            volume=0.8
        )
        fade_audio.add_fade("0.5s", "0.5s")  # 0.5秒淡入，0.5秒淡出
        script.add_segment(fade_audio)

        # 保存项目
        project_path = script.save()
        print(f"✅ 标准音频项目已保存到: {project_path}")

        # 返回正确的路径
        return "./output/audio_comparison/audio_features_std"

    except Exception as e:
        print(f"❌ 标准音频项目创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def analyze_audio_project(project_path, project_name):
    """分析生成的音频项目"""
    if not project_path:
        return None

    print(f"\n📊 {project_name}音频项目分析:")

    try:
        json_file = os.path.join(project_path, "draft_content.json")
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"   - 音频材料数量: {len(data['materials']['audios'])}")
        print(f"   - 速度材料数量: {len(data['materials']['speeds'])}")
        print(f"   - 轨道数量: {len(data['tracks'])}")
        print(f"   - 音频片段数量: {len(data['tracks'][0]['segments']) if data['tracks'] else 0}")
        print(f"   - 项目总时长: {data['duration']} (微秒) = {data['duration'] / 1000000:.1f}秒")

        return data

    except Exception as e:
        print(f"❌ {project_name}音频项目分析失败: {e}")
        return None


def compare_audio_projects(my_data, std_data):
    """对比两个音频项目的差异"""
    print(f"\n🔍 音频项目对比分析:")

    if not my_data or not std_data:
        print("❌ 无法对比，项目数据缺失")
        return

    # 基础数据对比
    print(f"📊 基础数据对比:")
    my_audios = len(my_data['materials']['audios'])
    std_audios = len(std_data['materials']['audios'])
    print(f"   音频材料数量: my_jianying={my_audios}, 标准={std_audios} {'✅' if my_audios == std_audios else '❌'}")

    my_speeds = len(my_data['materials']['speeds'])
    std_speeds = len(std_data['materials']['speeds'])
    print(f"   速度材料数量: my_jianying={my_speeds}, 标准={std_speeds} {'✅' if my_speeds == std_speeds else '❌'}")

    my_duration = my_data['duration']
    std_duration = std_data['duration']
    print(f"   项目时长: my_jianying={my_duration}, 标准={std_duration} {'✅' if my_duration == std_duration else '❌'}")

    # 总结
    print(f"\n🎯 对比总结:")
    if my_audios == std_audios and my_duration == std_duration:
        print(f"🎉 核心功能一致！音频项目基本匹配！")
    else:
        print(f"⚠️ 发现差异，需要进一步检查")

    print(f"\n📁 项目文件位置:")
    print(f"   my_jianying版本: ./output/audio_comparison/audio_features_my/")
    print(f"   标准版本: ./output/audio_comparison/audio_features_std/")
    print(f"🎬 两个项目都可以在剪映中打开查看效果差异")


def main():
    """主函数"""
    print("=== 音频功能对比测试 ===")
    print("创建两个相同的音频项目：一个用my_jianying，一个用标准pyJianYingDraft")
    print("然后对比它们的差异\n")

    # 创建两个项目
    my_path = create_my_jianying_audio_project()
    std_path = create_standard_audio_project()

    # 分析两个项目
    my_data = analyze_audio_project(my_path, "my_jianying")
    std_data = analyze_audio_project(std_path, "标准")

    # 对比项目
    compare_audio_projects(my_data, std_data)

    print(f"\n🎯 测试完成！")
    print(f"✅ 功能验证:")
    print(f"   - 基础音频片段创建")
    print(f"   - 音量调节")
    print(f"   - 播放速度调节")
    print(f"   - 音频片段截取（source_timerange）")
    print(f"   - 音频淡入淡出效果")
    print(f"   - 多个音频片段时间轴管理")
    print(f"⏱️ 每个项目时长: 10秒")
    print(f"📝 每个项目包含5个不同的音频效果")


if __name__ == "__main__":
    main()