"""
动画诊断工具 - 检查动画和转场是否正确生成
"""

import json
import os


def analyze_animations_and_transitions(json_file_path):
    """分析JSON文件中的动画和转场"""
    
    print("🔍 动画和转场诊断工具")
    print("=" * 60)
    
    if not os.path.exists(json_file_path):
        print(f"❌ 文件不存在: {json_file_path}")
        return
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    print(f"📁 分析文件: {json_file_path}")
    
    # 分析材料
    materials = data.get('materials', {})
    
    print(f"\n📊 材料统计:")
    print(f"   - 动画材料: {len(materials.get('material_animations', []))}个")
    print(f"   - 转场材料: {len(materials.get('transitions', []))}个")
    print(f"   - 特效材料: {len(materials.get('effects', []))}个")
    print(f"   - 文本材料: {len(materials.get('texts', []))}个")
    
    # 详细分析动画材料
    animations = materials.get('material_animations', [])
    if animations:
        print(f"\n🎬 动画材料详情:")
        for i, anim in enumerate(animations):
            anim_list = anim.get('animations', [])
            if anim_list:
                anim_detail = anim_list[0]
                print(f"   动画{i+1}:")
                print(f"     - ID: {anim.get('id', 'N/A')}")
                print(f"     - 名称: {anim_detail.get('name', 'N/A')}")
                print(f"     - 类型: {anim_detail.get('type', 'N/A')}")
                print(f"     - 时长: {anim_detail.get('duration', 'N/A')}微秒")
                print(f"     - 开始时间: {anim_detail.get('start', 'N/A')}微秒")
                print(f"     - 材料类型: {anim_detail.get('material_type', 'N/A')}")
    
    # 详细分析转场材料
    transitions = materials.get('transitions', [])
    if transitions:
        print(f"\n🔄 转场材料详情:")
        for i, trans in enumerate(transitions):
            print(f"   转场{i+1}:")
            print(f"     - ID: {trans.get('id', 'N/A')}")
            print(f"     - 名称: {trans.get('name', 'N/A')}")
            print(f"     - 时长: {trans.get('duration', 'N/A')}微秒")
            print(f"     - 效果ID: {trans.get('effect_id', 'N/A')}")
    
    # 分析轨道中的片段引用
    tracks = data.get('tracks', [])
    print(f"\n🛤️  轨道片段分析:")
    
    for track in tracks:
        track_type = track.get('type', 'unknown')
        track_name = track.get('name', 'unnamed')
        segments = track.get('segments', [])
        
        print(f"\n   {track_type}轨道 '{track_name}': {len(segments)}个片段")
        
        for i, segment in enumerate(segments):
            extra_refs = segment.get('extra_material_refs', [])
            timerange = segment.get('target_timerange', {})
            start_time = timerange.get('start', 0) / 1000000  # 转换为秒
            duration = timerange.get('duration', 0) / 1000000  # 转换为秒
            
            print(f"     片段{i+1}: {start_time:.1f}s-{start_time+duration:.1f}s")
            print(f"       - 材料引用: {len(extra_refs)}个")
            
            # 检查引用的材料类型
            if extra_refs:
                ref_types = []
                for ref_id in extra_refs:
                    # 查找这个ID对应的材料类型
                    ref_type = find_material_type_by_id(materials, ref_id)
                    if ref_type:
                        ref_types.append(ref_type)
                
                if ref_types:
                    print(f"       - 引用类型: {', '.join(ref_types)}")
    
    # 检查可能的问题
    print(f"\n⚠️  潜在问题检查:")
    
    issues = []
    
    # 检查是否有文本片段但没有文本动画
    text_segments = sum(1 for track in tracks if track.get('type') == 'text' for _ in track.get('segments', []))
    text_animations = sum(1 for anim in animations if any(a.get('material_type') == 'sticker' for a in anim.get('animations', [])))
    
    if text_segments > 0 and text_animations == 0:
        issues.append("文本片段存在但没有文本动画材料")
    
    # 检查是否有视频片段但没有转场
    video_segments = sum(1 for track in tracks if track.get('type') == 'video' for _ in track.get('segments', []))
    if video_segments > 1 and len(transitions) == 0:
        issues.append("多个视频片段但没有转场材料")
    
    # 检查动画时长是否合理
    for anim in animations:
        anim_list = anim.get('animations', [])
        if anim_list:
            duration = anim_list[0].get('duration', 0)
            if duration and duration < 100000:  # 小于0.1秒
                issues.append(f"动画时长过短: {duration}微秒")
    
    if issues:
        for issue in issues:
            print(f"   ❌ {issue}")
    else:
        print(f"   ✅ 未发现明显问题")
    
    # 提供建议
    print(f"\n💡 建议:")
    print(f"   1. 确保文本动画时长至少1秒 (1000000微秒)")
    print(f"   2. 转场时长建议0.5-1秒 (500000-1000000微秒)")
    print(f"   3. 检查动画类型是否被剪映支持")
    print(f"   4. 确保每个需要动画的片段都有对应的材料引用")


def find_material_type_by_id(materials, target_id):
    """根据ID查找材料类型"""
    
    # 检查各种材料类型
    material_types = {
        'material_animations': '动画',
        'transitions': '转场',
        'effects': '特效',
        'audio_fades': '音频淡入淡出',
        'speeds': '速度控制',
        'canvases': '画布'
    }
    
    for material_type, type_name in material_types.items():
        materials_list = materials.get(material_type, [])
        for material in materials_list:
            if material.get('id') == target_id:
                return type_name
    
    return "未知类型"


def main():
    """主函数"""
    
    # 分析不同的输出文件
    files_to_analyze = [
        "./advanced_output/draft_content.json",
        "./advanced_test_fixed_output/draft_content.json"
    ]
    
    for file_path in files_to_analyze:
        if os.path.exists(file_path):
            analyze_animations_and_transitions(file_path)
            print("\n" + "=" * 60 + "\n")
        else:
            print(f"⚠️  文件不存在: {file_path}")


if __name__ == "__main__":
    main()
