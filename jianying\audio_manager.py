"""
音频片段管理器
"""

from typing import Dict, Optional, List, Any
from data_structures import AudioSegmentInfo, TimeRange, generate_id


class AudioManager:
    """音频片段管理器"""
    
    def __init__(self):
        self.segments: Dict[str, AudioSegmentInfo] = {}
    
    def create_audio_segment(self, material_id: str, target_timerange: TimeRange, track_id: str,
                           source_timerange: Optional[TimeRange] = None, speed: float = 1.0,
                           volume: float = 1.0, change_pitch: bool = False) -> str:
        """创建音频片段，返回audio_id

        Args:
            material_id: 素材ID
            target_timerange: 目标时间范围
            track_id: 轨道ID
            source_timerange: 源时间范围
            speed: 播放速度
            volume: 音量
            change_pitch: 是否变调

        Returns:
            audio_id: 音频片段ID
        """
        segment_id = generate_id()
        
        segment_info = AudioSegmentInfo(
            segment_id=segment_id,
            material_id=material_id,
            track_id=track_id,
            target_timerange=target_timerange,
            source_timerange=source_timerange,
            speed=speed,
            volume=volume,
            change_pitch=change_pitch
        )
        
        self.segments[segment_id] = segment_info
        return segment_id
    
    def get_segment(self, audio_id: str) -> Optional[AudioSegmentInfo]:
        """获取音频片段信息"""
        return self.segments.get(audio_id)
    
    def add_fade(self, audio_id: str, fade_in_duration: int, fade_out_duration: int) -> bool:
        """给音频添加淡入淡出
        
        Args:
            audio_id: 音频片段ID
            fade_in_duration: 淡入时长（微秒）
            fade_out_duration: 淡出时长（微秒）
            
        Returns:
            是否添加成功
        """
        segment = self.segments.get(audio_id)
        if segment is None:
            return False
        
        # 检查是否已有淡入淡出效果
        for fade in segment.fade_effects:
            if fade.get("type") == "fade":
                return False  # 已存在淡入淡出效果
        
        fade_effect = {
            "type": "fade",
            "fade_in_duration": fade_in_duration,
            "fade_out_duration": fade_out_duration,
            "id": generate_id()
        }
        
        segment.fade_effects.append(fade_effect)
        return True
    
    def add_effect(self, audio_id: str, effect_type: str, params: Optional[List[Optional[float]]] = None) -> bool:
        """给音频添加特效
        
        Args:
            audio_id: 音频片段ID
            effect_type: 特效类型
            params: 特效参数
            
        Returns:
            是否添加成功
        """
        segment = self.segments.get(audio_id)
        if segment is None:
            return False
        
        # 检查是否已有相同类型的特效
        for effect in segment.audio_effects:
            if effect.get("type") == effect_type:
                return False  # 已存在相同类型的特效
        
        effect = {
            "type": effect_type,
            "params": params or [],
            "id": generate_id()
        }
        
        segment.audio_effects.append(effect)
        return True
    
    def set_volume(self, audio_id: str, volume: float) -> bool:
        """设置音频音量
        
        Args:
            audio_id: 音频片段ID
            volume: 音量 (0.0-1.0)
            
        Returns:
            是否设置成功
        """
        segment = self.segments.get(audio_id)
        if segment is None:
            return False
        
        segment.volume = max(0.0, min(1.0, volume))  # 限制在0-1范围内
        return True
    
    def add_keyframe(self, audio_id: str, time_offset: int, volume: float) -> bool:
        """添加音量关键帧
        
        Args:
            audio_id: 音频片段ID
            time_offset: 时间偏移（相对于片段开始时间，微秒）
            volume: 音量值 (0.0-1.0)
            
        Returns:
            是否添加成功
        """
        segment = self.segments.get(audio_id)
        if segment is None:
            return False
        
        # 检查时间偏移是否在有效范围内
        if time_offset < 0 or time_offset > segment.target_timerange.duration:
            return False
        
        keyframe = {
            "time_offset": time_offset,
            "volume": max(0.0, min(1.0, volume)),  # 限制在0-1范围内
            "id": generate_id()
        }
        
        segment.keyframes.append(keyframe)
        return True
    
    def get_all_segments(self) -> Dict[str, AudioSegmentInfo]:
        """获取所有音频片段"""
        return self.segments.copy()
    
    def remove_segment(self, audio_id: str) -> bool:
        """删除音频片段"""
        if audio_id in self.segments:
            del self.segments[audio_id]
            return True
        return False
