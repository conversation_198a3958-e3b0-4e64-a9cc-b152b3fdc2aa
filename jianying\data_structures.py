"""
数据结构定义
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any, Union, Tuple, Literal
import uuid


@dataclass
class DraftInfo:
    """草稿信息"""
    draft_id: str
    width: int
    height: int
    fps: int
    duration: int = 0
    name: str = ""


@dataclass
class TrackInfo:
    """轨道信息"""
    track_id: str
    track_type: str  # "audio", "video", "text"
    name: str
    mute: bool = False
    segments: List[str] = None  # segment_id列表
    
    def __post_init__(self):
        if self.segments is None:
            self.segments = []


@dataclass
class TimeRange:
    """时间范围"""
    start: int  # 微秒
    duration: int  # 微秒
    
    @property
    def end(self) -> int:
        return self.start + self.duration


@dataclass
class ClipSettings:
    """图像调节设置"""
    brightness: float = 0.0      # 亮度 (-100 到 100)
    contrast: float = 0.0        # 对比度 (-100 到 100)
    saturation: float = 0.0      # 饱和度 (-100 到 100)
    temperature: float = 0.0     # 色温 (-100 到 100)
    tint: float = 0.0           # 色调 (-100 到 100)
    highlights: float = 0.0      # 高光 (-100 到 100)
    shadows: float = 0.0         # 阴影 (-100 到 100)
    vignette: float = 0.0        # 暗角 (-100 到 100)
    sharpen: float = 0.0         # 锐化 (0 到 100)
    hue: float = 0.0            # 色相 (-180 到 180)


@dataclass
class TextStyle:
    """文本样式设置"""
    size: float = 8.0                                    # 字体大小
    bold: bool = False                                   # 是否粗体
    italic: bool = False                                 # 是否斜体
    underline: bool = False                              # 是否下划线
    color: Tuple[float, float, float] = (1.0, 1.0, 1.0) # 文字颜色 RGB (0-1)
    alpha: float = 1.0                                   # 透明度 (0-1)
    align: Literal[0, 1, 2] = 0                         # 对齐方式 0:左 1:中 2:右
    vertical: bool = False                               # 是否竖直排列
    letter_spacing: int = 0                              # 字符间距
    line_spacing: int = 0                                # 行间距
    auto_wrapping: bool = False                          # 是否自动换行
    max_line_width: float = 0.82                         # 最大行宽 (0-1)


@dataclass
class TextBorder:
    """文本描边设置"""
    alpha: float = 1.0                                   # 描边透明度 (0-1)
    color: Tuple[float, float, float] = (0.0, 0.0, 0.0) # 描边颜色 RGB (0-1)
    width: float = 40.0                                  # 描边宽度 (0-100)


@dataclass
class TextBackground:
    """文本背景设置"""
    color: str = "#000000"                               # 背景颜色 #RRGGBB
    style: Literal[1, 2] = 1                            # 背景样式 1:矩形 2:圆角矩形
    alpha: float = 1.0                                   # 背景透明度 (0-1)
    round_radius: float = 0.0                            # 圆角半径 (0-1)
    height: float = 0.14                                 # 背景高度 (0-1)
    width: float = 0.14                                  # 背景宽度 (0-1)
    horizontal_offset: float = 0.5                       # 水平偏移 (0-1)
    vertical_offset: float = 0.5                         # 竖直偏移 (0-1)


@dataclass
class MediaInfo:
    """媒体素材信息"""
    material_id: str
    path: str
    material_type: str  # "video", "audio"
    duration: int = 0
    width: int = 0
    height: int = 0


@dataclass
class VideoSegmentInfo:
    """视频片段信息"""
    segment_id: str
    material_id: str
    track_id: str
    target_timerange: TimeRange
    source_timerange: Optional[TimeRange] = None
    speed: float = 1.0
    volume: float = 1.0
    change_pitch: bool = False  # 新增：是否变调
    clip_settings: Optional[ClipSettings] = None  # 新增：图像调节设置

    # 特效相关
    animations: List[Dict[str, Any]] = None
    effects: List[Dict[str, Any]] = None
    filters: List[Dict[str, Any]] = None
    transitions: List[Dict[str, Any]] = None
    masks: List[Dict[str, Any]] = None
    background_fillings: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.animations is None:
            self.animations = []
        if self.effects is None:
            self.effects = []
        if self.filters is None:
            self.filters = []
        if self.transitions is None:
            self.transitions = []
        if self.masks is None:
            self.masks = []
        if self.background_fillings is None:
            self.background_fillings = []


@dataclass
class AudioSegmentInfo:
    """音频片段信息"""
    segment_id: str
    material_id: str
    track_id: str
    target_timerange: TimeRange
    source_timerange: Optional[TimeRange] = None
    speed: float = 1.0
    volume: float = 1.0
    change_pitch: bool = False  # 新增：是否变调

    # 特效相关
    fade_effects: List[Dict[str, Any]] = None
    audio_effects: List[Dict[str, Any]] = None
    keyframes: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.fade_effects is None:
            self.fade_effects = []
        if self.audio_effects is None:
            self.audio_effects = []
        if self.keyframes is None:
            self.keyframes = []


@dataclass
class TextSegmentInfo:
    """文本片段信息"""
    segment_id: str
    track_id: str
    text: str
    target_timerange: TimeRange
    
    # 样式相关
    font: Optional[Dict[str, Any]] = None
    style: Optional[TextStyle] = None  # 改为TextStyle类
    clip_settings: Optional[ClipSettings] = None  # 新增：图像调节设置
    border: Optional[TextBorder] = None  # 改为TextBorder类
    background: Optional[TextBackground] = None  # 改为TextBackground类
    
    # 特效相关
    animations: List[Dict[str, Any]] = None
    effects: List[Dict[str, Any]] = None
    bubbles: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.animations is None:
            self.animations = []
        if self.effects is None:
            self.effects = []
        if self.bubbles is None:
            self.bubbles = []


def generate_id() -> str:
    """生成唯一ID"""
    return uuid.uuid4().hex


def tim(time_input: Union[str, int, float]) -> int:
    """时间转微秒

    Args:
        time_input: 时间输入，支持:
            - 字符串: "1.5s", "500ms", "1000000us"
            - 数字: 直接当作秒处理

    Returns:
        微秒数
    """
    if isinstance(time_input, str):
        if time_input.endswith('s') and not time_input.endswith('us') and not time_input.endswith('ms'):
            # 秒
            return int(float(time_input[:-1]) * 1000000)
        elif time_input.endswith('ms'):
            # 毫秒
            return int(float(time_input[:-2]) * 1000)
        elif time_input.endswith('us'):
            # 微秒
            return int(float(time_input[:-2]))
        else:
            # 纯数字字符串，当作微秒
            return int(time_input)
    else:
        # 数字，当作秒
        return int(float(time_input) * 1000000)


def trange(start: Union[str, int, float], duration: Union[str, int, float]) -> TimeRange:
    """创建时间范围

    Args:
        start: 开始时间（秒或时间字符串）
        duration: 持续时间（秒或时间字符串）

    Returns:
        TimeRange对象
    """
    start_us = tim(start)
    duration_us = tim(duration)
    return TimeRange(start_us, duration_us)
