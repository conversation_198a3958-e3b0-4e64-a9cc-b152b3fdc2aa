"""
草稿管理器 - 总控制器
"""

import json
import os
from typing import Dict, Any, Optional, List
from data_structures import DraftInfo, generate_id, trange
from track_manager import TrackManager
from video_manager import VideoManager
from audio_manager import AudioManager
from text_manager import TextManager
from media_manager import MediaManager


class DraftManager:
    """草稿管理器 - 负责创建和导出草稿"""
    
    def __init__(self):
        self.draft_info: Optional[DraftInfo] = None
        self.track_manager = TrackManager()
        self.video_manager = VideoManager()
        self.audio_manager = AudioManager()
        self.text_manager = TextManager()
        self.media_manager = MediaManager()
    
    def create_draft(self, width: int = 1920, height: int = 1080, fps: int = 30, name: str = "") -> str:
        """创建新草稿
        
        Args:
            width: 视频宽度
            height: 视频高度
            fps: 帧率
            name: 草稿名称
            
        Returns:
            draft_id: 草稿ID
        """
        draft_id = generate_id()
        
        self.draft_info = DraftInfo(
            draft_id=draft_id,
            width=width,
            height=height,
            fps=fps,
            name=name
        )
        
        return draft_id
    
    def create_simple_project(self, 
                            audio_path: str = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\audio.mp3",
                            video_path: str = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4",
                            text_content: str = "大家好") -> Dict[str, str]:
        """创建简单项目：草稿 + 三个轨道 + 三个素材
        
        Args:
            audio_path: 音频文件路径
            video_path: 视频文件路径
            text_content: 文本内容
            
        Returns:
            包含所有ID的字典
        """
        # 1. 创建草稿
        draft_id = self.create_draft(1920, 1080, 30)
        
        # 2. 创建三个轨道
        audio_track_id = self.track_manager.create_track("audio", "audio")
        video_track_id = self.track_manager.create_track("video", "video")
        text_track_id = self.track_manager.create_track("text", "text")
        
        # 3. 注册素材
        audio_material_id = self.media_manager.register_audio_material(audio_path)
        video_material_id = self.media_manager.register_video_material(video_path)
        
        # 4. 创建片段
        audio_segment_id = self.audio_manager.create_audio_segment(
            material_id=audio_material_id,
            target_timerange=trange("0s", "5s"),
            track_id=audio_track_id,
            volume=0.6
        )
        
        video_segment_id = self.video_manager.create_video_segment(
            material_id=video_material_id,
            target_timerange=trange("0s", "4.2s"),
            track_id=video_track_id
        )
        
        text_segment_id = self.text_manager.create_text_segment(
            text=text_content,
            target_timerange=trange("0s", "4.2s"),
            track_id=text_track_id
        )
        
        # 5. 将片段添加到轨道
        self.track_manager.add_segment_to_track(audio_track_id, audio_segment_id)
        self.track_manager.add_segment_to_track(video_track_id, video_segment_id)
        self.track_manager.add_segment_to_track(text_track_id, text_segment_id)
        
        return {
            "draft_id": draft_id,
            "audio_track_id": audio_track_id,
            "video_track_id": video_track_id,
            "text_track_id": text_track_id,
            "audio_material_id": audio_material_id,
            "video_material_id": video_material_id,
            "audio_segment_id": audio_segment_id,
            "video_segment_id": video_segment_id,
            "text_segment_id": text_segment_id
        }
    
    def export_draft(self, output_dir: str) -> bool:
        """导出draft_content.json和draft_meta_info.json
        
        Args:
            output_dir: 输出目录
            
        Returns:
            是否导出成功
        """
        if self.draft_info is None:
            return False
        
        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 导出draft_content.json
            draft_content = self._build_draft_content()
            content_path = os.path.join(output_dir, "draft_content.json")
            with open(content_path, 'w', encoding='utf-8') as f:
                json.dump(draft_content, f, ensure_ascii=False, indent=4)
            
            # 导出draft_meta_info.json
            draft_meta = self._build_draft_meta_info()
            meta_path = os.path.join(output_dir, "draft_meta_info.json")
            with open(meta_path, 'w', encoding='utf-8') as f:
                json.dump(draft_meta, f, ensure_ascii=False, indent=4)
            
            return True

        except Exception as e:
            print(f"导出失败: {e}")
            return False

    def get_media_info(self, material_id: str):
        """获取媒体的基础信息"""
        return self.media_manager.get_media_info(material_id)

    def get_all_effect_type(self):
        """获取系统所有哪些特效/字体等"""
        from effect_validator import get_all_effect_type
        return get_all_effect_type()

    def get_effect_by_type(self, effect_type: str):
        """获取某个类型下的所有特效"""
        from effect_validator import get_effect_by_type
        return get_effect_by_type(effect_type)
    
    def _build_draft_content(self) -> Dict[str, Any]:
        """构建draft_content.json的内容"""
        # 计算总时长
        max_duration = 0
        for segment in self.video_manager.get_all_segments().values():
            end_time = segment.target_timerange.end
            if end_time > max_duration:
                max_duration = end_time
        for segment in self.audio_manager.get_all_segments().values():
            end_time = segment.target_timerange.end
            if end_time > max_duration:
                max_duration = end_time
        for segment in self.text_manager.get_all_segments().values():
            end_time = segment.target_timerange.end
            if end_time > max_duration:
                max_duration = end_time

        return {
            "canvas_config": {
                "width": self.draft_info.width,
                "height": self.draft_info.height,
                "ratio": "original"
            },
            "color_space": 0,
            "config": {
                "adjust_max_index": 1,
                "attachment_info": [],
                "combination_max_index": 1,
                "export_range": None,
                "extract_audio_last_index": 1,
                "lyrics_recognition_id": "",
                "lyrics_sync": True,
                "lyrics_taskinfo": [],
                "maintrack_adsorb": True,
                "material_save_mode": 0,
                "multi_language_current": "none",
                "multi_language_list": [],
                "multi_language_main": "none",
                "multi_language_mode": "none",
                "original_sound_last_index": 1,
                "record_audio_last_index": 1,
                "sticker_max_index": 1,
                "subtitle_keywords_config": None,
                "subtitle_recognition_id": "",
                "subtitle_sync": True,
                "subtitle_taskinfo": [],
                "system_font_list": [],
                "video_mute": False,
                "zoom_info_params": None
            },
            "cover": None,
            "create_time": 0,
            "duration": max_duration,
            "extra_info": None,
            "fps": self.draft_info.fps,
            "free_render_index_mode_on": False,
            "group_container": None,
            "id": self.draft_info.draft_id,
            "keyframe_graph_list": [],
            "keyframes": {
                "adjusts": [],
                "audios": [],
                "effects": [],
                "filters": [],
                "handwrites": [],
                "stickers": [],
                "texts": [],
                "videos": []
            },
            "last_modified_platform": {
                "app_id": 3704,
                "app_source": "lv",
                "app_version": "5.9.0",
                "os": "windows"
            },
            "platform": {
                "app_id": 3704,
                "app_source": "lv",
                "app_version": "5.9.0",
                "os": "windows"
            },
            "materials": self._build_materials(),
            "mutable_config": None,
            "name": self.draft_info.name,
            "new_version": "110.0.0",
            "relationships": [],
            "render_index_track_mode_on": False,
            "retouch_cover": None,
            "source": "default",
            "static_cover_image_path": "",
            "time_marks": None,
            "tracks": self._build_tracks(),
            "update_time": 0,
            "version": 360000
        }
    
    def _build_draft_meta_info(self) -> Dict[str, Any]:
        """构建draft_meta_info.json的内容"""
        return {
            "cloud_package_completed_time": "",
            "draft_cloud_capcut_purchase_info": "",
            "draft_cloud_last_action_download": False,
            "draft_cloud_materials": [],
            "draft_cloud_purchase_info": "",
            "draft_cloud_template_id": "",
            "draft_cloud_tutorial_info": "",
            "draft_cloud_videocut_purchase_info": "",
            "draft_cover": "",
            "draft_deeplink_url": "",
            "draft_enterprise_info": {
                "draft_enterprise_extra": "",
                "draft_enterprise_id": "",
                "draft_enterprise_name": "",
                "enterprise_material": []
            },
            "draft_fold_path": "",
            "draft_id": self.draft_info.draft_id,
            "draft_is_ai_packaging_used": False,
            "draft_is_ai_shorts": False,
            "draft_is_ai_translate": False,
            "draft_is_article_video_draft": False,
            "draft_is_from_deeplink": "false",
            "draft_is_invisible": False,
            "draft_materials": [
                {"type": 0, "value": []},
                {"type": 1, "value": []},
                {"type": 2, "value": []},
                {"type": 3, "value": []},
                {"type": 6, "value": []},
                {"type": 7, "value": []},
                {"type": 8, "value": []}
            ],
            "draft_materials_copied_info": [],
            "draft_name": self.draft_info.name,
            "draft_new_version": "",
            "draft_removable_storage_device": "",
            "draft_root_path": "",
            "draft_segment_extra_info": [],
            "draft_type": "",
            "tm_draft_cloud_completed": "",
            "tm_draft_cloud_modified": 0,
            "tm_draft_removed": 0,
            "tm_duration": 0
        }

    def _build_materials(self) -> Dict[str, Any]:
        """构建materials部分"""
        materials = {
            "ai_translates": [],
            "audio_balances": [],
            "audio_effects": [],
            "audio_fades": [],
            "audio_track_indexes": [],
            "audios": [],
            "beats": [],
            "canvases": [],
            "chromas": [],
            "color_curves": [],
            "digital_humans": [],
            "drafts": [],
            "effects": [],
            "flowers": [],
            "green_screens": [],
            "handwrites": [],
            "hsl": [],
            "images": [],
            "log_color_wheels": [],
            "loudnesses": [],
            "manual_deformations": [],
            "masks": [],
            "material_animations": [],
            "material_colors": [],
            "multi_language_refs": [],
            "placeholders": [],
            "plugin_effects": [],
            "primary_color_wheels": [],
            "realtime_denoises": [],
            "shapes": [],
            "smart_crops": [],
            "smart_relights": [],
            "sound_channel_mappings": [],
            "speeds": [],
            "stickers": [],
            "tail_leaders": [],
            "text_templates": [],
            "texts": [],
            "time_marks": [],
            "transitions": [],
            "video_effects": [],
            "video_trackings": [],
            "videos": [],
            "vocal_beautifys": [],
            "vocal_separations": []
        }

        # 添加音频素材
        for material in self.media_manager.get_all_materials().values():
            if material.material_type == "audio":
                materials["audios"].append({
                    "app_id": 0,
                    "category_id": "",
                    "category_name": "local",
                    "check_flag": 3,
                    "copyright_limit_type": "none",
                    "duration": material.duration,
                    "effect_id": "",
                    "formula_id": "",
                    "id": material.material_id,
                    "local_material_id": material.material_id,
                    "music_id": material.material_id,
                    "name": os.path.basename(material.path),
                    "path": material.path,
                    "source_platform": 0,
                    "type": "extract_music",
                    "wave_points": []
                })

        # 添加视频素材
        for material in self.media_manager.get_all_materials().values():
            if material.material_type == "video":
                materials["videos"].append({
                    "audio_fade": None,
                    "category_id": "",
                    "category_name": "local",
                    "check_flag": 63487,
                    "crop": {
                        "upper_left_x": 0.0,
                        "upper_left_y": 0.0,
                        "upper_right_x": 1.0,
                        "upper_right_y": 0.0,
                        "lower_left_x": 0.0,
                        "lower_left_y": 1.0,
                        "lower_right_x": 1.0,
                        "lower_right_y": 1.0
                    },
                    "crop_ratio": "free",
                    "crop_scale": 1.0,
                    "duration": material.duration,
                    "height": material.height,
                    "id": material.material_id,
                    "local_material_id": "",
                    "material_id": material.material_id,
                    "material_name": os.path.basename(material.path),
                    "media_path": "",
                    "path": material.path,
                    "type": "video",
                    "width": material.width
                })

        # 添加文本素材
        for segment in self.text_manager.get_all_segments().values():
            materials["texts"].append({
                "id": segment.segment_id,
                "content": f'{{"styles": [{{"fill": {{"alpha": 1.0, "content": {{"render_type": "solid", "solid": {{"alpha": 1.0, "color": [1.0, 1.0, 0.0]}}}}}}, "range": [0, {len(segment.text)}], "size": 8.0, "bold": false, "italic": false, "underline": false, "strokes": [], "font": {{"id": "7290445778273702455", "path": "C:/文轩体.ttf"}}, "effectStyle": {{"id": "7296357486490144036", "path": "C:"}}}}], "text": "{segment.text}"}}',
                "typesetting": 0,
                "alignment": 0,
                "letter_spacing": 0.0,
                "line_spacing": 0.02,
                "line_feed": 1,
                "line_max_width": 0.82,
                "force_apply_line_max_width": False,
                "check_flag": 7,
                "type": "text",
                "global_alpha": 1.0
            })

        # 添加音频淡入淡出素材
        self._add_audio_fades_materials(materials)

        # 添加动画素材
        self._add_animations_materials(materials)

        # 添加特效素材
        self._add_effects_materials(materials)

        # 添加转场素材
        self._add_transitions_materials(materials)

        # 添加画布素材
        self._add_canvases_materials(materials)

        # 添加速度素材
        self._add_speeds_materials(materials)

        return materials

    def _add_audio_fades_materials(self, materials):
        """添加音频淡入淡出素材"""
        # 收集所有音频片段的淡入淡出
        for segment in self.audio_manager.get_all_segments().values():
            for fade in segment.fade_effects:
                if fade.get("type") == "fade":
                    materials["audio_fades"].append({
                        "id": fade["id"],
                        "fade_in_duration": fade["fade_in_duration"],
                        "fade_out_duration": fade["fade_out_duration"],
                        "fade_type": 0,
                        "type": "audio_fade"
                    })

    def _add_animations_materials(self, materials):
        """添加动画素材"""
        # 收集所有视频片段的动画
        for segment in self.video_manager.get_all_segments().values():
            for animation in segment.animations:
                # 根据动画类型设置参数
                if animation["type"] == "斜切":
                    anim_id = "10696371"
                    resource_id = "7210657307938525751"
                    material_type = "video"
                    panel = "video"
                    anim_type = "in"
                else:
                    anim_id = "default_animation"
                    resource_id = "default_resource"
                    material_type = "video"
                    panel = "video"
                    anim_type = "in"

                materials["material_animations"].append({
                    "id": animation["id"],
                    "type": "sticker_animation",
                    "multi_language_current": "none",
                    "animations": [{
                        "anim_adjust_params": None,
                        "platform": "all",
                        "panel": panel,
                        "material_type": material_type,
                        "name": animation["type"],
                        "id": anim_id,
                        "type": anim_type,
                        "resource_id": resource_id,
                        "start": 0,
                        "duration": animation.get("duration", 700000)
                    }]
                })

        # 收集所有文本片段的动画
        for segment in self.text_manager.get_all_segments().values():
            for animation in segment.animations:
                # 根据动画类型设置参数
                if animation["type"] == "故障闪动":
                    anim_id = "15261509"
                    resource_id = "7244102414377161276"
                    material_type = "sticker"
                    panel = ""
                    anim_type = "out"
                    start_time = segment.target_timerange.start + segment.target_timerange.duration - animation.get("duration", 1000000)
                else:
                    anim_id = "default_text_animation"
                    resource_id = "default_resource"
                    material_type = "sticker"
                    panel = ""
                    anim_type = "in"
                    start_time = segment.target_timerange.start

                materials["material_animations"].append({
                    "id": animation["id"],
                    "type": "sticker_animation",
                    "multi_language_current": "none",
                    "animations": [{
                        "anim_adjust_params": None,
                        "platform": "all",
                        "panel": panel,
                        "material_type": material_type,
                        "name": animation["type"],
                        "id": anim_id,
                        "type": anim_type,
                        "resource_id": resource_id,
                        "start": start_time,
                        "duration": animation.get("duration", 1000000)
                    }]
                })

    def _add_effects_materials(self, materials):
        """添加特效素材"""
        # 收集所有文本片段的特效
        for segment in self.text_manager.get_all_segments().values():
            # 添加气泡特效
            for bubble in segment.bubbles:
                materials["effects"].append({
                    "apply_target_type": 0,
                    "effect_id": bubble["effect_id"],
                    "id": bubble["id"],
                    "resource_id": bubble["resource_id"],
                    "type": "text_shape",
                    "value": 1.0
                })

            # 添加花字特效
            for effect in segment.effects:
                materials["effects"].append({
                    "apply_target_type": 0,
                    "effect_id": effect["effect_id"],
                    "id": effect["id"],
                    "resource_id": effect["resource_id"],
                    "type": "text_effect",
                    "value": 1.0,
                    "source_platform": 1
                })

    def _add_transitions_materials(self, materials):
        """添加转场素材"""
        # 收集所有视频片段的转场
        for segment in self.video_manager.get_all_segments().values():
            for transition in segment.transitions:
                # 根据转场类型设置参数
                if transition["type"] == "信号故障":
                    effect_id = "25265947"
                    resource_id = "7288149307197231676"
                else:
                    effect_id = "default_transition"
                    resource_id = "default_resource"

                materials["transitions"].append({
                    "category_id": "",
                    "category_name": "",
                    "duration": transition.get("duration", 500000),
                    "effect_id": effect_id,
                    "id": transition["id"],
                    "is_overlap": True,
                    "name": transition["type"],
                    "platform": "all",
                    "resource_id": resource_id,
                    "type": "transition"
                })

    def _add_canvases_materials(self, materials):
        """添加画布素材"""
        # 检查是否有GIF片段需要背景填充
        for segment in self.video_manager.get_all_segments().values():
            material = self.media_manager.get_material(segment.material_id)
            if material and 'sticker.gif' in material.path:
                # 为GIF添加模糊背景填充
                materials["canvases"].append({
                    "id": generate_id(),
                    "type": "canvas_blur",
                    "blur": 0.0625,
                    "color": "#00000000",
                    "source_platform": 0
                })
                break  # 只需要一个画布

    def _add_speeds_materials(self, materials):
        """添加速度素材"""
        # 为每个视频片段添加速度控制
        for segment in self.video_manager.get_all_segments().values():
            materials["speeds"].append({
                "curve_speed": None,
                "id": generate_id(),
                "mode": 0,
                "speed": segment.speed,
                "type": "speed"
            })

        # 为文本片段添加速度控制
        for segment in self.text_manager.get_all_segments().values():
            materials["speeds"].append({
                "curve_speed": None,
                "id": generate_id(),
                "mode": 0,
                "speed": 1.0,
                "type": "speed"
            })

    def _build_tracks(self) -> List[Dict[str, Any]]:
        """构建tracks部分"""
        tracks = []

        for track in self.track_manager.get_all_tracks().values():
            track_data = {
                "attribute": 0,
                "flag": 0,
                "id": track.track_id,
                "is_default_name": False,
                "name": track.name,
                "segments": [],
                "type": track.track_type
            }

            # 添加该轨道的所有片段
            for segment_id in track.segments:
                if track.track_type == "audio":
                    segment = self.audio_manager.get_segment(segment_id)
                    if segment:
                        track_data["segments"].append(self._build_audio_segment_data(segment))
                elif track.track_type == "video":
                    segment = self.video_manager.get_segment(segment_id)
                    if segment:
                        track_data["segments"].append(self._build_video_segment_data(segment))
                elif track.track_type == "text":
                    segment = self.text_manager.get_segment(segment_id)
                    if segment:
                        track_data["segments"].append(self._build_text_segment_data(segment))

            tracks.append(track_data)

        return tracks

    def _build_audio_segment_data(self, segment) -> Dict[str, Any]:
        """构建音频片段数据"""
        # 构建extra_material_refs
        extra_refs = []

        # 添加速度控制引用 (每个片段都有一个速度控制)
        extra_refs.append(generate_id())  # 临时ID，实际会在speeds材料中生成

        # 添加淡入淡出效果引用
        for fade in segment.fade_effects:
            if fade.get("type") == "fade":
                extra_refs.append(fade["id"])

        # 添加音频特效引用
        for effect in segment.audio_effects:
            extra_refs.append(effect["id"])

        return {
            "enable_adjust": True,
            "enable_color_correct_adjust": False,
            "enable_color_curves": True,
            "enable_color_match_adjust": False,
            "enable_color_wheels": True,
            "enable_lut": True,
            "enable_smart_color_adjust": False,
            "last_nonzero_volume": 1.0,
            "reverse": False,
            "track_attribute": 0,
            "track_render_index": 0,
            "visible": True,
            "id": segment.segment_id,
            "material_id": segment.material_id,
            "target_timerange": {
                "start": segment.target_timerange.start,
                "duration": segment.target_timerange.duration
            },
            "common_keyframes": [],
            "keyframe_refs": [],
            "source_timerange": {
                "start": segment.source_timerange.start if segment.source_timerange else 0,
                "duration": segment.source_timerange.duration if segment.source_timerange else segment.target_timerange.duration
            },
            "speed": segment.speed,
            "volume": segment.volume,
            "extra_material_refs": extra_refs,
            "is_tone_modify": False,
            "clip": None,
            "hdr_settings": None,
            "render_index": 0
        }

    def _build_video_segment_data(self, segment) -> Dict[str, Any]:
        """构建视频片段数据"""
        # 构建extra_material_refs
        extra_refs = []

        # 添加速度控制引用 (每个片段都有一个速度控制)
        extra_refs.append(generate_id())  # 临时ID，实际会在speeds材料中生成

        # 添加动画引用
        for animation in segment.animations:
            extra_refs.append(animation["id"])

        # 添加转场引用
        for transition in segment.transitions:
            extra_refs.append(transition["id"])

        # 检查是否是GIF片段，如果是则添加canvas引用
        material = self.media_manager.get_material(segment.material_id)
        if material and 'sticker.gif' in material.path:
            extra_refs.append(generate_id())  # canvas引用

        # 添加特效引用
        for effect in segment.effects:
            extra_refs.append(effect["id"])

        return {
            "enable_adjust": True,
            "enable_color_correct_adjust": False,
            "enable_color_curves": True,
            "enable_color_match_adjust": False,
            "enable_color_wheels": True,
            "enable_lut": True,
            "enable_smart_color_adjust": False,
            "last_nonzero_volume": 1.0,
            "reverse": False,
            "track_attribute": 0,
            "track_render_index": 0,
            "visible": True,
            "id": segment.segment_id,
            "material_id": segment.material_id,
            "target_timerange": {
                "start": segment.target_timerange.start,
                "duration": segment.target_timerange.duration
            },
            "common_keyframes": [],
            "keyframe_refs": [],
            "source_timerange": {
                "start": segment.source_timerange.start if segment.source_timerange else 0,
                "duration": segment.source_timerange.duration if segment.source_timerange else segment.target_timerange.duration
            },
            "speed": segment.speed,
            "volume": segment.volume,
            "extra_material_refs": extra_refs,
            "is_tone_modify": False,
            "clip": {
                "alpha": 1.0,
                "flip": {
                    "horizontal": False,
                    "vertical": False
                },
                "rotation": 0.0,
                "scale": {
                    "x": 1.0,
                    "y": 1.0
                },
                "transform": {
                    "x": 0.0,
                    "y": 0.0
                }
            },
            "uniform_scale": {
                "on": True,
                "value": 1.0
            },
            "hdr_settings": {
                "intensity": 1.0,
                "mode": 1,
                "nits": 1000
            },
            "render_index": 0
        }

    def _build_text_segment_data(self, segment) -> Dict[str, Any]:
        """构建文本片段数据"""
        # 构建extra_material_refs
        extra_refs = []

        # 添加速度控制引用 (每个片段都有一个速度控制)
        extra_refs.append(generate_id())  # 临时ID，实际会在speeds材料中生成

        # 添加动画引用
        for animation in segment.animations:
            extra_refs.append(animation["id"])

        # 添加气泡引用
        for bubble in segment.bubbles:
            extra_refs.append(bubble["id"])

        # 添加特效引用
        for effect in segment.effects:
            extra_refs.append(effect["id"])

        return {
            "enable_adjust": True,
            "enable_color_correct_adjust": False,
            "enable_color_curves": True,
            "enable_color_match_adjust": False,
            "enable_color_wheels": True,
            "enable_lut": True,
            "enable_smart_color_adjust": False,
            "last_nonzero_volume": 1.0,
            "reverse": False,
            "track_attribute": 0,
            "track_render_index": 0,
            "visible": True,
            "id": segment.segment_id,
            "material_id": segment.segment_id,  # 文本片段的material_id就是segment_id
            "target_timerange": {
                "start": segment.target_timerange.start,
                "duration": segment.target_timerange.duration
            },
            "common_keyframes": [],
            "keyframe_refs": [],
            "source_timerange": None,
            "speed": 1.0,
            "volume": 1.0,
            "extra_material_refs": extra_refs,
            "is_tone_modify": False,
            "clip": {
                "alpha": 1.0,
                "flip": {
                    "horizontal": False,
                    "vertical": False
                },
                "rotation": 0.0,
                "scale": {
                    "x": 1.0,
                    "y": 1.0
                },
                "transform": {
                    "x": 0.0,
                    "y": -0.8
                }
            },
            "uniform_scale": {
                "on": True,
                "value": 1.0
            },
            "render_index": 15000
        }
