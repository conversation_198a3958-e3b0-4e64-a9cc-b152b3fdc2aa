"""
特效验证器 - 验证特效是否被支持
"""

# 支持的文本入场动画（免费）
SUPPORTED_TEXT_INTRO = {
    "冲屏位移", "卡拉OK", "变色输入", "右上弹入", "右下擦开", "向上擦除", "向上滑动", 
    "向上翻转", "向上重叠", "向上露出", "向下擦除", "向下滑动", "向下露出", "向下飞入",
    "向右擦除", "向右滑动", "向右缓入", "向右集合", "向右露出", "向左擦除", "向左滑动",
    "向左露出", "圆形扫描", "复古打字机", "居中打字", "左上弹入", "左移弹动", "开幕",
    "弹入", "弹弓", "弹性伸缩", "弹簧", "彩色映射", "打字机 I", "打字机 II", "打字机 III",
    "打字机IV", "扭曲模糊", "拖尾", "收拢", "放大", "故障打字机", "旋入", "日出", "晕开",
    "模糊", "水墨晕开", "水平翻转", "波浪弹入", "渐显", "溶解", "滑动上升", "生长",
    "甩出", "站起", "缩小", "缩小 II", "羽化向右擦开", "羽化向左擦开", "翻动", "轻微放大",
    "逐字旋转", "逐字显影", "逐字翻转", "闪动", "随机弹跳", "随机飞入"
}

# 支持的文本出场动画（免费）
SUPPORTED_TEXT_OUTRO = {
    "右上弹出", "右下擦除", "向上擦除", "向上溶解", "向上滑动", "向下擦除", "向下滑动",
    "向右擦除", "向右滑动", "向右缓出", "向左擦除", "向左滑动", "向左解散", "圆形扫描",
    "居中打字", "展开", "左上弹出", "左移弹动", "弹出", "弹弓", "弹性伸缩", "弹簧",
    "打字机 I", "打字机 II", "打字机 III", "扭曲模糊", "拖尾", "放大", "放大 II",
    "故障打字机", "旋出", "日落", "晕开", "模糊", "水墨晕开", "水平翻转", "波浪弹出",
    "渐隐", "溶解", "滑动下落", "生长", "缩小", "羽化向右擦除", "羽化向左擦除",
    "翻动", "躺下", "轻微放大", "闪动", "闭幕", "随机弹跳", "随机飞出"
}

# 支持的视频入场动画（免费）
SUPPORTED_VIDEO_INTRO = {
    "Kira游动", "上下抖动", "动感放大", "动感缩小", "向上滑动", "向上转入", "向上转入 II",
    "向下滑动", "向下甩入", "向右上甩入", "向右下甩入", "向右滑动", "向右甩入", "向右转入",
    "向左上甩入", "向左下甩入", "向左滑动", "向左转入", "左右抖动", "抖动下降", "折叠开幕",
    "放大", "斜切", "旋转", "旋转开幕", "渐显", "漩涡旋转", "缩小", "翻入", "跳转开幕",
    "轻微抖动", "轻微抖动 II", "轻微抖动 III", "轻微放大", "钟摆", "镜像翻转", "雨刷", "雨刷 II"
}

# 支持的转场（免费）
SUPPORTED_TRANSITIONS = {
    "3D空间", "上下翻页", "上移", "下移", "中心旋转", "云朵", "倒影", "冰雪结晶", "冲鸭",
    "分割", "分割 II", "分割 III", "分割 IV", "前后对比 II", "动漫云朵", "动漫漩涡",
    "动漫火焰", "动漫闪电", "压缩", "叠加", "叠化", "右移", "向上", "向上擦除", "向下",
    "向下擦除", "向下流动", "向右", "向右上", "向右下", "向右拉伸", "向右擦除", "向右流动",
    "向左", "向左上", "向左下", "向左拉伸", "向左擦除", "吸入", "回忆下滑", "圆形分割 II",
    "圆形扫描", "圆形遮罩", "圆形遮罩 II", "复古放映", "岁月的痕迹", "左下角 II", "左移",
    "开幕", "弹幕转场", "弹跳", "快门", "打板转场 I", "打板转场 II", "抖动", "抖动 II",
    "抠像旋转", "拉伸", "拉伸 II", "拉远", "拍摄器", "推近", "撕纸拉屏", "放射", "故障",
    "故障拼贴", "斜向分割", "星星", "星星 II", "模糊", "横向分割", "横向拉幕", "横向模糊",
    "横线", "气泡转场", "水波卷动", "水波向右", "水波向左", "泛光", "泛白", "波点向右",
    "渐变擦除", "滑动", "漩涡", "烟雾转场", "爱心", "爱心 II", "爱心上升", "电视故障 I",
    "电视故障 II", "画笔擦除", "白光快闪", "白色墨花", "白色烟雾", "百叶窗", "眨眼",
    "矩形分割", "窗格", "立方体", "竖向分割", "竖向拉幕", "竖向模糊", "竖向模糊 II",
    "竖线", "箭头向右", "粒子", "翻篇", "翻页", "色差逆时针", "色差顺时针", "色彩溶解",
    "色彩溶解 II", "色彩溶解 III", "蓝色线条", "逆时针旋转", "逆时针旋转 II", "镜像翻转",
    "闪白", "闪白 II", "闪黑", "雪花故障", "雾化", "震动", "顺时针旋转", "顺时针旋转 II",
    "频闪", "风车", "马赛克", "黑色块", "黑色烟雾"
}

# 付费转场（已知支持的）
SUPPORTED_PAID_TRANSITIONS = {
    "信号故障"  # 这个在demo.py中确认可用
}

# 所有支持的转场
ALL_SUPPORTED_TRANSITIONS = SUPPORTED_TRANSITIONS | SUPPORTED_PAID_TRANSITIONS


def validate_text_animation(animation_type: str, is_intro: bool = True) -> tuple[bool, str]:
    """验证文本动画是否支持
    
    Args:
        animation_type: 动画类型名称
        is_intro: 是否为入场动画
        
    Returns:
        (是否支持, 提示信息)
    """
    supported_set = SUPPORTED_TEXT_INTRO if is_intro else SUPPORTED_TEXT_OUTRO
    animation_name = "入场" if is_intro else "出场"
    
    if animation_type in supported_set:
        return True, f"✅ 文本{animation_name}动画 '{animation_type}' 支持"
    else:
        # 提供建议的替代动画
        suggestions = list(supported_set)[:5]  # 前5个作为建议
        return False, f"❌ 文本{animation_name}动画 '{animation_type}' 不支持，建议使用: {', '.join(suggestions)}"


def validate_video_animation(animation_type: str) -> tuple[bool, str]:
    """验证视频动画是否支持
    
    Args:
        animation_type: 动画类型名称
        
    Returns:
        (是否支持, 提示信息)
    """
    if animation_type in SUPPORTED_VIDEO_INTRO:
        return True, f"✅ 视频入场动画 '{animation_type}' 支持"
    else:
        # 提供建议的替代动画
        suggestions = ["斜切", "放大", "渐显", "旋转", "轻微放大"]
        return False, f"❌ 视频入场动画 '{animation_type}' 不支持，建议使用: {', '.join(suggestions)}"


def validate_transition(transition_type: str) -> tuple[bool, str]:
    """验证转场是否支持
    
    Args:
        transition_type: 转场类型名称
        
    Returns:
        (是否支持, 提示信息)
    """
    if transition_type in ALL_SUPPORTED_TRANSITIONS:
        return True, f"✅ 转场 '{transition_type}' 支持"
    else:
        # 提供建议的替代转场
        suggestions = ["信号故障", "叠化", "模糊", "闪白", "推近"]
        return False, f"❌ 转场 '{transition_type}' 不支持，建议使用: {', '.join(suggestions)}"


def get_recommended_effects():
    """获取推荐的特效组合"""
    return {
        "text_intro": ["复古打字机", "弹入", "渐显", "放大", "向右滑动"],
        "text_outro": ["渐隐", "缩小", "向左滑动", "弹出", "闪动"],
        "video_intro": ["斜切", "放大", "渐显", "旋转", "轻微放大"],
        "transitions": ["信号故障", "叠化", "模糊", "闪白", "推近"]
    }


def get_all_effect_type():
    """获取系统所有哪些特效/字体等

    Returns:
        dict: 包含所有特效类型的字典
    """
    return {
        "text_intro": list(SUPPORTED_TEXT_INTRO),
        "text_outro": list(SUPPORTED_TEXT_OUTRO),
        "video_intro": list(SUPPORTED_VIDEO_INTRO),
        "transitions": list(ALL_SUPPORTED_TRANSITIONS),
        "total_counts": {
            "text_intro": len(SUPPORTED_TEXT_INTRO),
            "text_outro": len(SUPPORTED_TEXT_OUTRO),
            "video_intro": len(SUPPORTED_VIDEO_INTRO),
            "transitions": len(ALL_SUPPORTED_TRANSITIONS)
        }
    }


def get_effect_by_type(effect_type: str):
    """获取某个类型下的所有特效

    Args:
        effect_type: 特效类型 ("text_intro", "text_outro", "video_intro", "transitions")

    Returns:
        list: 该类型下的所有特效列表
    """
    effect_map = {
        "text_intro": list(SUPPORTED_TEXT_INTRO),
        "text_outro": list(SUPPORTED_TEXT_OUTRO),
        "video_intro": list(SUPPORTED_VIDEO_INTRO),
        "transitions": list(ALL_SUPPORTED_TRANSITIONS)
    }

    return effect_map.get(effect_type, [])


def validate_all_effects(text_intro=None, text_outro=None, video_intro=None, transition=None):
    """验证所有特效
    
    Args:
        text_intro: 文本入场动画
        text_outro: 文本出场动画  
        video_intro: 视频入场动画
        transition: 转场
        
    Returns:
        dict: 验证结果
    """
    results = {
        "all_valid": True,
        "messages": [],
        "suggestions": []
    }
    
    if text_intro:
        valid, msg = validate_text_animation(text_intro, True)
        results["messages"].append(msg)
        if not valid:
            results["all_valid"] = False
    
    if text_outro:
        valid, msg = validate_text_animation(text_outro, False)
        results["messages"].append(msg)
        if not valid:
            results["all_valid"] = False
    
    if video_intro:
        valid, msg = validate_video_animation(video_intro)
        results["messages"].append(msg)
        if not valid:
            results["all_valid"] = False
    
    if transition:
        valid, msg = validate_transition(transition)
        results["messages"].append(msg)
        if not valid:
            results["all_valid"] = False
    
    if not results["all_valid"]:
        recommended = get_recommended_effects()
        results["suggestions"] = [
            "💡 推荐的特效组合:",
            f"   文本入场: {', '.join(recommended['text_intro'])}",
            f"   文本出场: {', '.join(recommended['text_outro'])}",
            f"   视频入场: {', '.join(recommended['video_intro'])}",
            f"   转场: {', '.join(recommended['transitions'])}"
        ]
    
    return results


if __name__ == "__main__":
    # 测试验证器
    print("🧪 特效验证器测试")
    print("=" * 50)
    
    # 测试一些特效
    test_cases = [
        ("复古打字机", "text_intro"),
        ("故障闪动", "text_outro"),
        ("斜切", "video_intro"),
        ("信号故障", "transition"),
        ("不存在的动画", "text_intro"),
        ("不存在的转场", "transition")
    ]
    
    for effect_name, effect_type in test_cases:
        if effect_type == "text_intro":
            valid, msg = validate_text_animation(effect_name, True)
        elif effect_type == "text_outro":
            valid, msg = validate_text_animation(effect_name, False)
        elif effect_type == "video_intro":
            valid, msg = validate_video_animation(effect_name)
        elif effect_type == "transition":
            valid, msg = validate_transition(effect_name)
        
        print(msg)
    
    print(f"\n💡 推荐特效:")
    recommended = get_recommended_effects()
    for category, effects in recommended.items():
        print(f"   {category}: {', '.join(effects)}")
