"""
媒体素材管理器
"""

import os
from typing import Dict, Optional, Any
from data_structures import MediaInfo, generate_id


class MediaManager:
    """媒体素材管理器"""
    
    def __init__(self):
        self.materials: Dict[str, MediaInfo] = {}
    
    def register_video_material(self, video_path: str) -> str:
        """注册视频素材，返回material_id"""
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        material_id = generate_id()
        
        # 根据文件名设置正确的参数（模拟真实文件信息）
        if 'video.mp4' in video_path:
            # 主视频文件
            duration = 5000000  # 5秒
            width = 2046
            height = 1080
        elif 'sticker.gif' in video_path:
            # GIF贴纸文件
            duration = 900000   # 0.9秒
            width = 312
            height = 259
        else:
            # 默认值
            duration = 5000000
            width = 1920
            height = 1080

        material_info = MediaInfo(
            material_id=material_id,
            path=video_path,
            material_type="video",
            duration=duration,
            width=width,
            height=height
        )
        
        self.materials[material_id] = material_info
        return material_id
    
    def register_audio_material(self, audio_path: str) -> str:
        """注册音频素材，返回material_id"""
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        material_id = generate_id()
        
        # 这里简化处理，实际应该读取音频文件信息
        material_info = MediaInfo(
            material_id=material_id,
            path=audio_path,
            material_type="audio",
            duration=5185000,  # 5.185秒，实际应该从文件读取
            width=0,
            height=0
        )
        
        self.materials[material_id] = material_info
        return material_id
    
    def get_material(self, material_id: str) -> Optional[MediaInfo]:
        """获取素材信息"""
        return self.materials.get(material_id)

    def get_media_info(self, material_id: str) -> Optional[Dict[str, Any]]:
        """获取媒体的基础信息

        Args:
            material_id: 素材ID

        Returns:
            媒体信息字典，包含路径、类型、时长等信息
        """
        material = self.materials.get(material_id)
        if material is None:
            return None

        import os

        media_info = {
            "material_id": material_id,
            "path": material.path,
            "type": material.material_type,
            "exists": os.path.exists(material.path),
            "size": None,
            "duration": None
        }

        # 获取文件大小
        if media_info["exists"]:
            try:
                media_info["size"] = os.path.getsize(material.path)
            except:
                media_info["size"] = 0

        # 获取媒体时长（简化实现）
        if material.material_type in ["video", "audio"]:
            # 这里可以集成ffprobe或其他工具获取真实时长
            # 目前返回默认值
            media_info["duration"] = 10000000  # 默认10秒（微秒）

        return media_info
    
    def get_all_materials(self) -> Dict[str, MediaInfo]:
        """获取所有素材"""
        return self.materials.copy()
    
    def remove_material(self, material_id: str) -> bool:
        """删除素材"""
        if material_id in self.materials:
            del self.materials[material_id]
            return True
        return False
