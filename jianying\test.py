# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/8/7 下午11:04
File Name:test.py
"""
from jianying import DraftManager, trange

# 1. 创建草稿管理器
draft = DraftManager()

# 2. 创建草稿项目
draft_id = draft.create_draft(1920, 1080, 30, "我的视频项目")

# 3. 创建轨道
video_track = draft.track_manager.create_track("video", "主视频轨道")
audio_track = draft.track_manager.create_track("audio", "主音频轨道")
text_track = draft.track_manager.create_track("text", "字幕轨道")

# 4. 注册素材
video_material = draft.media_manager.register_video_material(r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4")
audio_material = draft.media_manager.register_audio_material(r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\audio.mp3")

# 5. 创建片段并添加到轨道
video_id = draft.video_manager.create_video_segment(
    material_id=video_material,
    target_timerange=trange(0, 10.0),  # 0-10秒
    track_id=video_track
)
draft.track_manager.add_segment_to_track(video_track, video_id)
audio_id = draft.audio_manager.create_audio_segment(
    material_id=audio_material,
    target_timerange=trange(0, 10.0),  # 0-10秒
    track_id=audio_track
)
draft.track_manager.add_segment_to_track(audio_track, audio_id)


# 6. 导出项目
draft.export_draft("./output")