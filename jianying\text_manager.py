"""
文本片段管理器
"""

from typing import Dict, Optional, List, Any
from data_structures import TextSegmentInfo, TimeRange, generate_id, TextStyle, TextBorder, TextBackground, ClipSettings
from effect_validator import validate_text_animation


class TextManager:
    """文本片段管理器"""
    
    def __init__(self):
        self.segments: Dict[str, TextSegmentInfo] = {}
    
    def create_text_segment(self, text: str, target_timerange: TimeRange, track_id: str,
                          font: Optional[Dict[str, Any]] = None,
                          style: Optional[TextStyle] = None,
                          clip_settings: Optional[ClipSettings] = None,
                          border: Optional[TextBorder] = None,
                          background: Optional[TextBackground] = None) -> str:
        """创建文本片段，返回text_id

        Args:
            text: 文本内容
            target_timerange: 目标时间范围
            track_id: 轨道ID
            font: 字体信息
            style: 文本样式设置
            clip_settings: 图像调节设置
            border: 文本描边设置
            background: 文本背景设置

        Returns:
            text_id: 文本片段ID
        """
        segment_id = generate_id()
        
        segment_info = TextSegmentInfo(
            segment_id=segment_id,
            track_id=track_id,
            text=text,
            target_timerange=target_timerange,
            font=font,
            style=style,
            clip_settings=clip_settings,
            border=border,
            background=background
        )
        
        self.segments[segment_id] = segment_info
        return segment_id
    
    def get_segment(self, text_id: str) -> Optional[TextSegmentInfo]:
        """获取文本片段信息"""
        return self.segments.get(text_id)
    
    def add_animation(self, text_id: str, animation_type: str, duration: Optional[int] = None, is_intro: bool = True) -> bool:
        """给文本添加动画

        Args:
            text_id: 文本片段ID
            animation_type: 动画类型
            duration: 动画持续时间（微秒）
            is_intro: 是否为入场动画

        Returns:
            是否添加成功
        """
        segment = self.segments.get(text_id)
        if segment is None:
            return False

        # 验证动画类型是否支持
        valid, msg = validate_text_animation(animation_type, is_intro)
        if not valid:
            print(f"⚠️  {msg}")
            return False

        # 设置默认时长
        if duration is None:
            if animation_type in ["复古打字机", "打字机 I", "打字机 II", "打字机 III"]:
                duration = 1000000  # 1秒
            else:
                duration = 800000   # 默认0.8秒

        animation = {
            "type": animation_type,
            "duration": duration,
            "id": generate_id()
        }

        segment.animations.append(animation)
        print(f"✅ {msg}")
        return True
    
    def add_effect(self, text_id: str, effect_id: str) -> bool:
        """给文本添加花字效果
        
        Args:
            text_id: 文本片段ID
            effect_id: 花字效果ID
            
        Returns:
            是否添加成功
        """
        segment = self.segments.get(text_id)
        if segment is None:
            return False
        
        effect = {
            "effect_id": effect_id,
            "resource_id": effect_id,  # 花字效果的effect_id和resource_id相同
            "id": generate_id()
        }
        
        segment.effects.append(effect)
        return True
    
    def add_bubble(self, text_id: str, effect_id: str, resource_id: str) -> bool:
        """给文本添加气泡效果
        
        Args:
            text_id: 文本片段ID
            effect_id: 气泡效果ID
            resource_id: 资源ID
            
        Returns:
            是否添加成功
        """
        segment = self.segments.get(text_id)
        if segment is None:
            return False
        
        bubble = {
            "effect_id": effect_id,
            "resource_id": resource_id,
            "id": generate_id()
        }
        
        segment.bubbles.append(bubble)
        return True
    
    def update_text(self, text_id: str, new_text: str) -> bool:
        """更新文本内容
        
        Args:
            text_id: 文本片段ID
            new_text: 新的文本内容
            
        Returns:
            是否更新成功
        """
        segment = self.segments.get(text_id)
        if segment is None:
            return False
        
        segment.text = new_text
        return True
    
    def update_style(self, text_id: str, style: Dict[str, Any]) -> bool:
        """更新文本样式
        
        Args:
            text_id: 文本片段ID
            style: 新的样式信息
            
        Returns:
            是否更新成功
        """
        segment = self.segments.get(text_id)
        if segment is None:
            return False
        
        segment.style = style
        return True
    
    def update_font(self, text_id: str, font: Dict[str, Any]) -> bool:
        """更新文本字体
        
        Args:
            text_id: 文本片段ID
            font: 新的字体信息
            
        Returns:
            是否更新成功
        """
        segment = self.segments.get(text_id)
        if segment is None:
            return False
        
        segment.font = font
        return True
    
    def get_all_segments(self) -> Dict[str, TextSegmentInfo]:
        """获取所有文本片段"""
        return self.segments.copy()
    
    def remove_segment(self, text_id: str) -> bool:
        """删除文本片段"""
        if text_id in self.segments:
            del self.segments[text_id]
            return True
        return False
