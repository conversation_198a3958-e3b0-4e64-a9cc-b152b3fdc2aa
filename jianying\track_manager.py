"""
轨道管理器
"""

from typing import Dict, Optional, List
from data_structures import TrackInfo, generate_id


class TrackManager:
    """轨道管理器"""
    
    def __init__(self):
        self.tracks: Dict[str, TrackInfo] = {}
    
    def create_track(self, track_type: str, name: Optional[str] = None, mute: bool = False) -> str:
        """创建轨道，返回track_id
        
        Args:
            track_type: 轨道类型 ("audio", "video", "text")
            name: 轨道名称，如果不指定则使用默认名称
            mute: 是否静音
            
        Returns:
            track_id: 轨道ID
        """
        if track_type not in ["audio", "video", "text"]:
            raise ValueError(f"不支持的轨道类型: {track_type}")
        
        track_id = generate_id()
        
        if name is None:
            name = track_type
        
        track_info = TrackInfo(
            track_id=track_id,
            track_type=track_type,
            name=name,
            mute=mute
        )
        
        self.tracks[track_id] = track_info
        return track_id
    
    def get_track(self, track_id: str) -> Optional[TrackInfo]:
        """获取轨道信息"""
        return self.tracks.get(track_id)
    
    def add_segment_to_track(self, track_id: str, segment_id: str) -> bool:
        """将片段添加到轨道"""
        track = self.tracks.get(track_id)
        if track is None:
            return False
        
        if segment_id not in track.segments:
            track.segments.append(segment_id)
        return True
    
    def remove_segment_from_track(self, track_id: str, segment_id: str) -> bool:
        """从轨道中移除片段"""
        track = self.tracks.get(track_id)
        if track is None:
            return False
        
        if segment_id in track.segments:
            track.segments.remove(segment_id)
            return True
        return False
    
    def get_tracks_by_type(self, track_type: str) -> List[TrackInfo]:
        """根据类型获取轨道列表"""
        return [track for track in self.tracks.values() if track.track_type == track_type]
    
    def get_all_tracks(self) -> Dict[str, TrackInfo]:
        """获取所有轨道"""
        return self.tracks.copy()
    
    def remove_track(self, track_id: str) -> bool:
        """删除轨道"""
        if track_id in self.tracks:
            del self.tracks[track_id]
            return True
        return False
