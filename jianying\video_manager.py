"""
视频片段管理器
"""

from typing import Dict, Optional, List, Any, Union
from data_structures import VideoSegmentInfo, TimeRange, generate_id, ClipSettings
from effect_validator import validate_video_animation, validate_transition


class VideoManager:
    """视频片段管理器"""
    
    def __init__(self):
        self.segments: Dict[str, VideoSegmentInfo] = {}
    
    def create_video_segment(self, material_id: str, target_timerange: TimeRange, track_id: str,
                           source_timerange: Optional[TimeRange] = None, speed: float = 1.0,
                           volume: float = 1.0, change_pitch: bool = False,
                           clip_settings: Optional['ClipSettings'] = None) -> str:
        """创建视频片段，返回video_id

        Args:
            material_id: 素材ID
            target_timerange: 目标时间范围
            track_id: 轨道ID
            source_timerange: 源时间范围
            speed: 播放速度
            volume: 音量
            change_pitch: 是否变调
            clip_settings: 图像调节设置

        Returns:
            video_id: 视频片段ID
        """
        segment_id = generate_id()
        
        segment_info = VideoSegmentInfo(
            segment_id=segment_id,
            material_id=material_id,
            track_id=track_id,
            target_timerange=target_timerange,
            source_timerange=source_timerange,
            speed=speed,
            volume=volume,
            change_pitch=change_pitch,
            clip_settings=clip_settings
        )
        
        self.segments[segment_id] = segment_info
        return segment_id
    
    def get_segment(self, video_id: str) -> Optional[VideoSegmentInfo]:
        """获取视频片段信息"""
        return self.segments.get(video_id)
    
    def add_animation(self, video_id: str, animation_type: str, duration: Optional[int] = None) -> bool:
        """给视频添加动画

        Args:
            video_id: 视频片段ID
            animation_type: 动画类型
            duration: 动画持续时间（微秒）

        Returns:
            是否添加成功
        """
        segment = self.segments.get(video_id)
        if segment is None:
            return False

        # 验证动画类型是否支持
        valid, msg = validate_video_animation(animation_type)
        if not valid:
            print(f"⚠️  {msg}")
            return False

        # 设置默认时长
        if duration is None:
            if animation_type == "斜切":
                duration = 700000  # 0.7秒
            else:
                duration = 1000000  # 默认1秒

        animation = {
            "type": animation_type,
            "duration": duration,
            "id": generate_id()
        }

        segment.animations.append(animation)
        print(f"✅ {msg}")
        return True
    
    def add_effect(self, video_id: str, effect_type: str, params: Optional[List[Optional[float]]] = None) -> bool:
        """给视频添加特效

        Args:
            video_id: 视频片段ID
            effect_type: 特效类型
            params: 特效参数列表，范围0-100，None表示使用默认值

        Returns:
            是否添加成功
        """
        segment = self.segments.get(video_id)
        if segment is None:
            return False
        
        effect = {
            "type": effect_type,
            "params": params or [],
            "id": generate_id()
        }
        
        segment.effects.append(effect)
        return True
    
    def add_filter(self, video_id: str, filter_type: str, intensity: float = 100.0) -> bool:
        """给视频添加滤镜
        
        Args:
            video_id: 视频片段ID
            filter_type: 滤镜类型
            intensity: 滤镜强度 (0-100)
            
        Returns:
            是否添加成功
        """
        segment = self.segments.get(video_id)
        if segment is None:
            return False
        
        filter_effect = {
            "type": filter_type,
            "intensity": intensity / 100.0,  # 转换为0-1范围
            "id": generate_id()
        }
        
        segment.filters.append(filter_effect)
        return True
    
    def add_transition(self, video_id: str, transition_type: str, duration: Optional[int] = None) -> bool:
        """给视频添加转场

        Args:
            video_id: 视频片段ID
            transition_type: 转场类型
            duration: 转场持续时间（微秒）

        Returns:
            是否添加成功
        """
        segment = self.segments.get(video_id)
        if segment is None:
            return False

        # 验证转场类型是否支持
        valid, msg = validate_transition(transition_type)
        if not valid:
            print(f"⚠️  {msg}")
            return False

        # 检查是否已有转场，如果有就清空重新添加
        if segment.transitions:
            segment.transitions.clear()  # 清空已有转场，允许重新添加

        # 设置默认时长
        if duration is None:
            duration = 500000  # 默认0.5秒

        transition = {
            "type": transition_type,
            "duration": duration,
            "id": generate_id()
        }

        segment.transitions.append(transition)
        print(f"✅ {msg}")
        return True
    
    def add_mask(self, video_id: str, mask_type: str, center_x: float = 0.0,
                 center_y: float = 0.0, size: float = 0.5, rotation: float = 0.0,
                 feather: float = 0.0, invert: bool = False,
                 rect_width: Optional[float] = None, round_corner: Optional[float] = None) -> bool:
        """给视频添加蒙版

        Args:
            video_id: 视频片段ID
            mask_type: 蒙版类型
            center_x: 中心X坐标 (0.0-1.0)
            center_y: 中心Y坐标 (0.0-1.0)
            size: 大小 (0.0-1.0)
            rotation: 旋转角度 (度)
            feather: 羽化程度 (0.0-100.0)
            invert: 是否反转蒙版
            rect_width: 矩形蒙版宽度 (仅矩形蒙版有效)
            round_corner: 圆角程度 (0.0-100.0, 仅矩形蒙版有效)

        Returns:
            是否添加成功
        """
        segment = self.segments.get(video_id)
        if segment is None:
            return False

        # 构建蒙版参数
        mask_params = {
            "center_x": center_x,
            "center_y": center_y,
            "size": size,
            "rotation": rotation,
            "feather": feather / 100.0,  # 转换为0-1范围
            "invert": invert
        }

        # 矩形蒙版特有参数
        if mask_type.lower() in ["rectangle", "rect", "矩形"]:
            if rect_width is not None:
                mask_params["rect_width"] = rect_width
            if round_corner is not None:
                mask_params["round_corner"] = round_corner / 100.0  # 转换为0-1范围

        mask = {
            "type": mask_type,
            "params": mask_params,
            "id": generate_id()
        }

        segment.masks.append(mask)
        return True

    def add_background_filling(self, video_id: str, fill_type: str = "blur", blur: float = 0.0625, color: str = "#00000000") -> bool:
        """给视频添加背景填充

        Args:
            video_id: 视频片段ID
            fill_type: 填充类型 ("blur" 或 "color")
            blur: 模糊程度 (0.0-1.0)
            color: 填充颜色 (十六进制格式)

        Returns:
            是否添加成功
        """
        segment = self.segments.get(video_id)
        if segment is None:
            return False

        # 背景填充只对底层视频轨道有效
        background_filling = {
            "type": fill_type,
            "blur": blur,
            "color": color,
            "id": generate_id()
        }

        # 添加到片段的背景填充列表（如果没有这个属性，需要在data_structures中添加）
        if not hasattr(segment, 'background_fillings'):
            segment.background_fillings = []

        segment.background_fillings.append(background_filling)
        return True
    
    def get_all_segments(self) -> Dict[str, VideoSegmentInfo]:
        """获取所有视频片段"""
        return self.segments.copy()
    
    def remove_segment(self, video_id: str) -> bool:
        """删除视频片段"""
        if video_id in self.segments:
            del self.segments[video_id]
            return True
        return False
