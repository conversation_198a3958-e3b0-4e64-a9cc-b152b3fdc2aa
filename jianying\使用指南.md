# 剪映草稿构建系统 - 完整使用指南

## 🚀 快速开始

### 安装和导入

```python
from draft_manager import DraftManager
from data_structures import trange, tim
```

### 基础工作流程

```python
# 1. 创建草稿管理器
draft = DraftManager()

# 2. 创建草稿项目
draft_id = draft.create_draft(1920, 1080, 30, "我的视频项目")

# 3. 创建轨道
video_track = draft.track_manager.create_track("video", "主视频轨道")
audio_track = draft.track_manager.create_track("audio", "主音频轨道") 
text_track = draft.track_manager.create_track("text", "字幕轨道")

# 4. 注册素材
video_material = draft.media_manager.register_video_material("path/to/video.mp4")
audio_material = draft.media_manager.register_audio_material("path/to/audio.mp3")

# 5. 创建片段并添加到轨道
video_id = draft.video_manager.create_video_segment(
    material_id=video_material,
    target_timerange=trange(0, 10.0),  # 0-10秒
    track_id=video_track
)
draft.track_manager.add_segment_to_track(video_track, video_id)

# 6. 导出项目
draft.export_draft("./output")
```

## 📊 时间和范围工具

### 时间转换函数

```python
# tim() - 将时间转换为微秒
tim(1.5)        # 1.5秒 = 1500000微秒
tim("2.5s")     # 2.5秒
tim("500ms")    # 500毫秒

# trange() - 创建时间范围
trange(0, 5.0)           # 从0秒开始，持续5秒
trange(2.5, 3.0)         # 从2.5秒开始，持续3秒
trange(tim(1.0), tim(2.0))  # 使用微秒值
```

## 🎬 视频片段操作

### 创建视频片段

```python
# 基础创建
video_id = draft.video_manager.create_video_segment(
    material_id=video_material,
    target_timerange=trange(0, 5.0),
    track_id=video_track,
    speed=1.0,      # 播放速度
    volume=0.8      # 音量
)

# 指定源时间范围（裁剪）
video_id = draft.video_manager.create_video_segment(
    material_id=video_material,
    target_timerange=trange(0, 3.0),      # 在轨道上占3秒
    source_timerange=trange(10.0, 3.0),   # 从素材第10秒开始取3秒
    track_id=video_track
)
```

### 视频特效

```python
# 添加入场动画
draft.video_manager.add_animation(video_id, "斜切", tim(1.0))
draft.video_manager.add_animation(video_id, "放大", tim(1.5))
draft.video_manager.add_animation(video_id, "渐显", tim(0.8))

# 添加转场效果
draft.video_manager.add_transition(video_id, "信号故障", tim(0.5))
draft.video_manager.add_transition(video_id, "叠化", tim(0.8))

# 添加滤镜
draft.video_manager.add_filter(video_id, "复古", intensity=80.0)
draft.video_manager.add_filter(video_id, "黑白", intensity=100.0)

# 添加特效
draft.video_manager.add_effect(video_id, "光晕效果")
draft.video_manager.add_effect(video_id, "粒子效果")

# 添加蒙版
draft.video_manager.add_mask(video_id, "圆形", center_x=0.5, center_y=0.5, size=0.8)
draft.video_manager.add_mask(video_id, "矩形", center_x=0.0, center_y=0.0, size=0.6)

# 添加背景填充（仅底层轨道有效）
draft.video_manager.add_background_filling(video_id, "blur", blur=0.5)
draft.video_manager.add_background_filling(video_id, "color", color="#FF0000")
```

## 🎵 音频片段操作

### 创建音频片段

```python
# 基础创建
audio_id = draft.audio_manager.create_audio_segment(
    material_id=audio_material,
    target_timerange=trange(0, 10.0),
    track_id=audio_track,
    volume=0.7      # 音量 0.0-1.0
)
```

### 音频特效

```python
# 添加淡入淡出
draft.audio_manager.add_fade(audio_id, tim(2.0), tim(1.5))  # 2秒淡入，1.5秒淡出
draft.audio_manager.add_fade(audio_id, tim(1.0), tim(0))    # 只有淡入

# 添加音频特效
draft.audio_manager.add_effect(audio_id, "回声")
draft.audio_manager.add_effect(audio_id, "混响")

# 设置音量
draft.audio_manager.set_volume(audio_id, 0.8)

# 添加音量关键帧
draft.audio_manager.add_keyframe(audio_id, tim(2.0), 0.5)   # 2秒处音量为0.5
draft.audio_manager.add_keyframe(audio_id, tim(5.0), 1.0)   # 5秒处音量为1.0
```

## 📝 文本片段操作

### 创建文本片段

```python
# 基础创建
text_id = draft.text_manager.create_text_segment(
    text="Hello World",
    target_timerange=trange(1.0, 3.0),
    track_id=text_track
)

# 带样式创建
text_id = draft.text_manager.create_text_segment(
    text="标题文字",
    target_timerange=trange(0, 2.0),
    track_id=text_track,
    font_size=48,
    color="#FFFFFF",
    alignment="center"
)
```

### 文本特效

```python
# 添加入场动画
draft.text_manager.add_animation(text_id, "复古打字机", tim(1.0), is_intro=True)
draft.text_manager.add_animation(text_id, "弹入", tim(0.8), is_intro=True)

# 添加出场动画
draft.text_manager.add_animation(text_id, "渐隐", tim(0.5), is_intro=False)
draft.text_manager.add_animation(text_id, "缩小", tim(0.8), is_intro=False)

# 添加花字效果
draft.text_manager.add_effect(text_id, "7296357486490144036")

# 添加气泡效果
draft.text_manager.add_bubble(text_id, "361595", "6742029398926430728")
```

## 🛤️ 轨道管理

### 创建轨道

```python
# 创建不同类型的轨道
video_track1 = draft.track_manager.create_track("video", "主视频")
video_track2 = draft.track_manager.create_track("video", "画中画")
audio_track1 = draft.track_manager.create_track("audio", "背景音乐")
audio_track2 = draft.track_manager.create_track("audio", "音效")
text_track = draft.track_manager.create_track("text", "字幕")
```

### 管理片段

```python
# 添加片段到轨道
draft.track_manager.add_segment_to_track(video_track1, video_id)
draft.track_manager.add_segment_to_track(audio_track1, audio_id)
draft.track_manager.add_segment_to_track(text_track, text_id)

# 获取轨道信息
track_info = draft.track_manager.get_track(video_track1)
print(f"轨道类型: {track_info.track_type}")
print(f"轨道名称: {track_info.name}")

# 获取轨道中的所有片段
segments = draft.track_manager.get_track_segments(video_track1)
print(f"轨道中有 {len(segments)} 个片段")
```

## 📁 素材管理

### 注册素材

```python
# 注册视频素材
video_id = draft.media_manager.register_video_material("video.mp4")
gif_id = draft.media_manager.register_video_material("sticker.gif")

# 注册音频素材
audio_id = draft.media_manager.register_audio_material("music.mp3")
sound_id = draft.media_manager.register_audio_material("sound.wav")

# 获取素材信息
material = draft.media_manager.get_material(video_id)
print(f"素材路径: {material.path}")
print(f"素材类型: {material.material_type}")
```

### 查询媒体信息

```python
# 获取详细媒体信息
media_info = draft.get_media_info(video_id)
print(f"文件路径: {media_info['path']}")
print(f"文件类型: {media_info['type']}")
print(f"文件存在: {media_info['exists']}")
print(f"文件大小: {media_info['size']} 字节")
print(f"媒体时长: {media_info['duration']} 微秒")
```

## 🎨 特效查询系统

### 查询所有特效类型

```python
# 获取所有特效统计
all_effects = draft.get_all_effect_type()
print(f"文本入场动画: {all_effects['total_counts']['text_intro']}个")
print(f"文本出场动画: {all_effects['total_counts']['text_outro']}个")
print(f"视频入场动画: {all_effects['total_counts']['video_intro']}个")
print(f"转场效果: {all_effects['total_counts']['transitions']}个")
```

### 查询特定类型特效

```python
# 获取文本入场动画列表
text_intros = draft.get_effect_by_type("text_intro")
print("可用的文本入场动画:")
for i, effect in enumerate(text_intros[:10]):  # 显示前10个
    print(f"  {i+1}. {effect}")

# 获取转场效果列表
transitions = draft.get_effect_by_type("transitions")
print("可用的转场效果:")
for i, effect in enumerate(transitions[:10]):  # 显示前10个
    print(f"  {i+1}. {effect}")

# 获取视频动画列表
video_intros = draft.get_effect_by_type("video_intro")
print("可用的视频入场动画:")
for i, effect in enumerate(video_intros[:10]):  # 显示前10个
    print(f"  {i+1}. {effect}")
```

## 🎯 完整示例：创建多场景视频

```python
def create_multi_scene_video():
    # 初始化
    draft = DraftManager()
    draft_id = draft.create_draft(1920, 1080, 30, "多场景视频")
    
    # 创建轨道
    video_track = draft.track_manager.create_track("video", "主视频")
    audio_track = draft.track_manager.create_track("audio", "背景音乐")
    title_track = draft.track_manager.create_track("text", "标题")
    subtitle_track = draft.track_manager.create_track("text", "字幕")
    
    # 注册素材
    video_material = draft.media_manager.register_video_material("main_video.mp4")
    audio_material = draft.media_manager.register_audio_material("background_music.mp3")
    
    # 场景1: 开场 (0-4秒)
    scene1_video = draft.video_manager.create_video_segment(
        material_id=video_material,
        target_timerange=trange(0, 4.0),
        track_id=video_track
    )
    draft.video_manager.add_animation(scene1_video, "斜切", tim(1.0))
    draft.video_manager.add_transition(scene1_video, "信号故障", tim(0.5))
    draft.track_manager.add_segment_to_track(video_track, scene1_video)
    
    scene1_title = draft.text_manager.create_text_segment(
        text="开场介绍",
        target_timerange=trange(0, 4.0),
        track_id=title_track
    )
    draft.text_manager.add_animation(scene1_title, "复古打字机", tim(1.0))
    draft.track_manager.add_segment_to_track(title_track, scene1_title)
    
    # 场景2: 主要内容 (4-9秒)
    scene2_video = draft.video_manager.create_video_segment(
        material_id=video_material,
        target_timerange=trange(4.0, 5.0),
        source_timerange=trange(10.0, 5.0),  # 从素材第10秒开始
        track_id=video_track
    )
    draft.video_manager.add_animation(scene2_video, "放大", tim(1.0))
    draft.video_manager.add_transition(scene2_video, "叠化", tim(0.5))
    draft.track_manager.add_segment_to_track(video_track, scene2_video)
    
    scene2_title = draft.text_manager.create_text_segment(
        text="主要内容",
        target_timerange=trange(4.0, 5.0),
        track_id=title_track
    )
    draft.text_manager.add_animation(scene2_title, "弹入", tim(1.0))
    draft.track_manager.add_segment_to_track(title_track, scene2_title)
    
    # 添加背景音乐
    background_audio = draft.audio_manager.create_audio_segment(
        material_id=audio_material,
        target_timerange=trange(0, 9.0),
        track_id=audio_track,
        volume=0.3
    )
    draft.audio_manager.add_fade(background_audio, tim(1.0), tim(1.0))
    draft.track_manager.add_segment_to_track(audio_track, background_audio)
    
    # 导出项目
    success = draft.export_draft("./multi_scene_output")
    return success

# 运行示例
if __name__ == "__main__":
    success = create_multi_scene_video()
    print(f"项目创建{'成功' if success else '失败'}")
```

## ⚠️ 注意事项

### 特效验证
- 所有特效都会自动验证是否被支持
- 不支持的特效会显示警告和替代建议
- 建议使用 `get_effect_by_type()` 查询可用特效

### 时间管理
- 所有时间都以微秒为单位存储
- 使用 `tim()` 和 `trange()` 函数简化时间操作
- 注意片段时间不要重叠

### 文件路径
- 素材路径支持相对路径和绝对路径
- 确保素材文件存在且可访问
- 支持常见的视频、音频格式

### 导出
- 导出前确保所有片段都已添加到轨道
- 导出目录会自动创建
- 生成 `draft_content.json` 和 `draft_meta_info.json` 两个文件

## 🎉 开始创建你的剪映项目吧！
