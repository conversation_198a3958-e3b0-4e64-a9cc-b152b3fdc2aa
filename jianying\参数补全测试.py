#!/usr/bin/env python3
"""
参数补全测试 - 验证所有新增参数的功能
"""

from draft_manager import DraftManager
from data_structures import trange, tim, ClipSettings, TextStyle, TextBorder, TextBackground

def test_all_new_parameters():
    """测试所有新增的参数"""
    print("🧪 开始参数补全测试...")
    
    # 初始化
    draft = DraftManager()
    draft_id = draft.create_draft(1920, 1080, 30, "参数补全测试项目")
    
    # 创建轨道
    video_track = draft.track_manager.create_track("video", "主视频")
    audio_track = draft.track_manager.create_track("audio", "主音频")
    text_track = draft.track_manager.create_track("text", "字幕")
    
    # 注册素材
    video_material = draft.media_manager.register_video_material(r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4")
    audio_material = draft.media_manager.register_audio_material(r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\audio.mp3")
    
    print("\n📊 测试VideoSegment新增参数:")
    
    # 1. 测试ClipSettings
    clip_settings = ClipSettings(
        brightness=20.0,
        contrast=15.0,
        saturation=10.0,
        temperature=5.0,
        tint=-5.0,
        highlights=-10.0,
        shadows=15.0,
        vignette=5.0,
        sharpen=20.0,
        hue=10.0
    )
    
    # 2. 测试VideoSegment的change_pitch和clip_settings参数
    video_id = draft.video_manager.create_video_segment(
        material_id=video_material,
        target_timerange=trange(0, 5.0),
        track_id=video_track,
        speed=1.2,
        volume=0.8,
        change_pitch=True,  # 新增参数
        clip_settings=clip_settings  # 新增参数
    )
    
    if video_id:
        print("   ✅ VideoSegment创建成功，包含change_pitch和clip_settings参数")
        
        # 3. 测试add_mask的完整参数
        mask_success = draft.video_manager.add_mask(
            video_id=video_id,
            mask_type="圆形",
            center_x=0.3,
            center_y=0.4,
            size=0.6,
            rotation=45.0,  # 新增参数
            feather=20.0,   # 新增参数
            invert=False    # 新增参数
        )
        
        if mask_success:
            print("   ✅ add_mask完整参数测试成功")
        else:
            print("   ❌ add_mask完整参数测试失败")
        
        # 4. 测试矩形蒙版的特有参数
        rect_mask_success = draft.video_manager.add_mask(
            video_id=video_id,
            mask_type="矩形",
            center_x=0.5,
            center_y=0.5,
            size=0.8,
            rotation=30.0,
            feather=15.0,
            invert=False,
            rect_width=0.6,     # 矩形特有参数
            round_corner=25.0   # 矩形特有参数
        )
        
        if rect_mask_success:
            print("   ✅ 矩形蒙版特有参数测试成功")
        else:
            print("   ❌ 矩形蒙版特有参数测试失败")
        
        # 5. 测试add_effect的params参数
        effect_success = draft.video_manager.add_effect(
            video_id=video_id,
            effect_type="光晕效果",
            params=[80.0, None, 60.0]  # 新增参数支持
        )
        
        if effect_success:
            print("   ✅ add_effect的params参数测试成功")
        else:
            print("   ❌ add_effect的params参数测试失败")
    
    else:
        print("   ❌ VideoSegment创建失败")
    
    print("\n🎵 测试AudioSegment新增参数:")
    
    # 6. 测试AudioSegment的change_pitch参数
    audio_id = draft.audio_manager.create_audio_segment(
        material_id=audio_material,
        target_timerange=trange(0, 5.0),
        track_id=audio_track,
        speed=0.9,
        volume=0.7,
        change_pitch=False  # 新增参数
    )
    
    if audio_id:
        print("   ✅ AudioSegment创建成功，包含change_pitch参数")
        
        # 7. 测试音频特效的params参数
        audio_effect_success = draft.audio_manager.add_effect(
            audio_id=audio_id,
            effect_type="回声",
            params=[70.0, 50.0, None]  # 新增参数支持
        )
        
        if audio_effect_success:
            print("   ✅ 音频特效的params参数测试成功")
        else:
            print("   ❌ 音频特效的params参数测试失败")
    
    else:
        print("   ❌ AudioSegment创建失败")
    
    print("\n📝 测试TextSegment新增参数:")
    
    # 8. 测试TextStyle类
    text_style = TextStyle(
        size=24.0,
        bold=True,
        italic=False,
        underline=True,
        color=(1.0, 0.8, 0.2),  # 金色
        alpha=0.9,
        align=1,  # 居中
        vertical=False,
        letter_spacing=2,
        line_spacing=5,
        auto_wrapping=True,
        max_line_width=0.8
    )
    
    # 9. 测试TextBorder类
    text_border = TextBorder(
        alpha=0.8,
        color=(0.0, 0.0, 0.0),  # 黑色描边
        width=30.0
    )
    
    # 10. 测试TextBackground类
    text_background = TextBackground(
        color="#FF6B6B",
        style=2,  # 圆角矩形
        alpha=0.7,
        round_radius=0.3,
        height=0.2,
        width=0.8,
        horizontal_offset=0.5,
        vertical_offset=0.5
    )
    
    # 11. 测试文本图像调节
    text_clip_settings = ClipSettings(
        brightness=10.0,
        contrast=5.0,
        saturation=15.0
    )
    
    # 12. 测试TextSegment的完整参数
    text_id = draft.text_manager.create_text_segment(
        text="测试文本样式",
        target_timerange=trange(1.0, 4.0),
        track_id=text_track,
        font={"name": "Arial", "size": 24},
        style=text_style,           # 新增参数
        clip_settings=text_clip_settings,  # 新增参数
        border=text_border,         # 新增参数
        background=text_background  # 新增参数
    )
    
    if text_id:
        print("   ✅ TextSegment创建成功，包含所有新增参数")
        print(f"      样式: 大小{text_style.size}, 粗体{text_style.bold}, 颜色{text_style.color}")
        print(f"      描边: 宽度{text_border.width}, 颜色{text_border.color}")
        print(f"      背景: 颜色{text_background.color}, 样式{text_background.style}")
    else:
        print("   ❌ TextSegment创建失败")
    
    # 添加片段到轨道
    if video_id:
        draft.track_manager.add_segment_to_track(video_track, video_id)
    if audio_id:
        draft.track_manager.add_segment_to_track(audio_track, audio_id)
    if text_id:
        draft.track_manager.add_segment_to_track(text_track, text_id)
    
    print("\n📊 参数完整性检查:")
    
    # 检查VideoSegmentInfo
    if video_id:
        video_segment = draft.video_manager.get_segment(video_id)
        if video_segment:
            print(f"   VideoSegment - change_pitch: {video_segment.change_pitch}")
            print(f"   VideoSegment - clip_settings: {video_segment.clip_settings is not None}")
            print(f"   VideoSegment - masks数量: {len(video_segment.masks)}")
            print(f"   VideoSegment - effects数量: {len(video_segment.effects)}")
    
    # 检查AudioSegmentInfo
    if audio_id:
        audio_segment = draft.audio_manager.get_segment(audio_id)
        if audio_segment:
            print(f"   AudioSegment - change_pitch: {audio_segment.change_pitch}")
            print(f"   AudioSegment - audio_effects数量: {len(audio_segment.audio_effects)}")
    
    # 检查TextSegmentInfo
    if text_id:
        text_segment = draft.text_manager.get_segment(text_id)
        if text_segment:
            print(f"   TextSegment - style类型: {type(text_segment.style).__name__}")
            print(f"   TextSegment - border类型: {type(text_segment.border).__name__}")
            print(f"   TextSegment - background类型: {type(text_segment.background).__name__}")
            print(f"   TextSegment - clip_settings: {text_segment.clip_settings is not None}")
    
    # 导出测试
    print(f"\n📤 导出测试...")
    success = draft.export_draft("./参数补全测试输出")
    
    if success:
        print("✅ 参数补全测试完成，所有新增参数都已成功集成！")
        print("\n🎯 新增参数总结:")
        print("   VideoSegment: change_pitch, clip_settings")
        print("   AudioSegment: change_pitch")
        print("   TextSegment: style(TextStyle), border(TextBorder), background(TextBackground), clip_settings")
        print("   add_mask: rotation, feather, invert, rect_width, round_corner")
        print("   add_effect: params (支持Optional[float]列表)")
        return True
    else:
        print("❌ 导出失败")
        return False


if __name__ == "__main__":
    success = test_all_new_parameters()
    if success:
        print("\n🎉 所有参数补全测试通过！")
    else:
        print("\n⚠️ 部分测试未通过，请检查实现")
