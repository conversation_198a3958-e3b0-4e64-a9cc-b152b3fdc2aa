# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/8 下午3:58
File Name:jianying_demo.py
"""
import os
import pyJianYingDraft as draft
from pyJianYingDraft import IntroType, TransitionType, trange, tim, FilterType

draft_folder = draft.DraftFolder("./output")
script = draft_folder.create_draft("文本功能示例标准", 1920, 1080, allow_replace=True)
script.add_track(draft.TrackType.text)

# 创建基础文本
basic_text = draft.TextSegment(
    text="基础文本",
    timerange=draft.trange("0s", "2s")
)
script.add_segment(basic_text)

# 创建带样式的文本
style = draft.TextStyle(
    size=15.0,
    bold=True,
    color=(1.0, 0.0, 0.0),  # 红色
    align=1  # 居中
)

styled_text = draft.TextSegment(
    text="带样式文本",
    timerange=draft.trange("2s", "2s"),
    style=style
)
script.add_segment(styled_text)

# 创建带描边的文本
border = draft.TextBorder(
    alpha=1.0,
    color=(0.0, 0.0, 0.0),  # 黑色描边
    width=50.0
)

border_text = draft.TextSegment(
    text="带描边文本",
    timerange=draft.trange("4s", "2s"),
    border=border
)
script.add_segment(border_text)

# 创建带背景的文本
background = draft.TextBackground(
    color="#FFFF00",  # 黄色背景
    alpha=0.8,
    style=1
)

bg_text = draft.TextSegment(
    text="带背景文本",
    timerange=draft.trange("6s", "2s"),
    background=background
)
script.add_segment(bg_text)

# 保存项目
project_path = script.save()
