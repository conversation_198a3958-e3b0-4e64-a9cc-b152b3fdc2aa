package com.esther.jianyingdraft.config

import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.exception.SysException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestControllerAdvice

/**
 * 全局异常处理器，统一处理系统异常并返回标准响应结构。
 * <AUTHOR>
 */
@RestControllerAdvice
@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
class GlobalExceptionHandler {
    private val logger = LoggerFactory.getLogger(GlobalExceptionHandler::class.java)

    /**
     * 处理自定义系统异常
     */
    @ExceptionHandler(SysException::class)
    fun handleSysException(ex: SysException): DataResponse<String> {
        logger.error("业务异常: ", ex)
        return DataResponse.error(ex.code, ex.message)
    }

    /**
     * 处理所有未捕获异常
     */
    @ExceptionHandler(Throwable::class)
    fun handleException(ex: Throwable): DataResponse<String> {
        logger.error("系统异常: ", ex)
        return DataResponse.error(500, ex.message ?: "系统异常")
    }
} 