package com.esther.jianyingdraft.controller

import com.esther.jianyingdraft.domain.audio.AudioEffectReqDto
import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.domain.req.AudioFadeEffectReqDto
import com.esther.jianyingdraft.domain.req.AudioKeyframeReqDto
import com.esther.jianyingdraft.domain.req.MediaSegmentAddReqDto
import com.esther.jianyingdraft.service.AudioSegmentService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 音频片段控制器，提供音频片段相关接口。
 * <AUTHOR>
 */
@Tag(name = "音频片段管理", description = "音频片段相关接口")
@RestController
@RequestMapping("/segment/audio")
class AudioSegmentController @Autowired constructor(
    private val audioSegmentService: AudioSegmentService
) {
    private val logger = LoggerFactory.getLogger(AudioSegmentController::class.java)

    /**
     * 添加音频片段接口
     * @param req 音频片段添加请求参数
     * @return 新增音频片段的id，统一响应包装
     */
    @Operation(summary = "添加音频片段", description = "添加一个新的音频片段到MongoDB")
    @PostMapping("/add")
    suspend fun addAudioSegment(@RequestBody req: MediaSegmentAddReqDto): DataResponse<String> {
        logger.info("收到添加音频片段请求，draftId={}, resourcePath={}", req.draftId, req.resourcePath)
        val id = audioSegmentService.addAudioSegment(req)
        logger.info("音频片段添加完成，id={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给音频片段添加或更新淡入淡出特效接口
     * @param req 音频淡入淡出特效请求参数
     * @return 音频片段id，统一响应包装
     */
    @Operation(summary = "添加/更新音频淡入淡出特效", description = "为音频片段添加或更新淡入淡出特效")
    @PostMapping("/add-fade-effect")
    suspend fun addAudioFadeEffect(@RequestBody req: AudioFadeEffectReqDto): DataResponse<String> {
        logger.info("收到添加/更新音频淡入淡出特效请求，draftId={}, audioSegmentId={}", req.draftId, req.audioSegmentId)
        val id = audioSegmentService.addAudioFadeEffect(req)
        logger.info("音频片段淡入淡出特效添加/更新完成，audioSegmentId={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给音频片段批量添加关键帧接口
     * @param req 关键帧请求参数，支持一次添加多个关键帧
     * @return 音频片段id，统一响应包装
     */
    @Operation(
        summary = "批量添加音频关键帧",
        description = "为音频片段批量添加关键帧信息，同一片段的timeOffset和volume组合不能重复"
    )
    @PostMapping("/add-keyframe")
    suspend fun addAudioKeyframe(@RequestBody req: AudioKeyframeReqDto): DataResponse<String> {
        logger.info(
            "收到批量添加音频关键帧请求，draftId={}, audioSegmentId={}, 关键帧数量={}",
            req.draftId,
            req.audioSegmentId,
            req.keyframes.size
        )
        req.keyframes.forEach { keyframe ->
            logger.info(
                "关键帧详情：timeOffset={}, volume={}",
                keyframe.timeOffset,
                keyframe.volume
            )
        }
        val id = audioSegmentService.addAudioKeyframe(req)
        logger.info("音频片段关键帧批量添加完成，audioSegmentId={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给音频片段添加特效接口
     * @param req 音频特效请求参数列表
     * @return 音频片段id，统一响应包装
     */
    @Operation(
        summary = "添加音频特效",
        description = "为音频片段添加音频特效，同一类型的特效根据resourceId去重，只保留最新的"
    )
    @PostMapping("/add-effects")
    suspend fun addAudioEffect(@RequestBody req: AudioEffectReqDto): DataResponse<String> {
        logger.info("收到添加音频特效请求，请求数量={}", req.audioEffects.size)
        req.audioEffects.forEach { effectReq ->
            logger.info(
                "音频特效详情：draftId={}, segmentId={}, effectType={}",
                req.draftId,
                req.segmentId,
                effectReq.effectType.resourceId
            )
        }
        val id = audioSegmentService.addAudioEffect(req)
        logger.info("音频片段特效添加完成，audioSegmentId={}", id)
        return DataResponse.success(id)
    }
} 