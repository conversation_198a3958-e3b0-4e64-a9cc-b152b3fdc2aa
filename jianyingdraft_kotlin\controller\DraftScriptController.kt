package com.esther.jianyingdraft.controller

import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.domain.rep.DraftCreateRepDto
import com.esther.jianyingdraft.domain.req.DraftCreateReqDto
import com.esther.jianyingdraft.service.DraftService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.core.io.buffer.DataBuffer
import org.springframework.http.ContentDisposition
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux
import kotlinx.coroutines.reactor.asFlux // 引入 asFlux 扩展函数
import org.springframework.web.bind.annotation.RequestHeader
import java.nio.charset.StandardCharsets

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 草稿脚本控制器
 */
@RestController
@RequestMapping("/draft")
@Tag(name = "草稿管理", description = "草稿管理相关接口")
class DraftScriptController(
    val draftService: DraftService
) {
    private val logger = LoggerFactory.getLogger(DraftScriptController::class.java)

    @Operation(summary = "创建草稿", description = "创建一个新的草稿")
    @PostMapping("/create")
    suspend fun createDraftScript(
        @RequestBody reqDto: DraftCreateReqDto,
        @RequestHeader apiKey: String
    ): DataResponse<DraftCreateRepDto> {
        reqDto.apiKey = apiKey
        return DataResponse.success(draftService.createDraftScript(reqDto))
    }

    /**
     * 导出草稿为zip压缩包
     * @param draftId 草稿ID
     * @return zip文件下载响应 (现在是完全流式响应，不生成临时文件)
     */
    @Operation(
        summary = "导出草稿为zip",
        description = "导出草稿及其所有资源文件为一个zip压缩包，包含元数据、草稿内容和媒体资源"
    )
    @GetMapping("/{draftId}/export")
    suspend fun exportDraftAsZip(
        @Parameter(description = "草稿ID", required = true)
        @PathVariable draftId: String
    ): ResponseEntity<Flux<DataBuffer>> {
        logger.info("开始以完全流式方式导出草稿zip内容 (使用Flow和asFlux)，草稿ID: {}", draftId)

        val dataBufferFlow: kotlinx.coroutines.flow.Flow<DataBuffer>
        try {
            // 直接从服务层获取 DataBuffer Flow
            dataBufferFlow = draftService.exportDraftAsZip(draftId)
        } catch (e: Exception) {
            logger.error("以流式方式生成zip内容失败，草稿ID: {}, 错误: {}", draftId, e.message, e)
            return ResponseEntity.internalServerError().build()
        }

        // 使用 asFlux() 将 Kotlin Flow 转换为 Reactor Flux
        val dataBufferFlux: Flux<DataBuffer> = dataBufferFlow.asFlux()

        val fileName = "${draftId}.zip"

        val headers = HttpHeaders().apply {
            contentDisposition = ContentDisposition
                .attachment()
                .filename(fileName, StandardCharsets.UTF_8)
                .build()
            contentType = MediaType.APPLICATION_OCTET_STREAM
            // 再次强调：这里不再设置 Content-Length，因为文件大小是动态生成的，无法预知。
            // 客户端会通过分块传输 (Transfer-Encoding: chunked) 来接收。
        }

        logger.info(
            "草稿导出为zip文件流准备就绪（无临时文件，无Content-Length），草稿ID: {}, 文件名: {}",
            draftId,
            fileName
        )

        return ResponseEntity.ok()
            .headers(headers)
            .body(dataBufferFlux)
    }
}