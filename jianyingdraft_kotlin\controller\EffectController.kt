package com.esther.jianyingdraft.controller

import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.rpc.DraftScriptRpc
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import kotlinx.coroutines.reactive.awaitSingle
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2025/8/6
 * @des des
 */
@Tag(name = "特效接口", description = "特效接口")
@RestController
@RequestMapping("/effects")
class EffectController(val draftScriptRpc: DraftScriptRpc) {

    /**
     * 获取所有特效类型
     */
    @Operation(summary = "获取所有特效类型", description = "获取所有特效类型")
    @GetMapping("/all_types")
    suspend fun getAllTypes(): DataResponse<MutableMap<String, Any?>> = draftScriptRpc.getAllTypes().awaitSingle()


    /**
     * 获取所有特效
     * @param effectType 特效类型
     * @param isVip 是否为会员
     */
    @Operation(summary = "获取所有特效", description = "获取所有特效, 类型需要调用all_types接口获取")
    @GetMapping
    suspend fun effects(
        @Parameter(description = "特效类型") @RequestParam("effect_type") effectType: String,
        @Parameter(description = "是否为会员") @RequestParam("is_vip") isVip: Boolean?
    ): DataResponse<List<MutableMap<String, Any?>>> {
        return draftScriptRpc.effects(effectType, isVip).awaitSingle()
    }
}