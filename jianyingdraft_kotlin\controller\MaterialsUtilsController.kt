package com.esther.jianyingdraft.controller

import com.esther.jianyingdraft.domain.meterial.MediaInfo
import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.service.MaterialsUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2025/7/30
 * @des des
 */
@Tag(name = "素材工具接口", description = "素材工具接口")
@RestController
@RequestMapping("/materials/utils")
class MaterialsUtilsController(private val materialsUtils: MaterialsUtils) {

    /**
     * 获取素材信息
     * @param filePath
     * @return MediaInfo 素材信息
     */
    @Operation(summary = "获取素材信息", description = "获取素材信息,视频,音频,图片")
    @GetMapping("/media-info")
    suspend fun mediaInfo(@RequestParam("filePath") filePath: String): DataResponse<MediaInfo> {
        return DataResponse.success(materialsUtils.mediaInfo(filePath))
    }
}