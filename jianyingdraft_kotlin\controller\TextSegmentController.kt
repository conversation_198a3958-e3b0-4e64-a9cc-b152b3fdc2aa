package com.esther.jianyingdraft.controller

import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.domain.req.TextSegmentAddReqDto
import com.esther.jianyingdraft.domain.req.TextAnimationAndEffectReqDto
import com.esther.jianyingdraft.service.TextSegmentService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation

/**
 * 字幕片段控制器，提供字幕片段相关接口。
 * <AUTHOR>
 */
@Tag(name = "字幕片段管理", description = "字幕片段相关接口")
@RestController
@RequestMapping("/segment/text")
class TextSegmentController @Autowired constructor(
    private val textSegmentService: TextSegmentService
) {
    private val logger = LoggerFactory.getLogger(TextSegmentController::class.java)

    /**
     * 添加字幕片段接口
     * @param req 字幕片段添加请求参数
     * @return 新增字幕片段的id，统一响应包装
     */
    @Operation(summary = "添加字幕片段", description = "添加一个新的字幕片段到MongoDB")
    @PostMapping("/add")
    suspend fun addTextSegment(@RequestBody req: TextSegmentAddReqDto): DataResponse<String> {
        logger.info("收到添加字幕片段请求，draftId={}, text={}", req.draftId, req.text)
        val id = textSegmentService.addTextSegment(req)
        logger.info("字幕片段添加完成，id={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给字幕片段添加或更新动画和特效接口
     * @param req 动画和特效请求参数
     * @return 字幕片段id，统一响应包装
     */
    @Operation(summary = "添加/更新字幕动画和特效", description = "为字幕片段添加或更新动画和特效")
    @PostMapping("/add-animation-effect")
    suspend fun addOrUpdateTextAnimationAndEffectToSegment(@RequestBody req: TextAnimationAndEffectReqDto): DataResponse<String> {
        logger.info("收到添加/更新字幕动画和特效请求，textSegmentId={}, draftId={}", req.textSegmentId, req.draftId)
        val id = textSegmentService.addOrUpdateTextAnimationAndEffectToSegment(req)
        logger.info("字幕动画和特效添加/更新完成，textSegmentId={}", id)
        return DataResponse.success(id)
    }
} 