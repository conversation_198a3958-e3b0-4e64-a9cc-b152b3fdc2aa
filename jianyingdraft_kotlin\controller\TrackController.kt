package com.esther.jianyingdraft.controller

import com.esther.jianyingdraft.domain.req.TrackAddReqDto
import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.service.TrackService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation

/**
 * 轨道控制器，提供轨道相关的接口。
 * <AUTHOR>
 */
@Tag(name = "轨道管理", description = "轨道相关接口")
@RestController
@RequestMapping("/track")
class TrackController @Autowired constructor(
    private val trackService: TrackService
) {
    private val logger = LoggerFactory.getLogger(TrackController::class.java)

    /**
     * 添加轨道接口
     * @param req 轨道添加请求参数
     * @return 新增轨道的id，统一响应包装
     */
    @Operation(summary = "添加轨道", description = "添加一个新的轨道到MongoDB")
    @PostMapping("/add")
    suspend fun addTrack(@RequestBody req: TrackAddReqDto): DataResponse<String> {
        logger.info("收到添加轨道请求，draftId={}, trackType={}", req.draftId, req.trackType)
        val id = trackService.addTrack(req)
        logger.info("轨道添加完成，id={}", id)
        return DataResponse.success(id)
    }
} 