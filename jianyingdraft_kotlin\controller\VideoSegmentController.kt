package com.esther.jianyingdraft.controller

import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.domain.req.BackgroundFillingReqDto
import com.esther.jianyingdraft.domain.req.MediaSegmentAddReqDto
import com.esther.jianyingdraft.domain.req.TransitionTypeReqDto
import com.esther.jianyingdraft.domain.req.VideoAnimationReqDto
import com.esther.jianyingdraft.domain.video.VideoEffectReqDto
import com.esther.jianyingdraft.domain.video.VideoFilterReqDto
import com.esther.jianyingdraft.domain.video.VideoMaskReqDto
import com.esther.jianyingdraft.service.VideoSegmentService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 视频片段控制器，提供视频片段相关接口。
 * <AUTHOR>
 */
@Tag(name = "视频片段管理", description = "视频片段相关接口")
@RestController
@RequestMapping("/segment/video")
class VideoSegmentController @Autowired constructor(
    private val videoSegmentService: VideoSegmentService
) {
    private val logger = LoggerFactory.getLogger(VideoSegmentController::class.java)

    /**
     * 添加视频片段接口
     * @param req 视频片段添加请求参数
     * @return 新增视频片段的id，统一响应包装
     */
    @Operation(summary = "添加视频片段", description = "添加一个新的视频片段到MongoDB")
    @PostMapping("/add")
    suspend fun addVideoSegment(@RequestBody req: MediaSegmentAddReqDto): DataResponse<String> {
        logger.info("收到添加视频片段请求，draftId={}, resourcePath={}", req.draftId, req.resourcePath)
        val id = videoSegmentService.addVideoSegment(req)
        logger.info("视频片段添加完成，id={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给视频片段添加动画接口
     * @param req 动画请求参数
     * @return 视频片段id，统一响应包装
     */
    @Operation(summary = "给视频片段添加动画", description = "给指定的视频片段添加动画效果")
    @PostMapping("/add-animation")
    suspend fun addAnimation(@RequestBody req: VideoAnimationReqDto): DataResponse<String> {
        logger.info(
            "收到添加视频动画请求，draftId={}, videoSegmentId={}, type={}",
            req.draftId,
            req.videoSegmentId,
            req.type
        )
        val id = videoSegmentService.addAnimation(req)
        logger.info("视频片段动画添加完成，videoSegmentId={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给视频片段添加转场特效接口
     * @param req 转场请求参数
     * @return 视频片段id，统一响应包装
     */
    @Operation(summary = "给视频片段添加转场特效", description = "给指定的视频片段添加转场特效")
    @PostMapping("/add-transition")
    suspend fun addTransition(@RequestBody req: TransitionTypeReqDto): DataResponse<String> {
        logger.info(
            "收到添加视频转场特效请求，draftId={}, videoSegmentId={}, type={}",
            req.draftId,
            req.videoSegmentId,
            req.transitionType
        )
        val id = videoSegmentService.addTransition(req)
        logger.info("视频片段转场特效添加完成，videoSegmentId={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给视频片段添加背景填充特效接口
     * @param req 背景填充请求参数
     * @return 视频片段id，统一响应包装
     */
    @Operation(summary = "给视频片段添加背景填充特效", description = "给指定的视频片段添加背景填充特效")
    @PostMapping("/add-background-filling")
    suspend fun addBackgroundFilling(@RequestBody req: BackgroundFillingReqDto): DataResponse<String> {
        logger.info(
            "收到添加视频背景填充特效请求，draftId={}, videoSegmentId={}, fillType={}",
            req.draftId,
            req.videoSegmentId,
            req.fillType
        )
        val id = videoSegmentService.addBackgroundFilling(req)
        logger.info("视频片段背景填充特效添加完成，videoSegmentId={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给视频片段添加特效接口
     * @param req 视频特效请求参数
     * @return 视频片段id，统一响应包装
     */
    @Operation(
        summary = "添加视频特效",
        description = "为视频片段添加视频特效，同一类型的特效根据resourceId去重，只保留最新的"
    )
    @PostMapping("/add-effects")
    suspend fun addEffect(@RequestBody req: VideoEffectReqDto): DataResponse<String> {
        logger.info("收到添加视频特效请求，draftId={}, segmentId={}", req.draftId, req.segmentId)
        req.effects.forEach { effect ->
            logger.info(
                "视频特效详情：effectType={}, params={}",
                effect.effectType.resourceId,
                effect.params
            )
        }
        val id = videoSegmentService.addEffect(req)
        logger.info("视频片段特效添加完成，videoSegmentId={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给视频片段添加滤镜接口
     * @param req 视频滤镜请求参数
     * @return 视频片段id，统一响应包装
     */
    @Operation(
        summary = "添加视频滤镜",
        description = "为视频片段添加视频滤镜，同一类型的滤镜根据resourceId去重，只保留最新的"
    )
    @PostMapping("/add-filters")
    suspend fun addFilter(@RequestBody req: VideoFilterReqDto): DataResponse<String> {
        logger.info("收到添加视频滤镜请求，draftId={}, segmentId={}", req.draftId, req.segmentId)
        req.filters.forEach { filter ->
            logger.info(
                "视频滤镜详情：filterType={}, intensity={}",
                filter.filterType.resourceId,
                filter.intensity
            )
        }
        val id = videoSegmentService.addFilter(req)
        logger.info("视频片段滤镜添加完成，videoSegmentId={}", id)
        return DataResponse.success(id)
    }

    /**
     * 给视频片段添加蒙版接口
     * @param req 视频蒙版请求参数
     * @return 视频片段id，统一响应包装
     */
    @Operation(
        summary = "添加视频蒙版",
        description = "为视频片段添加视频蒙版，同一个片段只能有一个蒙版，新蒙版会覆盖旧蒙版"
    )
    @PostMapping("/add-mask")
    suspend fun addMask(@RequestBody req: VideoMaskReqDto): DataResponse<String> {
        logger.info(
            "收到添加视频蒙版请求，draftId={}, segmentId={}, maskType={}",
            req.draftId,
            req.segmentId,
            req.maskType.resourceId
        )
        logger.info(
            "蒙版参数详情：centerX={}, centerY={}, size={}, rotation={}, feather={}, invert={}",
            req.centerX,
            req.centerY,
            req.size,
            req.rotation,
            req.feather,
            req.invert
        )
        val id = videoSegmentService.addMask(req)
        logger.info("视频片段蒙版添加完成，videoSegmentId={}", id)
        return DataResponse.success(id)
    }
} 