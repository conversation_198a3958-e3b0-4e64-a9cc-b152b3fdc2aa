package com.esther.jianyingdraft.domain

import com.esther.jianyingdraft.exception.SysException
import com.esther.jianyingdraft.utils.TimeUtils
import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 记录了起始时间及持续长度的时间范围
 * 时间用 0s, 1s等 表示
 */
@Schema(description = "时间范围")
data class Timerange(
    @field:Schema(description = "起始时间，当 afterSegmentId 存在时可为空，系统自动计算")
    var start: String? = null,
    @field:Schema(description = "持续长度")
    var duration: String? = null
) {
    fun toLongDto(): TimerangeDto {
        if (duration == null) {
            throw SysException.systemError("duration can not be null")
        }
        if (start == null) {
            throw SysException.systemError("start can not be null when converting to TimerangeDto")
        }
        return TimerangeDto(
            start = TimeUtils.tim(start!!),
            duration = TimeUtils.tim(duration!!)
        )
    }
}

data class TimerangeDto(
    val start: Long,
    val duration: Long
)