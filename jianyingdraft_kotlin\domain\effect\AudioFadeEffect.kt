package com.esther.jianyingdraft.domain.effect

import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 音频淡入淡出
 */
@Schema(description = "音频淡入淡出特效")
data class AudioFadeEffect(
    /**
     * 淡入时间
     */
    @field:Schema(description = "淡入时间")
    val inDuration: String,
    /**
     * 淡出时间
     */
    @field:Schema(description = "淡出时间")
    val outDuration: String,
)