package com.esther.jianyingdraft.domain.rep

import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
@Schema(description = "统一响应实体")
data class DataResponse<T>(
    @field:<PERSON>hem<PERSON>(description = "响应码，200为成功")
    var code: Int = 200,
    @field:Schema(description = "响应消息")
    var message: String? = null,
    @field:Schema(description = "响应数据体")
    var data: T? = null,
) {
    companion object {
        fun <T> success(data: T?): DataResponse<T> {
            return DataResponse(200, "success", data)
        }

        fun success(): DataResponse<String> {
            return DataResponse(200, "success")
        }

        fun error(code: Int, message: String?): DataResponse<String> {
            return DataResponse(code, message)
        }

        fun error(message: String?): DataResponse<String> {
            return DataResponse(500, message)
        }
    }
}
