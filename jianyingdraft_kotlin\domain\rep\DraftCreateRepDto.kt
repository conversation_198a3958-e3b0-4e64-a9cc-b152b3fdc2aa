package com.esther.jianyingdraft.domain.rep

import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
@Schema(description = "创建草稿响应实体")
data class DraftCreateRepDto(
    @field:Schema(description = "草稿id")
    val draftId: String,
    @field:Schema(description = "草稿名称")
    val draftName: String? = null,
    @field:Schema(description = "草稿内容")
    val draftContent: Map<String, Any?>? = null,
)