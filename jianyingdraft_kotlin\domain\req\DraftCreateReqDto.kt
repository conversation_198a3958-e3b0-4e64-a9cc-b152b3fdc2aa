package com.esther.jianyingdraft.domain.req

import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
@Schema(description = "创建草稿请求参数")
data class DraftCreateReqDto(
    @field:Schema(description = "草稿宽度")
    val width: Int = 1920,
    @field:Schema(description = "草稿高度")
    val height: Int = 1080,
    @field:Schema(description = "帧率")
    val fps: Int = 30,
    @field:Schema(description = "草稿名称")
    var name: String? = null,
    @field:Schema(description = "草稿路径")
    var draftPath: String,

    @field:Schema(description = "apiKey")
    var apiKey: String? = null
)
