package com.esther.jianyingdraft.domain.video

import com.esther.jianyingdraft.domain.req.Resource
import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @des des
 */
@Schema(description = "视频特效类")
data class VideoEffectReqDto(
    @Schema(description = "素材所属的 draftId")
    val draftId: String,

    @Schema(description = "素材所属的片段Id")
    val segmentId: String,

    @Schema(description = "视频特效参数")
    val effects: List<VideoEffect>
)


@Schema(description = "视频特效类")
data class VideoEffect(
    @Schema(
        description = "特效类型"
    )
    val effectType: Resource,

    @Schema(description = "特效参数列表, 参数列表中未提供或为None的项使用默认值.\n" +
            "                参数取值范围(0~100)与剪映中一致. 某个特效类型有何参数以及具体参数顺序以枚举类成员的annotation为准")
    val params: List<Float>? = null
)

// ===========================================================================

@Schema(description = "视频滤镜类")
data class VideoFilterReqDto(

    @Schema(description = "素材所属的 draftId")
    val draftId: String,

    @Schema(description = "素材所属的片段Id")
    val segmentId: String,

    @Schema(description = "滤镜参数")
    val filters: List<VideoFilter>
)

/**
 * 滤镜
 */
@Schema(description = "视频滤镜类")
data class VideoFilter(
    @Schema(description = "滤镜强度(0-100), 仅当所选滤镜能够调节强度时有效. 默认为100.")
    val filterType: Resource,
    @Schema(description = "滤镜强度")
    val intensity: Float = 100.0f
)

// ===========================================================================
/**
 * 视频蒙版
 * 一个视频片段只能有一个蒙版
 */
@Schema(description = "视频蒙版类")
data class VideoMaskReqDto(

    @Schema(description = "素材所属的 draftId")
    val draftId: String,

    @Schema(description = "素材所属的片段Id")
    val segmentId: String,

    @Schema(description = "蒙版类型")
    val maskType: Resource,

    @Schema(description = "蒙版中心点X坐标(以素材的像素为单位), 默认设置在素材中心")
    val centerX: Float = 0f,

    @Schema(description = "蒙版中心点Y坐标(以素材的像素为单位), 默认设置在素材中心")
    val centerY: Float = 0f,

    @Schema(description = "蒙版的`主要尺寸`(镜面的可视部分高度/圆形直径/爱心高度等), 以占素材高度的比例表示, 默认为0.5")
    val size: Float = 0.5f,

    @Schema(description = "蒙版顺时针旋转的**角度**, 默认不旋转")
    val rotation: Float = 0.0f,

    @Schema(description = "蒙版的羽化参数, 取值范围0~100, 默认无羽化")
    val feather: Float = 0.0f,

    @Schema(description = "是否反转蒙版, 默认不反转")
    val invert: Boolean = false,

    @Schema(description = "矩形蒙版的宽度, 仅在蒙版类型为矩形时允许设置, 以占素材宽度的比例表示, 默认与`size`相同")
    val rectWidth: Float? = null,

    @Schema(description = "矩形蒙版的圆角参数, 仅在蒙版类型为矩形时允许设置, 取值范围0~100, 默认为0")
    val roundCorner: Float? = null
)

/**
 * 视频蒙版
 */
@Schema(description = "视频蒙版类")
data class VideoMask(
    @Schema(description = "蒙版类型")
    val maskType: Resource,

    @Schema(description = "蒙版中心点X坐标(以素材的像素为单位), 默认设置在素材中心")
    val centerX: Float = 0f,

    @Schema(description = "蒙版中心点Y坐标(以素材的像素为单位), 默认设置在素材中心")
    val centerY: Float = 0f,

    @Schema(description = "蒙版的`主要尺寸`(镜面的可视部分高度/圆形直径/爱心高度等), 以占素材高度的比例表示, 默认为0.5")
    val size: Float = 0.5f,

    @Schema(description = "蒙版顺时针旋转的**角度**, 默认不旋转")
    val rotation: Float = 0.0f,

    @Schema(description = "蒙版的羽化参数, 取值范围0~100, 默认无羽化")
    val feather: Float = 0.0f,

    @Schema(description = "是否反转蒙版, 默认不反转")
    val invert: Boolean = false,

    @Schema(description = "矩形蒙版的宽度, 仅在蒙版类型为矩形时允许设置, 以占素材宽度的比例表示, 默认与`size`相同")
    val rectWidth: Float? = null,

    @Schema(description = "矩形蒙版的圆角参数, 仅在蒙版类型为矩形时允许设置, 取值范围0~100, 默认为0")
    val roundCorner: Float? = null
)


