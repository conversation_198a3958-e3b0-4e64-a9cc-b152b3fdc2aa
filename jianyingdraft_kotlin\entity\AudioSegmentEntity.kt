package com.esther.jianyingdraft.entity

import com.esther.jianyingdraft.domain.Timerange
import com.esther.jianyingdraft.domain.TimerangeDto
import com.esther.jianyingdraft.domain.audio.AudioEffect
import com.esther.jianyingdraft.domain.effect.AudioFadeEffect
import com.esther.jianyingdraft.domain.meterial.MediaInfo
import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.data.mongodb.core.index.IndexDirection
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

/**
 * 音频关键帧信息
 */
data class AudioKeyframe(
    /**
     * 关键帧的时间偏移量, 1s,2s
     */
    val timeOffset: String,
    /**
     * 音量在time_offset处的值
     */
    val volume: Float
)

/**
 * 音频片段Mongo实体
 * <AUTHOR>
 */
@Document("audio_segments")
data class AudioSegmentEntity(
    /**
     * 主键id
     */
    val id: String,
    /**
     * 素材所属的 draftId
     */
    @Indexed(name = "draft_id_index", direction = IndexDirection.DESCENDING)
    val draftId: String,
    /**
     * 片段在轨道上的目标时间范围
     */
    val targetTimerange: Timerange,
    /**
     * 实际的片段在轨道上的目标时间范围
     */
    val realTargetTimerange: TimerangeDto,
    /**
     * 截取的素材片段的时间范围
     */
    val sourceTimerange: Timerange? = null,
    /**
     * 实际的截取的素材片段的时间范围
     */
    val realSourceTimerange: TimerangeDto? = null,
    /**
     * 播放速度
     */
    val speed: Double = 1.0,
    /**
     * 音量
     */
    val volume: Double = 1.0,

    /**
     * 轨道id, 把资源添加到哪个轨道
     */
    val trackId: String? = null,
    /**
     * 素材实例或素材路径
     */
    val resourcePath: String,
    /**
     * 音频淡入淡出特效
     */
    val fadeEffect: AudioFadeEffect? = null,
    /**
     * 音频关键帧列表，支持多个关键帧，同一片段的timeOffset和volume组合不能重复
     */
    val audioKeyframes: List<AudioKeyframe>? = null,

    /**
     * 音频特效列表，支持多种类型的音频特效，同一类型根据resourceId去重
     */
    val audioEffects: List<AudioEffect>? = null,

    /**
     * 素材信息
     */
    val mediaInfo: MediaInfo,
    /**
     * 创建时间
     */
    @JsonIgnore
    val createTime: LocalDateTime,
    /**
     * 更新时间
     */
    @JsonIgnore
    val updateTime: LocalDateTime
) 