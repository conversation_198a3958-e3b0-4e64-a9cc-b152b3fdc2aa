package com.esther.jianyingdraft.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
@Document("drafts")
data class DraftEntity(
    @Id
    val id: String,
    val draftName: String?,
    var draftContent: Map<String, Any?>? = null,
    val width: Int = 1920,
    val height: Int = 1080,
    val fps: Int = 30,
    val apiKey: String? = null,
    /**
     * 状态
     * create: 创建
     * finished: 发布
     */
    var status: String = "create",
    /**
     * 导出次数
     */
    var exportCount: Int = 0,
    /**
     * 草稿路径
     */
    var draftPath: String,
    @JsonIgnore
    val createTime: LocalDateTime = LocalDateTime.now(),
    @JsonIgnore
    var updateTime: LocalDateTime = LocalDateTime.now()
)
