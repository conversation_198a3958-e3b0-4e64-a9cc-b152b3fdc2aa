package com.esther.jianyingdraft.entity

import com.esther.jianyingdraft.domain.ClipSettings
import com.esther.jianyingdraft.domain.Timerange
import com.esther.jianyingdraft.domain.TimerangeDto
import com.esther.jianyingdraft.domain.req.Resource
import com.esther.jianyingdraft.domain.req.TextAnimationAndEffect
import com.esther.jianyingdraft.domain.text.TextBackground
import com.esther.jianyingdraft.domain.text.TextBorder
import com.esther.jianyingdraft.domain.text.TextStyle
import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.IndexDirection
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 文本片段
 */
@Document(collection = "text_segments")
data class TextSegmentEntity(
    @Id
    val id: String,
    /**
     * 文本
     */
    var text: String,
    /**
     * 素材所属的 draftId
     */
    @Indexed(name = "draft_id_index", direction = IndexDirection.DESCENDING)
    val draftId: String,
    /**
     * 字体
     */
    var font: Resource? = null,
    /**
     * 样式
     */
    var style: TextStyle? = null,
    /**
     * 边框
     */
    var border: TextBorder? = null,
    /**
     * 裁剪设置
     */
    var clipSettings: ClipSettings = ClipSettings(),
    /**
     * 背景
     */
    var background: TextBackground? = null,

    /**
     * 目标时间范围
     */
    val targetRanger: Timerange? = null,

    /**
     * 实际目标时间范围
     */
    val realTargetRanger: TimerangeDto? = null,

    /**
     * 轨道id, 把资源添加到哪个轨道
     */
    val trackId: String? = null,

    /**
     * 动画效果和特效列表
     */
    val textAnimationAndEffects: List<TextAnimationAndEffect> = emptyList(),

    @JsonIgnore
    val createTime: LocalDateTime = LocalDateTime.now(),
    @JsonIgnore
    val updateTime: LocalDateTime = LocalDateTime.now()
)
