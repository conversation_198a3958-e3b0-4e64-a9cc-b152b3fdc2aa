package com.esther.jianyingdraft.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.data.mongodb.core.index.IndexDirection
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 轨道, 只能添加视频, 音频, 文本
 */
@Document("tracks")
data class TrackEntity(
    val id: String,
    @Indexed(name = "draft_id_index", direction = IndexDirection.DESCENDING)
    val draftId: String,
    /**
     * 轨道类型.
     */
    val trackType: String,
    /**
     * 轨道名称. 仅在创建第一个同类型轨道时允许不指定.
     */
    var trackName: String = id,
    /**
     * 轨道是否静音. 默认不静音.
     */
    val mute: Boolean = false,
    /**
     * 相对(同类型轨道的)图层位置, 越高越接近前景. 默认为0.
     */
    val relativeIndex: Int = 0,
    /**
     * 绝对图层位置, 越高越接近前景. 此参数将直接覆盖相应片段的`render_index`属性, 供有经验的用户使用.
     * 此参数不能与`relative_index`同时使用.
     */
    val absoluteIndex: Int? = null,
    @JsonIgnore
    val createTime: LocalDateTime,
    @JsonIgnore
    val updateTime: LocalDateTime
)
