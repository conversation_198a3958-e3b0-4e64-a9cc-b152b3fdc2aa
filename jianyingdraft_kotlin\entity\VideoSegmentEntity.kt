package com.esther.jianyingdraft.entity

import com.esther.jianyingdraft.domain.ClipSettings
import com.esther.jianyingdraft.domain.Timerange
import com.esther.jianyingdraft.domain.TimerangeDto
import com.esther.jianyingdraft.domain.meterial.MediaInfo
import com.esther.jianyingdraft.domain.req.BackgroundFilling
import com.esther.jianyingdraft.domain.req.TransitionType
import com.esther.jianyingdraft.domain.req.VideoAnimation
import com.esther.jianyingdraft.domain.video.VideoEffect
import com.esther.jianyingdraft.domain.video.VideoFilter
import com.esther.jianyingdraft.domain.video.VideoMask
import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.IndexDirection
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 视频片段
 */
@Document("video_segments")
data class VideoSegmentEntity(
    /**
     * 主键id
     */
    @Id
    val id: String,
    /**
     * 素材所属的 draftId
     */
    @Indexed(name = "draft_id_index", direction = IndexDirection.DESCENDING)
    val draftId: String,
    /**
     * 片段在轨道上的目标时间范围
     */
    val targetTimerange: Timerange,

    /**
     * 实际片段在轨道上的目标时间范围
     */
    val realTargetTimerange: TimerangeDto,
    /**
     * 截取的素材片段的时间范围
     */
    val sourceTimerange: Timerange? = null,

    /**
     * 实际截取的素材片段的时间范围
     */
    val realSourceTimerange: TimerangeDto? = null,
    /**
     * 播放速度
     */
    val speed: Double = 1.0,
    /**
     * 音量
     */
    val volume: Double = 1.0,
    /**
     * 素材实例或素材路径
     */
    val resourcePath: String,
    /**
     * 素材片段的图像调节设置
     */
    var clipSettings: ClipSettings = ClipSettings(),
    /**
     * 素材片段的背景填充
     */
    val backgroundFilling: BackgroundFilling? = null,
    /**
     * 素材片段的转场, 转场一般添加到前一个片段上
     */
    val transitionType: TransitionType? = null,
    /**
     * 素材片段的动画
     */
    val animation: VideoAnimation? = null,
    /**
     * 视频特效列表，支持多种类型的视频特效，同一类型根据resourceId去重
     */
    val videoEffects: List<VideoEffect>? = null,
    /**
     * 视频滤镜列表，支持多种类型的视频滤镜，同一类型根据resourceId去重
     */
    val videoFilters: List<VideoFilter>? = null,
    /**
     * 视频蒙版，一个片段只能有一个蒙版，新蒙版会覆盖旧蒙版
     */
    val videoMask: VideoMask? = null,
    /**
     * 轨道id, 把资源添加到哪个轨道
     */
    val trackId: String? = null,
    /**
     * 素材信息
     */
    val mediaInfo: MediaInfo,
    /**
     * 创建时间
     */
    @JsonIgnore
    val createTime: LocalDateTime,
    /**
     * 更新时间
     */
    @JsonIgnore
    val updateTime: LocalDateTime
)