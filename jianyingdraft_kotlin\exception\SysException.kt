package com.esther.jianyingdraft.exception

/**
 * 系统统一异常，包含错误码和错误信息，支持静态工厂方法快速创建常见异常。
 * <AUTHOR>
 */
class SysException(
    val code: Int,
    override val message: String
) : RuntimeException(message) {
    companion object {
        /** 参数非法 */
        fun invalidParam(msg: String) = SysException(400, msg)
        /** 资源冲突（如已存在） */
        fun conflict(msg: String) = SysException(409, msg)
        /** 未知系统错误 */
        fun systemError(msg: String = "系统异常") = SysException(500, msg)
        /** 未授权 */
        fun unauthorized(msg: String = "未授权") = SysException(401, msg)
        /** 未找到资源 */
        fun notFound(msg: String = "资源未找到") = SysException(404, msg)
    }
} 