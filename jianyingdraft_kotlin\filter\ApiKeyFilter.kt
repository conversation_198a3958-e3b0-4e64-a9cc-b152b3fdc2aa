package com.esther.jianyingdraft.filter

import com.esther.jianyingdraft.domain.rep.DataResponse
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Profile
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.WebFilter
import org.springframework.web.server.WebFilterChain
import reactor.core.publisher.Mono

/**
 * <AUTHOR>
 * @date 2025/7/29
 * @des api-key 过滤器,所有请求必须携带api-key
 */
@Component
@Profile("prod")
class ApiKeyFilter(
    private val mongoTemplate: ReactiveMongoTemplate,
    val objectMapper: ObjectMapper
) : WebFilter {

    // 使用 SLF4j 作为日志门面，推荐在 Kotlin/Java 项目中使用
    private val logger = LoggerFactory.getLogger(ApiKeyFilter::class.java)

    companion object {
        const val API_KEY = "apiKey"
        const val VALID_API_KEY = "476cfb43ba1e45a6ad3e2a8ad28b726e"
    }

    override fun filter(
        exchange: ServerWebExchange,
        chain: WebFilterChain
    ): Mono<Void> {
        val request = exchange.request
        val clientIp = request.remoteAddress?.address?.hostAddress // 获取访问者的 IP 地址
        val apikey = request.headers.getFirst(API_KEY)
        val paramApiKey = request.queryParams.getFirst(API_KEY)
        if (!(paramApiKey.isNullOrEmpty()) && (paramApiKey == VALID_API_KEY) && request.path.value().contains("/export")) {
            return chain.filter(exchange)
        }
        if (!(apikey.isNullOrEmpty()) && (apikey == VALID_API_KEY)) {
            return chain.filter(exchange)
        }

        // --- 以下是返回自定义错误响应的代码 (只有失败时执行) ---
        // 打印访问者的 IP 信息
        logger.warn(
            "API Key validation failed for IP: $clientIp. Provided API Key: ${
                request.headers.getFirst(
                    API_KEY
                ) ?: "Not Provided"
            }"
        )

        val response = exchange.response
        response.statusCode = HttpStatus.UNAUTHORIZED
        response.headers.contentType = MediaType.APPLICATION_JSON
        val errorResponse = DataResponse.error("api-key error, please check your api-key")
        val bufferFactory = response.bufferFactory()
        return try {
            val bytes = objectMapper.writeValueAsBytes(errorResponse)
            val buffer = bufferFactory.wrap(bytes)
            response.writeWith(Mono.just(buffer))
        } catch (e: Exception) {
            logger.error("Failed to write error response for IP: $clientIp", e) // 记录 JSON 序列化失败的错误
            Mono.error(IllegalStateException("Failed to write error response", e))
        }
    }
}