package com.esther.jianyingdraft.resolving;

import jakarta.annotation.Nonnull;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;
import org.springframework.util.StringValueResolver;

public class PlaceholderResolvingStringValueResolver implements StringValueResolver {
    private Environment environment;

    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Override
    public String resolveStringValue(@Nonnull String strVal) {
        if (!StringUtils.hasText(strVal)) {
            return strVal;
        }
        return environment.resolvePlaceholders(strVal);
    }
}