package com.esther.jianyingdraft.rpc

import com.esther.jianyingdraft.domain.rep.DataResponse
import com.esther.jianyingdraft.domain.rep.DraftResultResDto
import com.esther.jianyingdraft.domain.req.DraftCreateReqDto
import org.springframework.stereotype.Component
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange
import org.springframework.web.service.annotation.HttpExchange
import org.springframework.web.service.annotation.PostExchange
import reactor.core.publisher.Mono

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des des
 */
@Component
@HttpExchange("\${jianying.draft.draft-url}/api/v1")
interface DraftScriptRpc {
    @PostExchange("/drafts/export_script")
    fun exportScript(@RequestBody reqDto: DraftResultResDto): Mono<DataResponse<MutableMap<String, Any?>>>


    /**
     * 获取特效
     */
    @GetExchange("/effects/findByType")
    fun effects(
        @RequestParam("effect_type") effectType: String,
        @RequestParam("is_vip") isVip: Boolean?
    ): Mono<DataResponse<List<MutableMap<String, Any?>>>>

    /**
     * 获取特效
     */
    @GetExchange("/effects/all_types")
    fun getAllTypes(): Mono<DataResponse<MutableMap<String, Any?>>>
}