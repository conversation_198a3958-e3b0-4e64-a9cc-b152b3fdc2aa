package com.esther.jianyingdraft.scanner;

import com.esther.jianyingdraft.resolving.PlaceholderResolvingStringValueResolver;
import io.netty.channel.ChannelOption;
import io.netty.channel.ConnectTimeoutException;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import jakarta.annotation.Nonnull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.util.ObjectUtils;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.util.retry.Retry;
import reactor.util.retry.RetryBackoffSpec;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
public class RemoteServiceProxyBean<T> implements FactoryBean<T>, EnvironmentAware {

    private final static ConnectionProvider connectionProvider = ConnectionProvider.builder("rpc-pool")
            .maxConnections(500)
            .pendingAcquireTimeout(Duration.ofSeconds(30))
            .pendingAcquireMaxCount(20000)
            .maxIdleTime(Duration.ofSeconds(5))
            .build();
    private static final Logger log = LoggerFactory.getLogger(RemoteServiceProxyBean.class);

    private final PlaceholderResolvingStringValueResolver placeholderResolver;

    private Class<T> remoteServiceInterface;

    public RemoteServiceProxyBeanCustomizer remoteServiceProxyBeanCustomizer;

    public RemoteServiceProxyBean() {
        placeholderResolver = new PlaceholderResolvingStringValueResolver();
    }

    public RemoteServiceProxyBean(Class<T> remoteServiceInterface) {
        this.remoteServiceInterface = remoteServiceInterface;
        placeholderResolver = new PlaceholderResolvingStringValueResolver();
    }

    public void setRemoteServiceInterface(Class<T> remoteServiceInterface) {
        this.remoteServiceInterface = remoteServiceInterface;
    }

    public Class<T> getRemoteServiceInterface() {
        return remoteServiceInterface;
    }

    @Autowired(required = false)
    public void setRemoteServiceProxyBeanCustomizer(RemoteServiceProxyBeanCustomizer remoteServiceProxyBeanCustomizer) {
        this.remoteServiceProxyBeanCustomizer = remoteServiceProxyBeanCustomizer;
    }

//    @Override
//    public T getObject() {
//        WebClient.Builder webClientBuilder = WebClient.builder()
//                .exchangeStrategies(ExchangeStrategies.builder()
//                        .codecs(configurer ->
//                                configurer.defaultCodecs().maxInMemorySize(1024 * 1024 * 1024)) // 1GB
//                        .build())
//                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
//                .defaultHeader(HttpHeaders.ACCEPT_CHARSET, StandardCharsets.UTF_8.name())
//                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
//        WebClientAdapter webClientAdapter = WebClientAdapter.forClient(webClientBuilder.build());
//        HttpServiceProxyFactory.Builder proxyFactoryBuilder = HttpServiceProxyFactory.builder()
//                .clientAdapter(webClientAdapter)
//                .blockTimeout(Duration.ofMinutes(5))
//                .embeddedValueResolver(placeholderResolver);
//
//        if (!ObjectUtils.isEmpty(remoteServiceProxyBeanCustomizer)) {
//            remoteServiceProxyBeanCustomizer.customize(webClientBuilder, proxyFactoryBuilder);
//        }
//        return proxyFactoryBuilder.build().createClient(remoteServiceInterface);
//    }

    @Override
    public T getObject() {
        HttpClient httpClient = HttpClient.create(connectionProvider)
                .responseTimeout(Duration.ofSeconds(30))
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)
                .doOnConnected(conn -> conn.addHandlerLast(new ReadTimeoutHandler(30, TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(30, TimeUnit.SECONDS)));

        /*
         * 重试机制
         */
        RetryBackoffSpec retryBackoffSpec = Retry.backoff(3, Duration.ofMillis(500))
                .maxBackoff(Duration.ofSeconds(5))
                .filter(this::shouldRetry)
                .doBeforeRetry(retrySignal -> log.warn("Retrying request, attempt {}", retrySignal.totalRetries() + 1));

        WebClient.Builder webClientBuilder = WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .filter((request, next) -> next.exchange(request).retryWhen(retryBackoffSpec))
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(configurer ->
                                configurer.defaultCodecs().maxInMemorySize(1024 * 1024 * 1024)) // 1GB
                        .build())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT_CHARSET, StandardCharsets.UTF_8.name())
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        WebClientAdapter webClientAdapter = WebClientAdapter.create(webClientBuilder.build());
        webClientAdapter.setBlockTimeout(Duration.ofMillis(5000));

        HttpServiceProxyFactory.Builder proxyFactoryBuilder = HttpServiceProxyFactory.builder()
                .exchangeAdapter(webClientAdapter)
                .embeddedValueResolver(placeholderResolver);

        if (!ObjectUtils.isEmpty(remoteServiceProxyBeanCustomizer)) {
            remoteServiceProxyBeanCustomizer.customize(webClientBuilder, proxyFactoryBuilder);
        }
        return proxyFactoryBuilder.build().createClient(remoteServiceInterface);
    }


    public boolean shouldRetry(Throwable throwable) {
        return throwable instanceof WebClientRequestException ||
                throwable instanceof WebClientResponseException ||
                throwable instanceof TimeoutException ||
                throwable instanceof ConnectTimeoutException;
    }


    @Override
    public Class<T> getObjectType() {
        return remoteServiceInterface;
    }

    /**
     * Set the {@code Environment} that this component runs in.
     *
     * @param environment 环境上下文
     */
    @Override
    public void setEnvironment(@Nonnull Environment environment) {
        placeholderResolver.setEnvironment(environment);
    }
}
