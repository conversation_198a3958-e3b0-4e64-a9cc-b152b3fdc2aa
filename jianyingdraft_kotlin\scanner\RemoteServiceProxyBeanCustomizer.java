package com.esther.jianyingdraft.scanner;

import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

@FunctionalInterface
public interface RemoteServiceProxyBeanCustomizer {
    /**
     * 自定义 WebClient.Builder 和 HttpServiceProxyFactory.Builder
     *
     * @param webClientBuilder    WebClient.Builder
     * @param proxyFactoryBuilder HttpServiceProxyFactory.Builder
     */
    void customize(WebClient.Builder webClientBuilder, HttpServiceProxyFactory.Builder proxyFactoryBuilder);
}
