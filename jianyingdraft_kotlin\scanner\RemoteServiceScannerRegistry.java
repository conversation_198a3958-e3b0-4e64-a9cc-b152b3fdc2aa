package com.esther.jianyingdraft.scanner;

import com.esther.jianyingdraft.anno.RemoteServiceScanner;
import jakarta.annotation.Nonnull;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanNameGenerator;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
public class RemoteServiceScannerRegistry implements ImportBeanDefinitionRegistrar {

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata,
                                        @Nonnull BeanDefinitionRegistry registry,
                                        @Nonnull BeanNameGenerator importBeanNameGenerator) {

        AnnotationAttributes annotationAttributes = AnnotationAttributes.fromMap(importingClassMetadata.getAnnotationAttributes(RemoteServiceScanner.class.getName()));
        if (!CollectionUtils.isEmpty(annotationAttributes)) {
            String[] basePackages = annotationAttributes.getStringArray("basePackages");
            if (basePackages.length < 1) {
                basePackages = annotationAttributes.getStringArray("value");
            }
            if (basePackages.length > 0) {
                RemoteCallClassPathScanner scanner = new RemoteCallClassPathScanner(registry);
                scanner.registerFilters();
                scanner.doScan(basePackages);
            }
        }
    }
}
