package com.esther.jianyingdraft.service

import com.esther.jianyingdraft.domain.audio.AudioEffectReqDto
import com.esther.jianyingdraft.domain.req.MediaSegmentAddReqDto
import com.esther.jianyingdraft.domain.req.AudioFadeEffectReqDto
import com.esther.jianyingdraft.domain.req.AudioKeyframeReqDto

/**
 * 音频片段服务接口，定义音频片段相关操作。
 * <AUTHOR>
 */
interface AudioSegmentService {
    /**
     * 添加音频片段到MongoDB
     * @param req 音频片段添加请求参数
     * @return 新增音频片段的id
     */
    suspend fun addAudioSegment(req: MediaSegmentAddReqDto): String

    /**
     * 给音频片段添加或更新淡入淡出特效
     * @param req 音频淡入淡出特效请求参数
     * @return 音频片段id
     */
    suspend fun addAudioFadeEffect(req: AudioFadeEffectReqDto): String

    /**
     * 给音频片段批量添加关键帧
     * @param req 关键帧请求参数，支持一次添加多个关键帧，同一片段的timeOffset和volume组合不能重复
     * @return 音频片段id
     */
    suspend fun addAudioKeyframe(req: AudioKeyframeReqDto): String


    /**
     * 给音频片段添加特效
     * @param req 特效请求参数
     * @return 音频片段id
     */
    suspend fun addAudioEffect( req: AudioEffectReqDto): String
} 