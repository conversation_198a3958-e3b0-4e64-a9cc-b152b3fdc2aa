package com.esther.jianyingdraft.service

import com.esther.jianyingdraft.domain.rep.DraftCreateRepDto
import com.esther.jianyingdraft.domain.req.DraftCreateReqDto
import kotlinx.coroutines.flow.Flow // 引入 Flow
import org.springframework.core.io.buffer.DataBuffer

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @des 草稿服务接口
 */
interface DraftService {
    /**
     * 创建草稿
     */
    suspend fun createDraftScript(reqDto: DraftCreateReqDto): DraftCreateRepDto

    /**
     * 导出草稿为zip压缩包
     * @param draftId 草稿ID
     * @return **表示zip文件内容的 DataBuffer 流**
     */
    suspend fun exportDraftAsZip(draftId: String): Flow<DataBuffer> // 返回类型改为 Flow<DataBuffer>
}