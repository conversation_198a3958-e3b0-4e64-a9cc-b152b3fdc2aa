package com.esther.jianyingdraft.service

import com.esther.jianyingdraft.domain.req.TextSegmentAddReqDto
import com.esther.jianyingdraft.domain.req.TextAnimationAndEffectReqDto

/**
 * 字幕片段服务接口，定义字幕片段相关操作。
 * <AUTHOR>
 */
interface TextSegmentService {
    /**
     * 添加字幕片段到MongoDB
     * @param req 字幕片段添加请求参数
     * @return 新增字幕片段的id
     */
    suspend fun addTextSegment(req: TextSegmentAddReqDto): String

    /**
     * 给字幕片段添加或更新动画和特效
     * @param req 动画和特效请求参数
     * @return 字幕片段id
     */
    suspend fun addOrUpdateTextAnimationAndEffectToSegment(req: TextAnimationAndEffectReqDto): String
} 