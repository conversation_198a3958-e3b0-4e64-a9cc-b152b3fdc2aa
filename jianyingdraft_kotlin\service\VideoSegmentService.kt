package com.esther.jianyingdraft.service

import com.esther.jianyingdraft.domain.req.MediaSegmentAddReqDto
import com.esther.jianyingdraft.domain.req.VideoAnimationReqDto
import com.esther.jianyingdraft.domain.req.TransitionTypeReqDto
import com.esther.jianyingdraft.domain.req.BackgroundFillingReqDto
import com.esther.jianyingdraft.domain.video.VideoEffectReqDto
import com.esther.jianyingdraft.domain.video.VideoFilterReqDto
import com.esther.jianyingdraft.domain.video.VideoMaskReqDto

/**
 * 视频片段服务接口，定义视频片段相关操作。
 * <AUTHOR>
 */
interface VideoSegmentService {
    /**
     * 添加视频片段到MongoDB
     * @param req 视频片段添加请求参数
     * @return 新增视频片段的id
     */
    suspend fun addVideoSegment(req: MediaSegmentAddReqDto): String

    /**
     * 给视频片段添加动画
     * @param req 动画请求参数
     * @return 视频片段id
     */
    suspend fun addAnimation(req: VideoAnimationReqDto): String

    /**
     * 给视频片段添加转场特效
     * @param req 转场请求参数
     * @return 视频片段id
     */
    suspend fun addTransition(req: TransitionTypeReqDto): String

    /**
     * 给视频片段添加背景填充特效
     * @param req 背景填充请求参数
     * @return 视频片段id
     */
    suspend fun addBackgroundFilling(req: BackgroundFillingReqDto): String


    /**
     * 给视频片段添加特效
     * @param req 特效请求参数
     * @return 视频片段id
     */
    suspend fun addEffect(req: VideoEffectReqDto): String


    /**
     * 给视频片段添加滤镜
     * @param req 滤镜请求参数
     * @return 滤镜id
     */
    suspend fun addFilter(req: VideoFilterReqDto): String


    /**
     * 给视频片段添加蒙版, 同一个片段只能有一个蒙版, 如果已经存在用新的覆盖上去
     * @param req 蒙版请求参数
     * @return 蒙版id
     */
    suspend fun addMask(req: VideoMaskReqDto): String
} 