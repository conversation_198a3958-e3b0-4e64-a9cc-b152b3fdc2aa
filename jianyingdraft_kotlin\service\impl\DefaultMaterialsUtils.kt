package com.esther.jianyingdraft.service.impl

import com.esther.jianyingdraft.domain.meterial.MediaInfo
import com.esther.jianyingdraft.exception.SysException
import com.esther.jianyingdraft.service.MaterialsUtils
import com.esther.jianyingdraft.utils.MediaInfoExtractor
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2025/7/30
 * @des 素材工具接口
 */
@Component
class DefaultMaterialsUtils : MaterialsUtils {

    /**
     * 获取媒体信息
     * @param mediaPath 媒体文件路径
     * @return 媒体信息
     */
    override fun mediaInfo(mediaPath: String): MediaInfo {
        val result = MediaInfoExtractor.extractMediaInfo(mediaPath)
        return result.data ?: throw SysException.systemError("无效的媒体文件路径")
    }
}