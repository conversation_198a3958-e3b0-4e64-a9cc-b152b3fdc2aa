package com.esther.jianyingdraft.service.impl

import com.esther.jianyingdraft.domain.rep.*
import com.esther.jianyingdraft.domain.req.DraftCreateReqDto
import com.esther.jianyingdraft.domain.req.TrackAddReqDto
import com.esther.jianyingdraft.entity.*
import com.esther.jianyingdraft.exception.SysException
import com.esther.jianyingdraft.rpc.DraftScriptRpc
import com.esther.jianyingdraft.service.DraftService
import com.esther.jianyingdraft.utils.MetaTemplateLoader
import com.esther.jianyingdraft.utils.NetUtils
import com.fasterxml.jackson.databind.ObjectMapper
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.reactor.awaitSingle
import org.apache.commons.io.IOUtils
import org.slf4j.LoggerFactory
import org.springframework.core.io.buffer.DataBuffer
import org.springframework.core.io.buffer.DataBufferFactory
import org.springframework.core.io.buffer.DefaultDataBufferFactory
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service
import java.io.*
import java.net.URI
import java.time.LocalDateTime
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @description 草稿服务实现类 - 优化后的版本
 */
@Service
class DraftServiceImpl(
    val draftScriptRpc: DraftScriptRpc,
    val mongoTemplate: ReactiveMongoTemplate,
    val objectMapper: ObjectMapper
) : DraftService {

    private val log = LoggerFactory.getLogger(DraftServiceImpl::class.java)
    private val dataBufferFactory: DataBufferFactory = DefaultDataBufferFactory()

    // 优化常量
    companion object {
        private const val BUFFER_SIZE = 8192 // 优化缓冲区大小
        private const val FINISHED_STATUS = "finished"
    }

    /**
     * 创建草稿
     */
    override suspend fun createDraftScript(reqDto: DraftCreateReqDto): DraftCreateRepDto {
        val draftId = UUID.randomUUID().toString().uppercase()

        @Suppress("UNCHECKED_CAST")
        val draftEntity =
            DraftEntity(
                draftId,
                reqDto.name,
                width = reqDto.width,
                height = reqDto.height,
                fps = reqDto.fps,
                draftPath = reqDto.draftPath,
                apiKey = reqDto.apiKey,
            )
        val save = mongoTemplate.insert(draftEntity).awaitSingle()
        return DraftCreateRepDto(save.id, save.draftName, save.draftContent)
    }

    /**
     * 导出草稿为zip压缩包 - 优化版本
     * 优化点：
     * 1. 添加详细的日志记录
     * 2. 优化缓冲区大小
     * 3. 改进错误处理
     * 4. 提取常量配置
     * @param draftId 草稿ID
     * @return 表示zip文件内容的 DataBuffer 流
     */
    override suspend fun exportDraftAsZip(draftId: String): Flow<DataBuffer> {
        log.info("服务层开始以流式方式生成草稿zip内容 (使用Flow)，草稿ID: {}", draftId)

        val query = Query().addCriteria(Criteria.where("draftId").`is`(draftId))

        val draft = mongoTemplate.findById(draftId, DraftEntity::class.java).awaitSingle()
        val tracks = mongoTemplate.find(query, TrackEntity::class.java).collectList().awaitSingle()
        val audioSegments = mongoTemplate.find(query, AudioSegmentEntity::class.java).collectList().awaitSingle()
        val videoSegments = mongoTemplate.find(query, VideoSegmentEntity::class.java).collectList().awaitSingle()
        val textSegments = mongoTemplate.find(query, TextSegmentEntity::class.java).collectList().awaitSingle()

        val draftResult = DraftResultResDto(draftId = draftId, draftName = draft.draftName)

        if (!tracks.isNullOrEmpty()) {
            val trackTypes = tracks.groupBy { it.trackType }
            val videoTracks = mediaTracks(
                trackTypes, videoSegments,
                { trackId, trackName, segments -> VideoTrack(trackId, trackName, segments) },
                { segment, track -> segment.trackId == track.id },
                TrackAddReqDto.TRACK_TYPE_VIDEO
            )
            val audioTracks = mediaTracks(
                trackTypes, audioSegments,
                { trackId, trackName, segments -> AudioTrack(trackId, trackName, segments) },
                { segment, track -> segment.trackId == track.id },
                TrackAddReqDto.TRACK_TYPE_AUDIO
            )
            val textTracks = mediaTracks(
                trackTypes, textSegments,
                { trackId, trackName, segments -> TextTrack(trackId, trackName, segments) },
                { segment, track -> segment.trackId == track.id },
                TrackAddReqDto.TRACK_TYPE_TEXT
            )
            draftResult.apply {
                this.audioTracks = audioTracks
                this.videoTracks = videoTracks
                this.textTracks = textTracks
            }
        }

        val resourcesPath =
            audioSegments.map { it.mediaInfo.absolutePath } + videoSegments.map { it.mediaInfo.absolutePath }

        val rpcResult = draftScriptRpc.exportScript(draftResult).awaitSingle()
        val draftContent = rpcResult.data ?: throw SysException.systemError("导出失败, 没有可导出的草稿内容")

        val metaContent = MetaTemplateLoader.loadMetaTemplate(objectMapper, draft.draftPath, draft.id)

        // 优化：使用copy方法更新实体，避免直接修改
        mongoTemplate.save(
            draft.copy(
                status = FINISHED_STATUS,
                exportCount = draft.exportCount + 1,
                draftContent = draftContent,
                updateTime = LocalDateTime.now()
            )
        ).awaitSingle()


        return channelFlow {
            val pos = PipedOutputStream()
            val pis = PipedInputStream(pos)

            val zipWriterJob = launch(Dispatchers.IO) {
                ZipOutputStream(pos).use { zipOut ->
                    try {
                        // 调用并行写入方法，它现在会处理所有资源的并行下载和顺序写入 ZIP
                        writeZipContentsToStream(
                            zipOut,
                            resourcesPath,
                            draftId,
                            draftContent as Map<String, Any?>,
                            metaContent
                        )
                        log.info("草稿zip内容生成完成，草稿ID: {}", draftId)
                        zipOut.finish()
                        pos.close()
                    } catch (e: Exception) {
                        log.error("以流式方式生成草稿zip内容失败，草稿ID: {}, 错误: {}", draftId, e.message, e)
                        try {
                            pos.close()
                        } catch (ioe: Exception) {
                            log.warn("关闭 PipedOutputStream 失败: {}", ioe.message)
                        }
                        throw SysException.systemError("导出草稿为zip文件失败: ${e.message}")
                    }
                }
            }

            val bufferSize = BUFFER_SIZE // 使用优化后的缓冲区大小
            try {
                withContext(Dispatchers.IO) {
                    val buffer = ByteArray(bufferSize)
                    var bytesRead: Int
                    while (pis.read(buffer).also { bytesRead = it } != -1) {
                        if (bytesRead > 0) {
                            val dataBuffer = dataBufferFactory.wrap(buffer.copyOfRange(0, bytesRead))
                            send(dataBuffer)
                        }
                    }
                }
            } finally {
                try {
                    pis.close()
                    log.debug("PipedInputStream 已关闭，草稿ID: {}", draftId)
                } catch (e: Exception) {
                    log.warn("关闭 PipedInputStream 失败，草稿ID: {}, 错误: {}", draftId, e.message)
                }
                zipWriterJob.join() // 确保 ZIP 写入任务也完成或取消
            }
        }
    }

    // 定义一个数据类来包装下载结果
    private data class ResourceData(
        val zipEntryPath: String,
        val inputStream: InputStream,
        val contentLength: Long = -1, // 用于已知大小的本地文件，-1 表示未知
        val isPlaceholder: Boolean = false // 新增字段，标记是否为占位文件
    )

    // 占位文件内容
    private val PLACEHOLDER_CONTENT = "This file could not be downloaded or found. Original path: "

    /**
     * 创建zip压缩包内容并写入到 OutputStream
     * 此函数负责并行下载资源并顺序写入到 ZipOutputStream
     * @param zipOut ZipOutputStream 对象
     * @param resourcesPath 资源路径列表
     * @param draftId 草稿ID
     * @param draftContent 草稿内容
     * @param metaContent 元数据内容
     */
    private suspend fun writeZipContentsToStream(
        zipOut: ZipOutputStream,
        resourcesPath: List<String>,
        draftId: String,
        draftContent: Map<String, Any?>,
        metaContent: Map<String, Any?>
    ) = withContext(Dispatchers.IO) {
        log.debug("开始创建zip文件内容流，草稿ID: {}, 资源文件数量: {}", draftId, resourcesPath.size)

        val metaJson = objectMapper.writeValueAsString(metaContent)
        addJsonFileToZip(zipOut, "draft_meta_info.json", metaJson)
        log.debug("已添加元数据文件到zip流")

        val contentJson = objectMapper.writeValueAsString(draftContent)
        addJsonFileToZip(zipOut, "draft_content.json", contentJson)
        log.debug("已添加草稿内容文件到zip流")

        // 创建一个通道，用于在并行下载任务和顺序写入 ZIP 任务之间传递数据
        val resourceChannel = Channel<ResourceData>(Channel.BUFFERED)

        // 启动生产者协程：并行下载资源并发送到通道
        val downloadJobs = resourcesPath.map { resourcePath ->
            async {
                val zipResourcesBaseDir = if (draftId.endsWith('/')) draftId else "$draftId/"
                var fileName = ""
                var zipEntryPath: String

                try {
                    fileName = if (NetUtils.isNetworkPath(resourcePath)) {
                        extractFileNameFromUrl(resourcePath)
                    } else {
                        File(resourcePath).name
                    }
                    zipEntryPath = "$zipResourcesBaseDir$fileName"

                    if (NetUtils.isNetworkPath(resourcePath)) {
                        val (inputStream, contentLength) = downloadNetworkResource(resourceUrl = resourcePath)
                        resourceChannel.send(ResourceData(zipEntryPath, inputStream, contentLength))
                    } else {
                        val resourceFile = File(resourcePath)
                        if (resourceFile.exists() && resourceFile.isFile) {
                            resourceChannel.send(
                                ResourceData(
                                    zipEntryPath,
                                    resourceFile.inputStream(),
                                    resourceFile.length()
                                )
                            )
                        } else {
                            // 本地文件不存在，发送占位文件
                            log.warn("本地资源文件不存在或不是文件，将生成占位文件: {}", resourcePath)
                            val placeholderFileName = "下载错误-${fileName}.txt"
                            val placeholderZipEntryPath = "$zipResourcesBaseDir$placeholderFileName"
                            val placeholderContent = "${PLACEHOLDER_CONTENT}${resourcePath}".toByteArray(Charsets.UTF_8)
                            resourceChannel.send(
                                ResourceData(
                                    placeholderZipEntryPath,
                                    ByteArrayInputStream(placeholderContent),
                                    placeholderContent.size.toLong(),
                                    isPlaceholder = true
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    log.error("并行下载或准备资源失败，将生成占位文件: {}, 错误: {}", resourcePath, e.message)
                    // 下载失败，发送占位文件
                    val placeholderFileName = "下载错误-${fileName.ifEmpty { "未知文件" }}.txt" // 如果fileName为空，提供默认值
                    val placeholderZipEntryPath = "$zipResourcesBaseDir$placeholderFileName"
                    val placeholderContent =
                        "${PLACEHOLDER_CONTENT}${resourcePath}, Error: ${e.message}".toByteArray(Charsets.UTF_8)
                    resourceChannel.send(
                        ResourceData(
                            placeholderZipEntryPath,
                            ByteArrayInputStream(placeholderContent),
                            placeholderContent.size.toLong(),
                            isPlaceholder = true
                        )
                    )
                }
            }
        }

        downloadJobs.awaitAll()
        resourceChannel.close()

        var successCount = 0
        var failureCount = 0
        for (resourceData in resourceChannel) {
            try {
                val zipEntry = ZipEntry(resourceData.zipEntryPath)
                if (resourceData.contentLength != -1L) {
                    zipEntry.size = resourceData.contentLength
                }
                zipOut.putNextEntry(zipEntry)
                resourceData.inputStream.use {
                    val bytesCopied = IOUtils.copy(it, zipOut)
                    log.debug(
                        "写入zipEntry: {} -> {} bytes (是否占位文件: {})",
                        resourceData.zipEntryPath,
                        bytesCopied,
                        resourceData.isPlaceholder
                    )
                }
                zipOut.closeEntry()
                if (resourceData.isPlaceholder) {
                    failureCount++ // 占位文件也算作失败的资源
                } else {
                    successCount++
                }
            } catch (e: Exception) {
                log.error("写入zipEntry失败: {}, 错误: {}", resourceData.zipEntryPath, e.message)
                failureCount++
            } finally {
                try {
                    resourceData.inputStream.close()
                } catch (e: Exception) { /* ignore */
                }
            }
        }

        log.info(
            "资源文件处理完成，成功: {}, 失败: {}, 总计: {}",
            successCount,
            failureCount,
            resourcesPath.size
        )
    }

    /**
     * 添加JSON文件到zip压缩包
     * @param zipOut zip输出流
     * @param fileName 文件名
     * @param jsonContent JSON内容
     */
    private fun addJsonFileToZip(zipOut: ZipOutputStream, fileName: String, jsonContent: String) {
        val zipEntry = ZipEntry(fileName)
        zipOut.putNextEntry(zipEntry)
        zipOut.write(jsonContent.toByteArray(Charsets.UTF_8))
        zipOut.closeEntry()
    }

    /**
     * 从URL中提取文件名
     * @param url URL地址
     * @return 文件名
     */
    private fun extractFileNameFromUrl(url: String): String {
        return try {
            val uri = URI(url)
            val path = uri.path
            val fileName = path.substringAfterLast('/')
            fileName.ifBlank {
                "resource_${System.currentTimeMillis()}"
            }
        } catch (_: Exception) {
            log.warn("无法从URL提取文件名: {}, 使用默认名称", url)
            "resource_${System.currentTimeMillis()}"
        }
    }

    /**
     * 并行下载网络资源并返回 InputStream 和 Content-Length
     * @param resourceUrl 网络资源URL
     * @return Pair<InputStream, Long>，其中 Long 是内容长度，-1 表示未知
     * @throws Exception 当下载失败时抛出异常
     */
    private suspend fun downloadNetworkResource(resourceUrl: String): Pair<InputStream, Long> =
        withContext(Dispatchers.IO) {
            log.debug("开始下载网络资源: {}", resourceUrl)

            val url = URI.create(resourceUrl).toURL()
            val connection = url.openConnection()

            connection.connectTimeout = 30000
            connection.readTimeout = 60000

            connection.setRequestProperty(
                "User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            connection.setRequestProperty("Accept", "*/*")
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
            connection.setRequestProperty("Cache-Control", "no-cache")

            val contentLength = connection.contentLengthLong // 获取内容长度，可能为 -1
            val inputStream = connection.inputStream // 获取输入流

            log.debug("网络资源下载器连接完成: {}, 大小: {} bytes", resourceUrl, contentLength)
            Pair(inputStream, contentLength)
        }

    private fun <T, R> mediaTracks(
        tracks: Map<String, List<TrackEntity>>,
        audioSegments: List<T>,
        buildTrack: (String, String, List<T>) -> R,
        filter: (T, TrackEntity) -> Boolean,
        mediaType: String
    ): List<R> {
        val entities = tracks[mediaType]
        if (entities.isNullOrEmpty()) {
            return emptyList()
        }
        if (entities.size == 1) {
            return listOf(
                buildTrack(
                    entities[0].id,
                    entities[0].trackName,
                    audioSegments
                )
            )
        }
        return entities.map {
            buildTrack(
                it.id,
                it.trackName,
                audioSegments.filter { media -> filter(media, it) }
            )
        }
    }
}