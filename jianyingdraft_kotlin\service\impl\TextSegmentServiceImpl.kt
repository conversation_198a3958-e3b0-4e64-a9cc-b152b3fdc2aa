package com.esther.jianyingdraft.service.impl

import com.esther.jianyingdraft.domain.req.TextSegmentAddReqDto
import com.esther.jianyingdraft.domain.text.TextBackground
import com.esther.jianyingdraft.domain.text.TextBorder
import com.esther.jianyingdraft.domain.text.TextStyle
import com.esther.jianyingdraft.domain.req.TextAnimationAndEffectReqDto
import com.esther.jianyingdraft.domain.req.TextAnimationAndEffect
import com.esther.jianyingdraft.entity.TextSegmentEntity
import com.esther.jianyingdraft.service.TextSegmentService
import com.esther.jianyingdraft.utils.DraftUtils
import com.esther.jianyingdraft.utils.TimeUtils
import kotlinx.coroutines.reactive.awaitSingle
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import kotlinx.coroutines.reactor.awaitSingleOrNull

/**
 * 字幕片段服务实现类，实现字幕片段相关操作。
 * <AUTHOR>
 */
@Service
class TextSegmentServiceImpl @Autowired constructor(
    private val reactiveMongoTemplate: ReactiveMongoTemplate
) : TextSegmentService {
    private val logger = LoggerFactory.getLogger(TextSegmentServiceImpl::class.java)

    companion object{
        const val DEFAULT_DURATION = "3s"
    }

    /**
     * 添加或更新字幕片段到MongoDB
     * 使用模板方法模式，根据是否存在textSegmentId决定执行新增或更新操作
     * 
     * @param req 字幕片段添加请求参数
     * @return 字幕片段的id
     * @throws IllegalArgumentException 当请求参数无效时
     * <AUTHOR>
     */
    override suspend fun addTextSegment(req: TextSegmentAddReqDto): String {
        logger.info(
            "开始处理字幕片段请求，操作类型={}, draftId={}, textSegmentId={}, text={}, afterSegmentId={}",
            if (req.textSegmentId != null) "更新" else "新增",
            req.draftId,
            req.textSegmentId,
            req.text.take(50), // 限制日志中文本长度
            req.afterSegmentId
        )
        
        // 前置检查：验证draftId权限
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        
        // 根据是否存在textSegmentId决定操作类型
        return if (req.textSegmentId != null) {
            processUpdateOperation(req)
        } else {
            processCreateOperation(req)
        }
    }

    /**
     * 处理字幕片段更新操作
     * 
     * @param req 字幕片段更新请求参数
     * @return 更新后的字幕片段id
     * @throws IllegalArgumentException 当片段不存在或参数无效时
     * <AUTHOR>
     */
    private suspend fun processUpdateOperation(req: TextSegmentAddReqDto): String {
        logger.debug("执行字幕片段更新操作，textSegmentId={}", req.textSegmentId)
        
        val existingSegment = findExistingSegment(req.draftId, req.textSegmentId!!)
            ?: throw IllegalArgumentException("字幕片段不存在，无法更新")
        
        logger.info("找到待更新字幕片段，id={}, 原文本={}", existingSegment.id, existingSegment.text.take(50))
        
        // 合并样式信息
        val mergedStyleData = mergeSegmentStyles(existingSegment, req)
        
        // 计算更新后的时间范围
        val updatedTimerange = calculateUpdatedTimerange(req, existingSegment)
        
        // 执行更新操作
        performSegmentUpdate(req, existingSegment, mergedStyleData, updatedTimerange)
        
        logger.info("字幕片段更新成功，id={}", existingSegment.id)
        return existingSegment.id
    }

    /**
     * 处理字幕片段创建操作
     * 
     * @param req 字幕片段创建请求参数
     * @return 新创建字幕片段的id
     * @throws IllegalArgumentException 当参数无效或发生时间重叠时
     * <AUTHOR>
     */
    private suspend fun processCreateOperation(req: TextSegmentAddReqDto): String {
        logger.debug("执行字幕片段创建操作")
        
        val newSegmentId = ObjectId().toHexString()
        
        // 计算新片段的完整时间范围
        val completeTimerange = calculateCompleteTimerange(req)
        
        // 执行重叠检测
        validateTimerangeOverlap(req.draftId, req.trackId, completeTimerange, null)
        
        // 创建并保存新片段
        val newSegment = createNewSegmentEntity(req, newSegmentId, completeTimerange)
        reactiveMongoTemplate.save(newSegment).awaitSingle()
        
        logger.info(
            "字幕片段创建成功，id={}, 最终时间范围: start={}, duration={}",
            newSegmentId, completeTimerange?.start, completeTimerange?.duration
        )
        return newSegmentId
    }

    /**
     * 查找现有的字幕片段
     * 
     * @param draftId 草稿ID
     * @param segmentId 片段ID
     * @return 现有片段实体，如果不存在则返回null
     * <AUTHOR>
     */
    private suspend fun findExistingSegment(draftId: String, segmentId: String): TextSegmentEntity? {
        val query = Query()
            .addCriteria(Criteria.where("id").`is`(segmentId))
            .addCriteria(Criteria.where("draftId").`is`(draftId))
        return reactiveMongoTemplate.findOne(query, TextSegmentEntity::class.java).awaitSingleOrNull()
    }

    /**
     * 合并片段样式数据
     * 整合现有样式和新样式信息，包括文本样式、边框样式和背景样式
     * 
     * @param existingSegment 现有片段实体
     * @param req 更新请求参数
     * @return 合并后的样式数据
     * <AUTHOR>
     */
    private fun mergeSegmentStyles(existingSegment: TextSegmentEntity, req: TextSegmentAddReqDto): MergedStyleData {
        val mergedStyle = mergeTextStyle(existingSegment.style, req.style)
        val mergedBorder = mergeTextBorder(existingSegment.border, req.border)
        val mergedBackground = mergeTextBackground(existingSegment.background, req.background)
        
        logger.debug("样式合并完成，样式更新={}, 边框更新={}, 背景更新={}", 
            req.style != null, req.border != null, req.background != null)
        
        return MergedStyleData(mergedStyle, mergedBorder, mergedBackground)
    }

    /**
     * 计算更新操作中的时间范围
     * 如果请求中提供了新的时间范围，则计算完整时间范围并进行重叠检测
     * 
     * @param req 更新请求参数
     * @param existingSegment 现有片段实体
     * @return 计算后的时间范围
     * <AUTHOR>
     */
    private suspend fun calculateUpdatedTimerange(
        req: TextSegmentAddReqDto, 
        existingSegment: TextSegmentEntity
    ): com.esther.jianyingdraft.domain.Timerange? {
        return if (req.targetRanger != null) {
            logger.debug("检测到时间范围更新请求")
            
            val completeTimerange = DraftUtils.buildCompleteTargetTimerange(
                originalTimerange = req.targetRanger,
                afterSegmentId = req.afterSegmentId,
                calculatedDuration = req.targetRanger.duration ?: DEFAULT_DURATION,
                mongoTemplate = reactiveMongoTemplate
            )
            
            logger.debug(
                "更新片段时间范围计算完成: start={}, duration={}",
                completeTimerange.start, completeTimerange.duration
            )
            
            // 执行重叠检测（排除当前片段）
            validateTimerangeOverlap(
                req.draftId, 
                req.trackId ?: existingSegment.trackId, 
                completeTimerange, 
                req.textSegmentId
            )
            
            completeTimerange
        } else {
            logger.debug("时间范围保持不变")
            existingSegment.targetRanger
        }
    }

    /**
     * 执行片段更新操作
     * 构建更新查询并执行数据库更新操作
     * 
     * @param req 更新请求参数
     * @param existingSegment 现有片段实体
     * @param mergedStyleData 合并后的样式数据
     * @param updatedTimerange 更新后的时间范围
     * <AUTHOR>
     */
    private suspend fun performSegmentUpdate(
        req: TextSegmentAddReqDto,
        existingSegment: TextSegmentEntity,
        mergedStyleData: MergedStyleData,
        updatedTimerange: com.esther.jianyingdraft.domain.Timerange?
    ) {
        val query = Query()
            .addCriteria(Criteria.where("id").`is`(req.textSegmentId))
            .addCriteria(Criteria.where("draftId").`is`(req.draftId))
        
        val update = Update()
            .set("text", req.text)
            .set("font", req.font ?: existingSegment.font)
            .set("style", mergedStyleData.style)
            .set("border", mergedStyleData.border)
            .set("clipSettings", req.clipSettings)
            .set("background", mergedStyleData.background)
            .set("targetRanger", updatedTimerange)
            .set("realTargetRanger", updatedTimerange?.let { TimeUtils.calculateTimeline(it) })
            .set("updateTime", LocalDateTime.now())
        
        reactiveMongoTemplate.updateFirst(query, update, TextSegmentEntity::class.java).awaitSingle()
        
        logger.debug("数据库更新操作执行完成")
    }

    /**
     * 计算新片段的完整时间范围
     * 根据请求参数和afterSegmentId计算新片段的时间范围
     * 
     * @param req 创建请求参数
     * @return 计算后的完整时间范围
     * <AUTHOR>
     */
    private suspend fun calculateCompleteTimerange(req: TextSegmentAddReqDto): com.esther.jianyingdraft.domain.Timerange? {
        return when {
            req.targetRanger != null -> {
                logger.debug("根据提供的时间范围计算完整时间范围")
                val targetDuration = req.targetRanger.duration?.takeIf { it.isNotBlank() } ?: DEFAULT_DURATION
                DraftUtils.buildCompleteTargetTimerange(
                    originalTimerange = req.targetRanger,
                    afterSegmentId = req.afterSegmentId,
                    calculatedDuration = targetDuration,
                    mongoTemplate = reactiveMongoTemplate
                )
            }
            req.afterSegmentId != null -> {
                logger.debug("仅根据afterSegmentId计算时间范围")
                DraftUtils.buildCompleteTargetTimerange(
                    originalTimerange = com.esther.jianyingdraft.domain.Timerange(start = null, duration = DEFAULT_DURATION),
                    afterSegmentId = req.afterSegmentId,
                    calculatedDuration = DEFAULT_DURATION,
                    mongoTemplate = reactiveMongoTemplate
                )
            }
            else -> {
                logger.debug("使用原始时间范围")
                req.targetRanger
            }
        }.also { timerange ->
            logger.debug("完整时间范围计算完成: start={}, duration={}", timerange?.start, timerange?.duration)
        }
    }

    /**
     * 验证时间范围重叠
     * 检查指定时间范围是否与同轨道的其他文本片段发生重叠
     * 
     * @param draftId 草稿ID
     * @param trackId 轨道ID
     * @param timerange 待检查的时间范围
     * @param excludeSegmentId 需要排除检查的片段ID（用于更新操作）
     * <AUTHOR>
     */
    private suspend fun validateTimerangeOverlap(
        draftId: String,
        trackId: String?,
        timerange: com.esther.jianyingdraft.domain.Timerange?,
        excludeSegmentId: String?
    ) {
        if (timerange != null) {
            logger.debug("执行时间范围重叠检测，trackId={}, excludeSegmentId={}", trackId, excludeSegmentId)
            DraftUtils.checkSegmentOverlapInTrack(
                draftId = draftId,
                trackId = trackId,
                newSegmentTimerange = timerange,
                segmentType = "text",
                excludeSegmentId = excludeSegmentId,
                mongoTemplate = reactiveMongoTemplate
            )
            logger.debug("时间范围重叠检测通过")
        } else {
            logger.debug("时间范围为空，跳过重叠检测")
        }
    }

    /**
     * 创建新的片段实体
     * 根据请求参数构建新的TextSegmentEntity对象
     * 
     * @param req 创建请求参数
     * @param segmentId 新片段的ID
     * @param timerange 片段的时间范围
     * @return 新创建的片段实体
     * <AUTHOR>
     */
    private fun createNewSegmentEntity(
        req: TextSegmentAddReqDto,
        segmentId: String,
        timerange: com.esther.jianyingdraft.domain.Timerange?
    ): TextSegmentEntity {
        val now = LocalDateTime.now()
        
        return TextSegmentEntity(
            id = segmentId,
            text = req.text,
            draftId = req.draftId,
            font = req.font,
            style = req.style,
            border = req.border,
            background = req.background,
            createTime = now,
            trackId = req.trackId,
            targetRanger = timerange,
            realTargetRanger = timerange?.let { TimeUtils.calculateTimeline(it) },
            updateTime = now
        ).apply {
            // 设置剪辑配置（如果提供）
            req.clipSettings?.let { clipSettings = it }
        }
    }

    /**
     * 合并样式数据的数据类
     * 用于封装合并后的样式信息
     * 
     * @property style 合并后的文本样式
     * @property border 合并后的边框样式
     * @property background 合并后的背景样式
     * <AUTHOR>
     */
    private data class MergedStyleData(
        val style: TextStyle?,
        val border: TextBorder?,
        val background: TextBackground?
    )

    /**
     * 合并TextStyle对象，属性不同则用新值，否则保留旧值
     * 使用Elvis操作符简化条件判断，提高代码可读性
     * 
     * @param old 原有的文本样式
     * @param new 新的文本样式
     * @return 合并后的文本样式，如果两者都为null则返回null
     * <AUTHOR>
     */
    private fun mergeTextStyle(old: TextStyle?, new: TextStyle?): TextStyle? {
        if (new == null) return old
        if (old == null) return new
        
        return TextStyle(
            size = new.size.takeIf { it != old.size } ?: old.size,
            bold = new.bold.takeIf { it != old.bold } ?: old.bold,
            italic = new.italic.takeIf { it != old.italic } ?: old.italic,
            underline = new.underline.takeIf { it != old.underline } ?: old.underline,
            color = new.color.takeIf { it != old.color } ?: old.color,
            alpha = new.alpha.takeIf { it != old.alpha } ?: old.alpha,
            align = new.align.takeIf { it != old.align } ?: old.align,
            vertical = new.vertical.takeIf { it != old.vertical } ?: old.vertical,
            letterSpacing = new.letterSpacing.takeIf { it != old.letterSpacing } ?: old.letterSpacing,
            lineSpacing = new.lineSpacing.takeIf { it != old.lineSpacing } ?: old.lineSpacing,
            autoWrapping = new.autoWrapping.takeIf { it != old.autoWrapping } ?: old.autoWrapping,
            maxLineWidth = new.maxLineWidth.takeIf { it != old.maxLineWidth } ?: old.maxLineWidth
        )
    }

    /**
     * 合并TextBorder对象，属性不同则用新值，否则保留旧值
     * 使用Elvis操作符简化条件判断，提高代码可读性
     * 
     * @param old 原有的边框样式
     * @param new 新的边框样式
     * @return 合并后的边框样式，如果两者都为null则返回null
     * <AUTHOR>
     */
    private fun mergeTextBorder(old: TextBorder?, new: TextBorder?): TextBorder? {
        if (new == null) return old
        if (old == null) return new
        
        return TextBorder(
            alpha = new.alpha.takeIf { it != old.alpha } ?: old.alpha,
            color = new.color.takeIf { it != old.color } ?: old.color
            // width 属性为只读，不能直接赋值
        )
    }

    /**
     * 合并TextBackground对象，属性不同则用新值，否则保留旧值
     * 使用Elvis操作符简化条件判断，提高代码可读性
     * 
     * @param old 原有的背景样式
     * @param new 新的背景样式
     * @return 合并后的背景样式，如果两者都为null则返回null
     * <AUTHOR>
     */
    private fun mergeTextBackground(old: TextBackground?, new: TextBackground?): TextBackground? {
        if (new == null) return old
        if (old == null) return new
        
        return TextBackground(
            style = new.style.takeIf { it != old.style } ?: old.style,
            alpha = new.alpha.takeIf { it != old.alpha } ?: old.alpha,
            color = new.color.takeIf { it != old.color } ?: old.color,
            roundRadius = new.roundRadius.takeIf { it != old.roundRadius } ?: old.roundRadius,
            height = new.height.takeIf { it != old.height } ?: old.height,
            width = new.width.takeIf { it != old.width } ?: old.width
            // horizontal/vertical 为private，offset 为只读属性
        )
    }

    /**
     * 给字幕片段添加动画和特效到列表中
     * @param req 动画和特效请求参数
     * @return 字幕片段id
     */
    override suspend fun addOrUpdateTextAnimationAndEffectToSegment(req: TextAnimationAndEffectReqDto): String {
        logger.info("开始为字幕片段添加动画和特效，textSegmentId={}, draftId={}", req.textSegmentId, req.draftId)
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        val query = Query()
            .addCriteria(Criteria.where("id").`is`(req.textSegmentId))
            .addCriteria(Criteria.where("draftId").`is`(req.draftId))
        val old = reactiveMongoTemplate.findOne(query, TextSegmentEntity::class.java).awaitSingleOrNull()
        if (old == null) {
            logger.warn("字幕片段不存在，textSegmentId={}", req.textSegmentId)
            throw IllegalArgumentException("字幕片段不存在")
        }
        val updatedList = addToTextAnimationAndEffectList(old.textAnimationAndEffects, req)
        val update = Update()
            .set("textAnimationAndEffects", updatedList)
            .set("updateTime", LocalDateTime.now())
        reactiveMongoTemplate.updateFirst(query, update, TextSegmentEntity::class.java).awaitSingle()
        logger.info("字幕片段动画和特效添加成功，textSegmentId={}", req.textSegmentId)
        return req.textSegmentId
    }

    /**
     * 将新的动画和特效添加到列表中
     * 采用函数式编程风格，创建新列表而非修改原列表
     * 
     * @param oldList 现有的动画和特效列表
     * @param req 动画和特效请求参数
     * @return 包含新动画特效的列表
     * <AUTHOR>
     */
    private fun addToTextAnimationAndEffectList(
        oldList: List<TextAnimationAndEffect>,
        req: TextAnimationAndEffectReqDto
    ): List<TextAnimationAndEffect> {
        val newEffect = TextAnimationAndEffect(
            type = req.type,
            duration = req.duration,
            bubbleEffectId = req.bubbleEffectId,
            bubbleResourceId = req.bubbleResourceId,
            flowerEffectId = req.flowerEffectId
        )
        
        logger.debug("添加新的文本动画特效，类型={}, 时长={}", req.type, req.duration)
        return oldList + newEffect
    }
} 