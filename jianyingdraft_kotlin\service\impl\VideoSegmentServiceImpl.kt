package com.esther.jianyingdraft.service.impl

import com.esther.jianyingdraft.domain.Timerange
import com.esther.jianyingdraft.domain.meterial.MediaInfo
import com.esther.jianyingdraft.domain.req.*
import com.esther.jianyingdraft.domain.video.*
import com.esther.jianyingdraft.entity.VideoSegmentEntity
import com.esther.jianyingdraft.exception.SysException
import com.esther.jianyingdraft.service.VideoSegmentService
import com.esther.jianyingdraft.utils.DraftUtils
import com.esther.jianyingdraft.utils.MediaInfoExtractor
import com.esther.jianyingdraft.utils.TimeUtils
import kotlinx.coroutines.reactive.awaitSingle
import kotlinx.coroutines.reactor.awaitSingleOrNull
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 视频片段服务实现类，实现视频片段相关操作。
 * <AUTHOR>
 */
@Service
class VideoSegmentServiceImpl @Autowired constructor(
    private val reactiveMongoTemplate: ReactiveMongoTemplate
) : VideoSegmentService {
    private val logger = LoggerFactory.getLogger(VideoSegmentServiceImpl::class.java)


    /**
     * 添加视频片段到MongoDB
     * @param req 视频片段添加请求参数
     * @return 新增视频片段的id
     */
    override suspend fun addVideoSegment(req: MediaSegmentAddReqDto): String {
        logger.info(
            "开始添加视频片段，draftId={}, resourcePath={}, afterSegmentId={}",
            req.draftId, req.resourcePath, req.afterSegmentId
        )

        if (req.resourcePath.isBlank()) {
            logger.warn("视频片段资源路径不能为空")
            throw SysException.invalidParam("视频片段资源路径不能为空")
        }

        // 权限检查
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)

        // 提取媒体信息
        val extractMediaInfo = MediaInfoExtractor.extractMediaInfo(req.resourcePath)
        if (!extractMediaInfo.success || extractMediaInfo.data == null) {
            logger.warn("视频片段资源路径无效，请检查资源是否合法")
            throw SysException.invalidParam("视频片段资源无效")
        }

        // 使用TimeUtils.handDuration计算实际的duration
        val durationCalculatedTimerange = TimeUtils.handDuration(req.targetTimerange, extractMediaInfo.data)

        // 构建完整的目标时间范围（支持afterSegmentId自动计算start时间）
        val completeTargetTimerange = DraftUtils.buildCompleteTargetTimerange(
            originalTimerange = req.targetTimerange,
            afterSegmentId = req.afterSegmentId,
            calculatedDuration = durationCalculatedTimerange.duration!!,
            mongoTemplate = reactiveMongoTemplate
        )

        logger.info(
            "完整的目标时间范围: start={}, duration={}",
            completeTargetTimerange.start, completeTargetTimerange.duration
        )

        // 检查同一轨道上是否存在重叠片段
        DraftUtils.checkSegmentOverlapInTrack(
            draftId = req.draftId,
            trackId = req.trackId,
            newSegmentTimerange = completeTargetTimerange,
            segmentType = "video",
            excludeSegmentId = null, // 新增操作，不排除任何片段
            mongoTemplate = reactiveMongoTemplate
        )

        val id = ObjectId().toHexString()
        val now = LocalDateTime.now()
        val entity = VideoSegmentEntity(
            id = id,
            draftId = req.draftId,
            targetTimerange = completeTargetTimerange,
            sourceTimerange = req.sourceTimerange,
            speed = req.speed,
            realTargetTimerange = completeTargetTimerange.let { TimeUtils.calculateTimeline(it) },
            realSourceTimerange = req.sourceTimerange?.let { TimeUtils.calculateTimeline(it) },
            volume = req.volume,
            trackId = req.trackId,
            resourcePath = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\${req.draftId}\\${extractMediaInfo.data.fileName}",
            mediaInfo = extractMediaInfo.data,
            createTime = now,
            updateTime = now
        ).apply {
            if (req.clipSettings != null) {
                clipSettings = req.clipSettings
            }
        }
        reactiveMongoTemplate.save(entity).awaitSingle()
        logger.info(
            "视频片段添加成功，id={}, 最终时间范围: start={}, duration={}",
            id, completeTargetTimerange.start, completeTargetTimerange.duration
        )
        return id
    }

    /**
     * 给视频片段添加动画
     * @param req 动画请求参数
     * @return 视频片段id
     */
    override suspend fun addAnimation(req: VideoAnimationReqDto): String {
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        return updateVideoSegmentField(
            segmentId = req.videoSegmentId,
            fieldName = "animation",
            fieldValue = VideoAnimation(type = req.type, duration = req.duration),
            operationName = "添加动画",
            draftId = req.draftId,
            logParams = mapOf("type" to req.type.resourceId)
        )
    }

    /**
     * 给视频片段添加转场特效
     * @param req 转场请求参数
     * @return 视频片段id
     */
    override suspend fun addTransition(req: TransitionTypeReqDto): String {
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        // 先检查是否已存在转场特效
        val segment = validateAndGetSegment(req.videoSegmentId)
        if (segment.transitionType != null) {
            logger.warn("该视频片段已存在转场特效，videoSegmentId={}", req.videoSegmentId)
            throw SysException.conflict("该视频片段已存在转场特效")
        }

        return updateVideoSegmentField(
            segmentId = req.videoSegmentId,
            fieldName = "transitionType",
            fieldValue = TransitionType(transitionType = req.transitionType, duration = req.duration),
            operationName = "添加转场特效",
            draftId = req.draftId,
            logParams = mapOf("type" to req.transitionType.resourceId)
        )
    }

    /**
     * 给视频片段添加背景填充特效
     * @param req 背景填充请求参数
     * @return 视频片段id
     */
    override suspend fun addBackgroundFilling(req: BackgroundFillingReqDto): String {
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        // 先检查是否已存在背景填充特效
        val segment = validateAndGetSegment(req.videoSegmentId)
        if (segment.backgroundFilling != null) {
            logger.warn("该视频片段已存在背景填充特效，videoSegmentId={}", req.videoSegmentId)
            throw SysException.conflict("该视频片段已存在背景填充特效")
        }

        return updateVideoSegmentField(
            segmentId = req.videoSegmentId,
            fieldName = "backgroundFilling",
            fieldValue = BackgroundFilling(fillType = req.fillType, blur = req.blur, color = req.color),
            operationName = "添加背景填充特效",
            draftId = req.draftId,
            logParams = mapOf("fillType" to req.fillType)
        )
    }

    /**
     * 给视频片段添加特效
     * @param req 特效请求参数
     * @return 视频片段id
     * @throws SysException 当视频片段不存在或请求参数无效时抛出异常
     */
    override suspend fun addEffect(req: VideoEffectReqDto): String {
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        validateBatchRequest(req.effects, "视频特效", req.segmentId)

        logBatchOperationStart("添加特效", req.draftId, req.segmentId, req.effects.size)
        logEffectDetails(req.effects)

        val segment = validateAndGetSegment(req.segmentId)
        val existingEffects = segment.videoEffects ?: emptyList()

        logger.info("当前视频片段已有特效数量={}", existingEffects.size)

        val mergedEffects = mergeResourceBasedItems(
            existingItems = existingEffects,
            newItems = req.effects,
            getResourceId = { it.effectType.resourceId },
            itemTypeName = "特效"
        )

        logBatchOperationResult(mergedEffects, "特效") { effect ->
            "resourceId=${effect.effectType.resourceId}, resourceName=${effect.effectType.resourceName}, params=${effect.params}"
        }

        updateSegmentField(req.segmentId, "videoEffects", mergedEffects)

        logger.info("视频片段特效添加成功，segmentId={}", req.segmentId)
        return req.segmentId
    }

    /**
     * 给视频片段添加滤镜
     * @param req 滤镜请求参数
     * @return 视频片段id
     * @throws SysException 当视频片段不存在或请求参数无效时抛出异常
     */
    override suspend fun addFilter(req: VideoFilterReqDto): String {
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        validateBatchRequest(req.filters, "视频滤镜", req.segmentId)

        logBatchOperationStart("添加滤镜", req.draftId, req.segmentId, req.filters.size)
        logFilterDetails(req.filters)

        val segment = validateAndGetSegment(req.segmentId)
        val existingFilters = segment.videoFilters ?: emptyList()

        logger.info("当前视频片段已有滤镜数量={}", existingFilters.size)

        val mergedFilters = mergeResourceBasedItems(
            existingItems = existingFilters,
            newItems = req.filters,
            getResourceId = { it.filterType.resourceId },
            itemTypeName = "滤镜"
        )

        logBatchOperationResult(mergedFilters, "滤镜") { filter ->
            "resourceId=${filter.filterType.resourceId}, resourceName=${filter.filterType.resourceName}, intensity=${filter.intensity}"
        }

        updateSegmentField(req.segmentId, "videoFilters", mergedFilters)

        logger.info("视频片段滤镜添加成功，segmentId={}", req.segmentId)
        return req.segmentId
    }

    /**
     * 给视频片段添加蒙版
     * @param req 蒙版请求参数
     * @return 视频片段id
     * @throws SysException 当视频片段不存在时抛出异常
     */
    override suspend fun addMask(req: VideoMaskReqDto): String {
        logger.info(
            "开始为视频片段添加蒙版，draftId={}, segmentId={}, maskType={}",
            req.draftId,
            req.segmentId,
            req.maskType.resourceId
        )
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)

        logger.info(
            "蒙版参数详情：centerX={}, centerY={}, size={}, rotation={}, feather={}, invert={}, rectWidth={}, roundCorner={}",
            req.centerX, req.centerY, req.size, req.rotation, req.feather, req.invert, req.rectWidth, req.roundCorner
        )

        val currentSegment = validateAndGetSegment(req.segmentId)

        // 检查是否已有蒙版
        if (currentSegment.videoMask != null) {
            logger.info("该视频片段已有蒙版，将使用新蒙版覆盖，segmentId={}", req.segmentId)
        }

        // 构造新蒙版对象
        val newMask = VideoMask(
            maskType = req.maskType,
            centerX = req.centerX,
            centerY = req.centerY,
            size = req.size,
            rotation = req.rotation,
            feather = req.feather,
            invert = req.invert,
            rectWidth = req.rectWidth,
            roundCorner = req.roundCorner
        )

        updateSegmentField(req.segmentId, "videoMask", newMask)

        logger.info("视频片段蒙版添加/覆盖成功，segmentId={}", req.segmentId)
        return req.segmentId
    }

    // =========================== 私有辅助方法 ===========================

    /**
     * 构建查询条件
     * @param segmentId 片段ID
     * @return 查询对象
     */
    private fun buildSegmentQuery(segmentId: String): Query = Query(Criteria.where("id").`is`(segmentId))

    /**
     * 验证视频片段是否存在并返回实体
     * @param segmentId 视频片段ID
     * @throws SysException 当视频片段不存在时抛出异常
     * @return 视频片段实体
     */
    private suspend fun validateAndGetSegment(segmentId: String): VideoSegmentEntity {
        val segment = reactiveMongoTemplate.findOne(
            buildSegmentQuery(segmentId),
            VideoSegmentEntity::class.java
        ).awaitSingleOrNull()

        if (segment == null) {
            logger.warn("视频片段不存在，videoSegmentId={}", segmentId)
            throw SysException.notFound("视频片段不存在")
        }
        return segment
    }

    /**
     * 更新视频片段字段的通用方法
     * @param segmentId 片段ID
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param operationName 操作名称（用于日志）
     * @param draftId 草稿ID
     * @param logParams 额外的日志参数
     * @return 片段ID
     */
    private suspend fun updateVideoSegmentField(
        segmentId: String,
        fieldName: String,
        fieldValue: Any?,
        operationName: String,
        draftId: String,
        logParams: Map<String, Any?> = emptyMap()
    ): String {
        logger.info(
            "开始为视频片段{}，draftId={}, videoSegmentId={}, 参数={}",
            operationName, draftId, segmentId, logParams
        )

        validateAndGetSegment(segmentId)
        updateSegmentField(segmentId, fieldName, fieldValue)

        logger.info("视频片段{}成功，videoSegmentId={}", operationName, segmentId)
        return segmentId
    }

    /**
     * 更新片段字段的底层方法
     * @param segmentId 片段ID
     * @param fieldName 字段名
     * @param fieldValue 字段值
     */
    private suspend fun updateSegmentField(segmentId: String, fieldName: String, fieldValue: Any?) {
        val query = buildSegmentQuery(segmentId)
        val update = Update().set(fieldName, fieldValue)
        reactiveMongoTemplate.updateFirst(query, update, VideoSegmentEntity::class.java).awaitSingle()
    }

    /**
     * 验证批量请求参数
     * @param items 项目列表
     * @param itemTypeName 项目类型名称
     * @param segmentId 片段ID
     * @throws SysException 当列表为空时抛出异常
     */
    private fun <T> validateBatchRequest(items: List<T>?, itemTypeName: String, segmentId: String) {
        if (items.isNullOrEmpty()) {
            logger.warn("{}列表为空，segmentId={}", itemTypeName, segmentId)
            throw SysException.invalidParam("${itemTypeName}列表不能为空")
        }
    }

    /**
     * 记录批量操作开始日志
     * @param operationName 操作名称
     * @param draftId 草稿ID
     * @param segmentId 片段ID
     * @param itemCount 项目数量
     */
    private fun logBatchOperationStart(operationName: String, draftId: String, segmentId: String, itemCount: Int) {
        logger.info(
            "开始为视频片段{}，draftId={}, segmentId={}, 数量={}",
            operationName, draftId, segmentId, itemCount
        )
    }

    /**
     * 记录批量操作结果日志
     * @param items 合并后的项目列表
     * @param itemTypeName 项目类型名称
     * @param getLogInfo 获取日志信息的函数
     */
    private fun <T> logBatchOperationResult(
        items: List<T>,
        itemTypeName: String,
        getLogInfo: (T) -> String
    ) {
        logger.info("{}去重合并后，最终{}数量={}", itemTypeName, itemTypeName, items.size)
        items.forEach { item ->
            logger.info("{}详情：{}", itemTypeName, getLogInfo(item))
        }
    }

    /**
     * 记录特效详情日志
     * @param effects 特效列表
     */
    private fun logEffectDetails(effects: List<VideoEffect>) {
        effects.forEach { effect ->
            logger.info(
                "视频特效详情：resourceId={}, resourceName={}, params={}",
                effect.effectType.resourceId, effect.effectType.resourceName, effect.params
            )
        }
    }

    /**
     * 记录滤镜详情日志
     * @param filters 滤镜列表
     */
    private fun logFilterDetails(filters: List<VideoFilter>) {
        filters.forEach { filter ->
            logger.info(
                "视频滤镜详情：resourceId={}, resourceName={}, intensity={}",
                filter.filterType.resourceId, filter.filterType.resourceName, filter.intensity
            )
        }
    }

    /**
     * 通用的基于Resource的项目合并方法
     * @param existingItems 现有项目列表
     * @param newItems 新项目列表
     * @param getResourceId 获取resourceId的函数
     * @param itemTypeName 项目类型名称（用于日志）
     * @return 合并后的项目列表
     */
    private fun <T> mergeResourceBasedItems(
        existingItems: List<T>,
        newItems: List<T>,
        getResourceId: (T) -> String?,
        itemTypeName: String
    ): List<T> {
        logger.debug(
            "开始合并视频{}，现有{}数量={}, 新{}数量={}",
            itemTypeName, itemTypeName, existingItems.size, itemTypeName, newItems.size
        )

        val itemMap = mutableMapOf<String, T>()

        // 先添加现有项目
        existingItems.forEach { item ->
            val resourceId = getResourceId(item)
            if (!(resourceId.isNullOrBlank())) {
                itemMap[resourceId] = item
                logger.debug("添加现有{}：resourceId={}", itemTypeName, resourceId)
            }

        }

        // 再添加新项目，相同resourceId的会覆盖现有的
        newItems.forEach { item ->
            val resourceId = getResourceId(item)
            if (!(resourceId.isNullOrBlank())) {
                val previousItem = itemMap[resourceId]
                itemMap[resourceId] = item
                if (previousItem != null) {
                    logger.info(
                        "{}覆盖：resourceId={} 的{}被新{}覆盖",
                        itemTypeName, resourceId, itemTypeName, itemTypeName
                    )
                } else {
                    logger.info("{}新增：resourceId={}", itemTypeName, resourceId)
                }
            }
        }

        val result = itemMap.values.toList()
        logger.debug("{}合并完成，最终{}数量={}", itemTypeName, itemTypeName, result.size)
        return result
    }
} 