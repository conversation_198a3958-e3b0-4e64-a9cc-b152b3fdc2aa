package com.esther.jianyingdraft.utils

import com.esther.jianyingdraft.domain.Timerange
import com.esther.jianyingdraft.domain.TimerangeDto
import com.esther.jianyingdraft.entity.AudioSegmentEntity
import com.esther.jianyingdraft.entity.DraftEntity
import com.esther.jianyingdraft.entity.TextSegmentEntity
import com.esther.jianyingdraft.entity.VideoSegmentEntity
import com.esther.jianyingdraft.exception.SysException
import kotlinx.coroutines.reactor.awaitSingle
import kotlinx.coroutines.reactor.awaitSingleOrNull
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query

/**
 * <AUTHOR>
 * @since 2025/7/30 21:23
 * @description 草稿工具类 - 提供草稿相关的通用操作
 */
object DraftUtils {

    private val logger = LoggerFactory.getLogger(DraftUtils::class.java)

    /**
     * 检查草稿是否存在, 同时查看新建的时候是否有草稿apiKey
     */
    suspend fun checkApiKeyByDraftId(draftId: String, mongoTemplate: ReactiveMongoTemplate) {
        try {
            val draft = mongoTemplate.findById(draftId, DraftEntity::class.java).awaitSingle()
            if (draft == null) {
                logger.warn("草稿不存在")
                throw SysException.notFound("草稿不存在")
            }
            if (draft.apiKey.isNullOrEmpty()) {
                logger.warn("apiKey错误")
                throw SysException.unauthorized("apiKey错误")
            }
        } catch (_: Exception) {
            logger.warn("草稿不存在")
            throw SysException.unauthorized("草稿不存在")
        }
    }

    /**
     * 根据片段ID查询任意类型的片段，并返回其目标时间范围
     * @param segmentId 片段ID
     * @param mongoTemplate MongoDB模板
     * @return 片段的目标时间范围，如果片段不存在则返回null
     */
    suspend fun findSegmentTargetTimerange(segmentId: String, mongoTemplate: ReactiveMongoTemplate): TimerangeDto? {
        logger.debug("查询片段目标时间范围，segmentId: {}", segmentId)

        val query = Query(Criteria.where("id").`is`(segmentId))

        // 尝试从视频片段中查找
        val videoSegment = mongoTemplate.findOne(query, VideoSegmentEntity::class.java).awaitSingleOrNull()
        if (videoSegment != null) {
            logger.debug("找到视频片段，segmentId: {}", segmentId)
            return videoSegment.realTargetTimerange
        }

        // 尝试从音频片段中查找
        val audioSegment = mongoTemplate.findOne(query, AudioSegmentEntity::class.java).awaitSingleOrNull()
        if (audioSegment != null) {
            logger.debug("找到音频片段，segmentId: {}", segmentId)
            return audioSegment.realTargetTimerange
        }

        // 尝试从文本片段中查找
        val textSegment = mongoTemplate.findOne(query, TextSegmentEntity::class.java).awaitSingleOrNull()
        if (textSegment != null) {
            logger.debug("找到文本片段，segmentId: {}", segmentId)
            return textSegment.realTargetRanger
        }

        logger.warn("未找到指定的片段，segmentId: {}", segmentId)
        return null
    }

    /**
     * 根据 afterSegmentId 计算新片段的起始时间
     * @param afterSegmentId 前置片段ID
     * @param mongoTemplate MongoDB模板
     * @return 计算后的起始时间（微秒），如果afterSegmentId为空则返回null
     */
    suspend fun calculateStartTimeByAfterSegment(afterSegmentId: String?, mongoTemplate: ReactiveMongoTemplate): Long? {
        if (afterSegmentId.isNullOrBlank()) {
            return null
        }

        logger.debug("计算新片段起始时间，afterSegmentId: {}", afterSegmentId)

        val targetTimerange = findSegmentTargetTimerange(afterSegmentId, mongoTemplate)
            ?: throw SysException.notFound("指定的前置片段不存在: $afterSegmentId")

        val newStartTime = targetTimerange.start + targetTimerange.duration
        logger.debug("计算得出新片段起始时间: {} 微秒，afterSegmentId: {}", newStartTime, afterSegmentId)

        return newStartTime
    }

    /**
     * 构建完整的目标时间范围
     * @param originalTimerange 原始时间范围
     * @param afterSegmentId 前置片段ID
     * @param calculatedDuration 计算后的持续时间
     * @param mongoTemplate MongoDB模板
     * @return 完整的目标时间范围
     */
    suspend fun buildCompleteTargetTimerange(
        originalTimerange: Timerange,
        afterSegmentId: String?,
        calculatedDuration: String,
        mongoTemplate: ReactiveMongoTemplate
    ): Timerange {
        val startTime = if (originalTimerange.start != null) {
            // 如果原始请求中提供了start时间，则使用原始值
            logger.debug("使用原始提供的起始时间: {}", originalTimerange.start)
            originalTimerange.start
        } else if (!(afterSegmentId.isNullOrBlank())) {
            // 如果提供了afterSegmentId，则计算起始时间
            val calculatedStartMicros = calculateStartTimeByAfterSegment(afterSegmentId, mongoTemplate)
                ?: throw SysException.systemError("无法计算片段起始时间")

            // 将微秒转换为字符串格式（秒）
            val calculatedStartSeconds = calculatedStartMicros / TimeUtils.SEC.toDouble()
            val startTimeStr = "${calculatedStartSeconds}s"
            logger.debug("根据afterSegmentId计算的起始时间: {}", startTimeStr)
            startTimeStr
        } else {
            // 如果既没有start也没有afterSegmentId，则默认从0开始
            logger.debug("使用默认起始时间: 0s")
            "0s"
        }

        return Timerange(
            start = startTime,
            duration = calculatedDuration
        )
    }

    /**
     * 检查同一轨道上的同类型片段是否存在时间重叠
     * @param draftId 草稿ID
     * @param trackId 轨道ID（可为空）
     * @param newSegmentTimerange 新片段的时间范围
     * @param segmentType 片段类型 ("video", "audio", "text")
     * @param excludeSegmentId 要排除的片段ID（用于更新操作）
     * @param mongoTemplate MongoDB模板
     * @throws SysException 如果存在重叠则抛出异常
     */
    suspend fun checkSegmentOverlapInTrack(
        draftId: String,
        trackId: String?,
        newSegmentTimerange: Timerange,
        segmentType: String,
        excludeSegmentId: String?,
        mongoTemplate: ReactiveMongoTemplate
    ) {
        logger.debug(
            "检查同类型片段轨道重叠，draftId: {}, trackId: {}, segmentType: {}, 时间范围: start={}, duration={}",
            draftId, trackId, segmentType, newSegmentTimerange.start, newSegmentTimerange.duration
        )

        if (newSegmentTimerange.start == null || newSegmentTimerange.duration == null) {
            throw SysException.systemError("时间范围不完整，无法检查重叠")
        }

        // 计算新片段的时间范围（微秒）
        val startStr = newSegmentTimerange.start!!
        val durationStr = newSegmentTimerange.duration!!
        val newStartMicros = TimeUtils.tim(startStr)
        val newEndMicros = newStartMicros + TimeUtils.tim(durationStr)

        // 查询同一轨道上的同类型片段
        val sameTrackSegments = mutableListOf<TimerangeDto>()

        // 构建查询条件：同一草稿且同一轨道（trackId为空或相同）
        val baseCriteria = Criteria.where("draftId").`is`(draftId)
        val trackCriteria = if (trackId == null) {
            Criteria().orOperator(
                Criteria.where("trackId").`is`(null),
                Criteria.where("trackId").exists(false)
            )
        } else {
            Criteria.where("trackId").`is`(trackId)
        }
        val query = Query(baseCriteria.andOperator(trackCriteria))

        // 如果是更新操作，排除当前片段
        if (excludeSegmentId != null) {
            query.addCriteria(Criteria.where("id").ne(excludeSegmentId))
        }

        // 根据片段类型查询对应的集合
        when (segmentType.lowercase()) {
            "video" -> {
                val videoSegments =
                    mongoTemplate.find(query, VideoSegmentEntity::class.java).collectList().awaitSingle()
                videoSegments.forEach { segment ->
                    sameTrackSegments.add(segment.realTargetTimerange)
                }
                logger.debug("找到 {} 个同轨道视频片段", videoSegments.size)
            }

            "audio" -> {
                val audioSegments =
                    mongoTemplate.find(query, AudioSegmentEntity::class.java).collectList().awaitSingle()
                audioSegments.forEach { segment ->
                    sameTrackSegments.add(segment.realTargetTimerange)
                }
                logger.debug("找到 {} 个同轨道音频片段", audioSegments.size)
            }

            "text" -> {
                val textSegments = mongoTemplate.find(query, TextSegmentEntity::class.java).collectList().awaitSingle()
                textSegments.forEach { segment ->
                    if (segment.realTargetRanger != null) {
                        sameTrackSegments.add(segment.realTargetRanger)
                    }
                }
                logger.debug("找到 {} 个同轨道文本片段", textSegments.size)
            }

            else -> {
                logger.warn("未知的片段类型: {}", segmentType)
                throw SysException.invalidParam("未知的片段类型: $segmentType")
            }
        }

        logger.debug("同一轨道找到 {} 个同类型现有片段", sameTrackSegments.size)

        // 检查是否存在重叠
        for (existingSegment in sameTrackSegments) {
            val existingStart = existingSegment.start
            val existingEnd = existingStart + existingSegment.duration

            // 重叠检测：新片段的开始时间 < 现有片段的结束时间 且 新片段的结束时间 > 现有片段的开始时间
            val isOverlapping = newStartMicros < existingEnd && newEndMicros > existingStart

            if (isOverlapping) {
                logger.warn(
                    "检测到同类型片段重叠！新{}片段: {}微秒-{}微秒, 现有{}片段: {}微秒-{}微秒",
                    segmentType, newStartMicros, newEndMicros, segmentType, existingStart, existingEnd
                )
                throw SysException.conflict(
                    "${segmentType}片段时间重叠！新片段时间范围 ${newStartMicros / TimeUtils.SEC}s-${newEndMicros / TimeUtils.SEC}s " +
                            "与现有${segmentType}片段 ${existingStart / TimeUtils.SEC}s-${existingEnd / TimeUtils.SEC}s 存在重叠"
                )
            }
        }

        logger.debug("同类型片段轨道重叠检查通过，没有发现重叠片段")
    }
}