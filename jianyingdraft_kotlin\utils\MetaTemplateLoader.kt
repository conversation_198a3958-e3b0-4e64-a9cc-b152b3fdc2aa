package com.esther.jianyingdraft.utils

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.core.io.ClassPathResource

/**
 * <AUTHOR>
 * @date 2025/7/25
 * @des des
 */
object MetaTemplateLoader {
    private val logger = LoggerFactory.getLogger(MetaTemplateLoader::class.java)

    fun loadMetaTemplate(
        objectMapper: ObjectMapper,
        draftPath: String,
        draftId: String,
        filePath: String = "draft_meta_info.json"
    ): Map<String, Any?> {
        val classPathResource = ClassPathResource(filePath)
        return try {
            val map =
                objectMapper.readValue(
                    classPathResource.inputStream,
                    object : TypeReference<MutableMap<String, Any?>>() {})
            map["draft_fold_path"] = draftPath + "\\${draftId}"
            map["draft_id"] = draftId
            map["draft_name"] = draftId
            map["draft_root_path"] = draftPath + "\\${draftId}"
            return map
        } catch (e: Exception) {
            logger.error("Error loading meta template: ${e.message}")
            emptyMap()
        }
    }
}