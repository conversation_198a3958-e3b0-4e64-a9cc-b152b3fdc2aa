package com.esther.jianyingdraft.utils

import java.net.URI

/**
 * <AUTHOR>
 * @since 2025/7/24 20:48
 *
 */
object NetUtils {
    /**
     * 判断路径是否为网络路径
     *
     * @param path 文件路径
     * @return true表示网络路径，false表示本地路径
     */
    fun isNetworkPath(path: String): <PERSON><PERSON><PERSON> {
        return try {
            val uri = URI(path)
            uri.scheme != null && (uri.scheme.equals("http", ignoreCase = true) ||
                    uri.scheme.equals("https", ignoreCase = true) ||
                    uri.scheme.equals("ftp", ignoreCase = true))
        } catch (_: Exception) {
            false
        }
    }
}