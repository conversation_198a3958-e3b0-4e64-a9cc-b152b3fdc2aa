# JianYing Draft Python API

这是将 `jianyingdraft_kotlin` 项目完全转换为 Python 语法的版本，使用 FastAPI 框架，删除了所有 API key 相关逻辑。

## 项目特点

- ✅ 完全照抄 jianyingdraft_kotlin 项目逻辑
- ✅ 使用 FastAPI 框架
- ✅ 删除了所有 API key 相关代码
- ✅ 支持剪映草稿创建、编辑、导出
- ✅ 支持视频、音频、文本片段管理
- ✅ 支持轨道管理
- ✅ 内存存储（代替 MongoDB）

## 项目结构

```
jianyingdraft_python/
├── config/                 # 配置模块
├── controller/            # API控制器
├── domain/                # 领域模型
│   ├── audio/            # 音频相关
│   ├── effect/           # 特效相关
│   ├── meterial/         # 素材相关
│   ├── req/              # 请求DTO
│   ├── rep/              # 响应DTO
│   ├── text/             # 文本相关
│   └── video/            # 视频相关
├── entity/               # 实体类
├── exception/            # 异常处理
├── service/              # 业务逻辑层
├── utils/                # 工具类
├── main.py              # 主应用文件
├── requirements.txt     # 依赖列表
└── README.md            # 项目说明
```

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python main.py
```

或

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 访问API文档

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API端点

### 草稿管理
- `POST /api/draft/create` - 创建草稿
- `GET /api/draft/export/{draft_id}` - 导出草稿为ZIP
- `GET /api/draft/all` - 获取所有草稿
- `GET /api/draft/{draft_id}` - 获取指定草稿

### 视频片段管理
- `POST /api/video/segment/add` - 添加视频片段
- `GET /api/video/segments/{draft_id}` - 获取视频片段列表

### 音频片段管理
- `POST /api/audio/segment/add` - 添加音频片段
- `GET /api/audio/segments/{draft_id}` - 获取音频片段列表

### 文本片段管理
- `POST /api/text/segment/add` - 添加文本片段
- `GET /api/text/segments/{draft_id}` - 获取文本片段列表

### 轨道管理
- `POST /api/track/add` - 添加轨道
- `GET /api/track/list/{draft_id}` - 获取轨道列表
- `DELETE /api/track/{track_id}` - 删除轨道

## 使用示例

### 创建草稿

```bash
curl -X POST "http://localhost:8000/api/draft/create" \
     -H "Content-Type: application/json" \
     -d '{
       "width": 1920,
       "height": 1080,
       "fps": 30,
       "name": "我的视频",
       "draft_path": "./drafts/my_video"
     }'
```

### 添加视频片段

```bash
curl -X POST "http://localhost:8000/api/video/segment/add" \
     -H "Content-Type: application/json" \
     -d '{
       "draft_id": "草稿ID",
       "track_id": "轨道ID",
       "material_id": "material1.mp4",
       "segment_id": "segment1",
       "target_range": {
         "start": "0s",
         "duration": "10s"
       }
     }'
```

## 注意事项

1. 本项目使用内存存储，重启服务后数据会丢失
2. 实际生产环境建议添加数据库支持
3. 素材文件路径需要实际存在
4. 时间格式支持：纯数字、1s、2m、1h 等

## 与原项目的差异

- ✅ 删除了 ApiKeyFilter 和相关验证逻辑
- ✅ 删除了 MongoDB 依赖，改为内存存储
- ✅ 简化了异步处理（Python async/await vs Kotlin coroutines）
- ✅ 保持核心业务逻辑不变
- ✅ 所有DTO和实体类完全映射

## 扩展建议

1. 添加数据库支持（SQLite、PostgreSQL等）
2. 添加文件上传功能
3. 添加素材管理模块
4. 添加视频处理功能
5. 添加用户认证系统