from fastapi import Request
from fastapi.responses import JSONResponse
import logging
from exception.sys_exception import SysException

logger = logging.getLogger(__name__)


async def global_exception_handler(request: Request, exc: Exception):
    """
    全局异常处理器
    """
    if isinstance(exc, SysException):
        return JSONResponse(
            status_code=exc.code,
            content={"code": exc.code, "message": exc.message, "data": None}
        )
    
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"code": 500, "message": str(exc), "data": None}
    )