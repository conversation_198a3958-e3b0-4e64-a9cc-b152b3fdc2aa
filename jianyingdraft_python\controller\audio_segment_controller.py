from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.audio_segment_service import AudioSegmentService
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/api/audio", tags=["音频片段管理"])
audio_service = AudioSegmentService()


@router.post("/segment/add", response_model=DataResponse[AudioSegmentEntity])
async def add_audio_segment(req_dto: MediaSegmentAddReqDto):
    """添加音频片段"""
    try:
        result = audio_service.add_audio_segment(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/segments/{draft_id}", response_model=DataResponse[List[AudioSegmentEntity]])
async def get_audio_segments(draft_id: str):
    """获取草稿的音频片段"""
    try:
        segments = audio_service.get_audio_segments_by_draft(draft_id)
        return DataResponse.success(segments)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))