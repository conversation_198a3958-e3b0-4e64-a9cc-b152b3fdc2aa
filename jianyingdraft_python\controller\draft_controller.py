import io
from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.draft_service import DraftService
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.rep.draft_create_rep_dto import DraftCreateRepDto
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/api/draft", tags=["草稿管理"])
draft_service = DraftService()


@router.post("/create", response_model=DataResponse[DraftCreateRepDto])
async def create_draft(req_dto: DraftCreateReqDto):
    """创建草稿"""
    try:
        result = draft_service.create_draft_script(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/export/{draft_id}")
async def export_draft_as_zip(draft_id: str):
    """导出草稿为zip压缩包"""
    try:
        zip_data = draft_service.export_draft_as_zip(draft_id)
        from fastapi.responses import StreamingResponse
        
        return StreamingResponse(
            io.BytesIO(zip_data),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=draft_{draft_id}.zip"}
        )
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/all", response_model=DataResponse[List[DraftCreateRepDto]])
async def get_all_drafts():
    """获取所有草稿"""
    try:
        drafts = draft_service.get_all_drafts()
        result = [
            DraftCreateRepDto(
                draft_id=draft.id,
                draft_path=draft.draft_path
            ) for draft in drafts
        ]
        return DataResponse.success(result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{draft_id}", response_model=DataResponse[DraftCreateRepDto])
async def get_draft_by_id(draft_id: str):
    """根据ID获取草稿"""
    try:
        draft = draft_service.get_draft_by_id(draft_id)
        if not draft:
            raise HTTPException(status_code=404, detail="草稿不存在")
        
        result = DraftCreateRepDto(
            draft_id=draft.id,
            draft_path=draft.draft_path
        )
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))