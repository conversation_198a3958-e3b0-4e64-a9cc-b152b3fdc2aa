from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.text_segment_service import TextSegmentService
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.req.text_segment_add_req_dto import TextSegmentAddReqDto
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/api/text", tags=["文本片段管理"])
text_service = TextSegmentService()


@router.post("/segment/add", response_model=DataResponse[TextSegmentEntity])
async def add_text_segment(req_dto: TextSegmentAddReqDto):
    """添加文本片段"""
    try:
        result = text_service.add_text_segment(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/segments/{draft_id}", response_model=DataResponse[List[TextSegmentEntity]])
async def get_text_segments(draft_id: str):
    """获取草稿的文本片段"""
    try:
        segments = text_service.get_text_segments_by_draft(draft_id)
        return DataResponse.success(segments)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))