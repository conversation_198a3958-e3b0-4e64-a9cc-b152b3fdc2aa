from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.video_segment_service import VideoSegmentService
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/api/video", tags=["视频片段管理"])
video_service = VideoSegmentService()


@router.post("/segment/add", response_model=DataResponse[VideoSegmentEntity])
async def add_video_segment(req_dto: MediaSegmentAddReqDto):
    """添加视频片段"""
    try:
        result = video_service.add_video_segment(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/segments/{draft_id}", response_model=DataResponse[List[VideoSegmentEntity]])
async def get_video_segments(draft_id: str):
    """获取草稿的视频片段"""
    try:
        segments = video_service.get_video_segments_by_draft(draft_id)
        return DataResponse.success(segments)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))