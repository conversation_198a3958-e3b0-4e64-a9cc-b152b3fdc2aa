from pydantic import BaseModel, Field


class ClipSettings(BaseModel):
    """
    素材片段的图像调节设置
    """
    alpha: float = Field(default=1.0, description="图像不透明度, 0-1")
    flip_horizontal: bool = Field(default=False, description="是否水平翻转")
    flip_vertical: bool = Field(default=False, description="是否垂直翻转")
    rotation: float = Field(default=0.0, description="顺时针旋转的**角度**, 可正可负")
    scale_x: float = Field(default=1.0, description="水平缩放比例")
    scale_y: float = Field(default=1.0, description="垂直缩放比例")
    transform_x: float = Field(default=0.0, description="水平位移, 单位为半个画布宽")
    transform_y: float = Field(default=0.0, description="垂直位移, 单位为半个画布高")