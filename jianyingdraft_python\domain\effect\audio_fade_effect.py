from pydantic import BaseModel, Field
from typing import Optional


class AudioFadeEffect(BaseModel):
    """
    音频淡入淡出效果
    """
    fade_in_duration: Optional[str] = Field(default=None, description="淡入持续时间")
    fade_out_duration: Optional[str] = Field(default=None, description="淡出持续时间")
    fade_in_type: str = Field(default="linear", description="淡入类型")
    fade_out_type: str = Field(default="linear", description="淡出类型")