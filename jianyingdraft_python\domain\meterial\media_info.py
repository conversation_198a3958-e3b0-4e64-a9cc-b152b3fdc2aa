from pydantic import BaseModel, Field
from typing import Optional


class MediaInfo(BaseModel):
    """
    媒体文件信息数据类
    包含媒体文件的完整信息：文件名、路径、大小、格式、类型、尺寸、时长等
    """
    file_name: str = Field(description="文件名")
    absolute_path: str = Field(description="文件路径")
    file_size: int = Field(description="文件大小")
    mime_type: str = Field(description="文件MIME类型")
    type: str = Field(description="文件格式")
    width: Optional[int] = Field(default=None, description="宽度")
    height: Optional[int] = Field(default=None, description="高度")
    duration_microseconds: Optional[int] = Field(default=None, description="时长(微秒)，静态图片为null")
    duration_seconds: Optional[str] = Field(default=None, description="时长(秒)，静态图片为null")