from typing import Generic, TypeVar, Optional
from pydantic import BaseModel, Field

T = TypeVar('T')


class DataResponse(BaseModel, Generic[T]):
    """
    统一响应实体
    """
    code: int = Field(default=200, description="响应码，200为成功")
    message: Optional[str] = Field(default=None, description="响应消息")
    data: Optional[T] = Field(default=None, description="响应数据体")

    @staticmethod
    def success(data: T = None) -> 'DataResponse[T]':
        return DataResponse(code=200, message="success", data=data)

    @staticmethod
    def success_empty() -> 'DataResponse[str]':
        return DataResponse(code=200, message="success")

    @staticmethod
    def error(code: int, message: str) -> 'DataResponse[str]':
        return DataResponse(code=code, message=message)

    @staticmethod
    def error_message(message: str) -> 'DataResponse[str]':
        return DataResponse(code=500, message=message)