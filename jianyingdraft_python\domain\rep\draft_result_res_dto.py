from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity


class AudioTrack(BaseModel):
    """音频轨道 - 完全匹配kotlin版本"""
    trackId: str = Field(description="轨道id")
    trackName: Optional[str] = Field(default=None, description="轨道名称")
    audioSegments: List[AudioSegmentEntity] = Field(default_factory=list, description="轨道下的音频")


class VideoTrack(BaseModel):
    """视频轨道 - 完全匹配kotlin版本"""
    trackId: str = Field(description="轨道id")
    trackName: Optional[str] = Field(default=None, description="轨道名称")
    videoSegments: List[VideoSegmentEntity] = Field(default_factory=list, description="轨道下的视频")


class TextTrack(BaseModel):
    """字幕轨道 - 完全匹配kotlin版本"""
    trackId: str = Field(description="轨道id")
    trackName: Optional[str] = Field(default=None, description="轨道名称")
    textSegments: List[TextSegmentEntity] = Field(default_factory=list, description="轨道下的字幕")


class DraftResultResDto(BaseModel):
    """草稿结果返回参数 - 完全匹配kotlin版本"""
    draftId: str = Field(description="草稿id")
    draftName: Optional[str] = Field(default=None, description="草稿名称")
    draftContent: Optional[Dict[str, Any]] = Field(default=None, description="草稿内容")
    audioTracks: List[AudioTrack] = Field(default_factory=list, description="音频轨道")
    videoTracks: List[VideoTrack] = Field(default_factory=list, description="视频轨道")
    textTracks: List[TextTrack] = Field(default_factory=list, description="字幕轨道")