from pydantic import BaseModel, Field


class BackgroundFilling(BaseModel):
    """
    背景填充
    """
    fill_type: str = Field(default="blur", description="背景填充类型")
    blur: float = Field(default=0.625, description="模糊度, 0~1")
    color: str = Field(default="#000000", description="填充颜色")


class BackgroundFillingReqDto(BaseModel):
    """
    背景填充请求参数
    """
    draft_id: str = Field(description="草稿id")
    video_segment_id: str = Field(description="视频片段id")
    fill_type: str = Field(default="blur", description="背景填充类型")
    blur: float = Field(default=0.625, description="模糊度, 0~1")
    color: str = Field(default="#00000000", description="填充颜色")