from typing import Optional
from pydantic import BaseModel


class DraftCreateReqDto(BaseModel):
    """创建草稿请求DTO - 完全匹配kotlin版本"""
    width: int = 1920
    height: int = 1080
    fps: int = 30
    name: Optional[str] = None
    draftPath: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "width": 1920,
                "height": 1080,
                "fps": 30,
                "name": "测试草稿",
                "draftPath": "/path/to/draft"
            }
        }