from typing import Optional
from pydantic import BaseModel
from jianyingdraft_python.domain.clip_settings import ClipSettings
from jianyingdraft_python.domain.timerange import Timerange


class MediaSegmentAddReqDto(BaseModel):
    """音频/视频片段添加请求参数 - 完全匹配kotlin版本"""
    afterSegmentId: Optional[str] = None
    draftId: str
    targetTimerange: Timerange
    sourceTimerange: Optional[Timerange] = None
    speed: float = 1.0
    volume: float = 1.0
    resourcePath: str
    trackId: Optional[str] = None
    clipSettings: Optional[ClipSettings] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "afterSegmentId": None,
                "draftId": "draft-123",
                "targetTimerange": {
                    "start": "0s",
                    "duration": "5s"
                },
                "sourceTimerange": None,
                "speed": 1.0,
                "volume": 1.0,
                "resourcePath": "/path/to/video.mp4",
                "trackId": "track-456",
                "clipSettings": None
            }
        }