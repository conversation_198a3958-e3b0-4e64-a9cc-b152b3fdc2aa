from pydantic import BaseModel, Field
from typing import Optional
from jianyingdraft_python.domain.text.text_style import TextStyle
from jianyingdraft_python.domain.text.text_border import TextBorder
from jianyingdraft_python.domain.text.text_background import TextBackground
from jianyingdraft_python.domain.clip_settings import ClipSettings
from jianyingdraft_python.domain.timerange import Timerange
from jianyingdraft_python.domain.effect.audio_fade_effect import AudioFadeEffect


class Resource(BaseModel):
    """
    资源信息, id和名字
    """
    resource_id: Optional[str] = Field(default=None, description="资源id")
    resource_name: Optional[str] = Field(default=None, description="资源名字")


class AudioFadeEffectReqDto(BaseModel):
    """
    音频淡入淡出特效请求参数
    """
    draft_id: str = Field(description="草稿id")
    audio_segment_id: str = Field(description="音频片段id")
    audio_fade: AudioFadeEffect = Field(description="音频淡入淡出效果")


class KeyframeData(BaseModel):
    """
    单个关键帧数据
    """
    time_offset: str = Field(description="关键帧的时间偏移量, 1s,2s")
    volume: float = Field(default=1.0, description="音量在time_offset处的值")


class AudioKeyframeReqDto(BaseModel):
    """
    音频关键帧请求参数
    """
    draft_id: str = Field(description="草稿id")
    audio_segment_id: str = Field(description="音频片段id")
    keyframes: list[KeyframeData] = Field(description="关键帧列表，支持批量添加多个关键帧")


class VideoAnimation(BaseModel):
    """
    视频动画
    """
    type: Resource = Field(description="动画类型")
    duration: Optional[str] = Field(default=None, description="动画的时间")
    real_duration: Optional[int] = Field(default=None, description="动画的时间")

    def __init__(self, **data):
        super().__init__(**data)
        if self.duration is not None:
            from ..utils.time_utils import TimeUtils
            self.real_duration = TimeUtils.tim(self.duration)


class BackgroundFilling(BaseModel):
    """
    背景填充
    """
    fill_type: str = Field(default="blur", description="背景填充类型")
    blur: float = Field(default=0.625, description="模糊度, 0~1")
    color: str = Field(default="#000000", description="填充颜色")


class TransitionType(BaseModel):
    """
    转场类型
    """
    transition_type: Resource = Field(description="转场类型")
    duration: Optional[str] = Field(default=None, description="转场持续时间, 单位为微秒")
    real_duration: Optional[int] = Field(default=None, description="动画的时间范围")

    def __init__(self, **data):
        super().__init__(**data)
        if self.duration is not None:
            from ..utils.time_utils import TimeUtils
            self.real_duration = TimeUtils.tim(self.duration)


class DraftCreateReqDto(BaseModel):
    """
    创建草稿请求参数
    """
    width: int = Field(default=1920, description="草稿宽度")
    height: int = Field(default=1080, description="草稿高度")
    fps: int = Field(default=30, description="帧率")
    name: Optional[str] = Field(default=None, description="草稿名称")
    draft_path: str = Field(description="草稿路径")