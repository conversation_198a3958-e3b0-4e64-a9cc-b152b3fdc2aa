from pydantic import BaseModel, Field
from typing import Optional
from ..req.resource import Resource


class TextAnimationAndEffect(BaseModel):
    """
    字幕动画和特效
    """
    type: Optional[Resource] = Field(default=None, description="动画类型")
    duration: Optional[str] = Field(default=None, description="动画持续时间")
    bubble_effect_id: Optional[str] = Field(default=None, description="泡泡特效id")
    bubble_resource_id: Optional[str] = Field(default=None, description="泡泡特效资源id")
    flower_effect_id: Optional[str] = Field(default=None, description="花字特效资源id")


class TextAnimationAndEffectReqDto(BaseModel):
    """
    字幕动画和特效请求参数
    """
    text_segment_id: str = Field(description="字幕片段id")
    draft_id: str = Field(description="草稿id")
    type: Optional[Resource] = Field(default=None, description="动画类型")
    duration: Optional[str] = Field(default=None, description="动画持续时间")
    bubble_effect_id: Optional[str] = Field(default=None, description="泡泡特效id")
    bubble_resource_id: Optional[str] = Field(default=None, description="泡泡特效资源id")
    flower_effect_id: Optional[str] = Field(default=None, description="花字特效资源id")