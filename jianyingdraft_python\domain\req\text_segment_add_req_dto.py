from typing import Optional
from pydantic import BaseModel
from jianyingdraft_python.domain.clip_settings import ClipSettings
from jianyingdraft_python.domain.text.text_background import TextBackground
from jianyingdraft_python.domain.text.text_border import TextBorder
from jianyingdraft_python.domain.text.text_style import TextStyle
from jianyingdraft_python.domain.timerange import Timerange


class Resource(BaseModel):
    """资源信息 - 完全匹配kotlin版本"""
    resourceId: Optional[str] = None
    resourceName: Optional[str] = None


class TextSegmentAddReqDto(BaseModel):
    """字幕片段添加请求参数 - 完全匹配kotlin版本"""
    afterSegmentId: Optional[str] = None
    textSegmentId: Optional[str] = None
    draftId: str
    text: str
    font: Optional[Resource] = None
    style: Optional[TextStyle] = None
    border: Optional[TextBorder] = None
    clipSettings: Optional[ClipSettings] = None
    background: Optional[TextBackground] = None
    trackId: Optional[str] = None
    targetRanger: Optional[Timerange] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "afterSegmentId": None,
                "textSegmentId": None,
                "draftId": "draft-123",
                "text": "测试字幕文本",
                "font": None,
                "style": None,
                "border": None,
                "clipSettings": None,
                "background": None,
                "trackId": "track-456",
                "targetRanger": {
                    "start": "0s",
                    "duration": "5s"
                }
            }
        }