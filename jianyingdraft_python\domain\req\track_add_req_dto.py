from typing import Optional
from pydantic import BaseModel


class TrackAddReqDto(BaseModel):
    """轨道添加请求参数 - 完全匹配kotlin版本"""
    draftId: str
    trackType: str
    trackName: Optional[str] = None
    mute: bool = False
    relativeIndex: int = 0
    absoluteIndex: Optional[int] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "draftId": "draft-123",
                "trackType": "video",
                "trackName": "视频轨道1",
                "mute": False,
                "relativeIndex": 0,
                "absoluteIndex": None
            }
        }