from pydantic import BaseModel, Field


class TextBackground(BaseModel):
    """
    字体背景设置
    """
    style: int = Field(default=1, description="背景样式, 1和2分别对应剪映中的两种样式")
    alpha: float = Field(default=1.0, description="背景不透明度")
    color: str = Field(default="#000000", description="背景颜色, 格式为'#RRGGBB'")
    round_radius: float = Field(default=0.0, description="背景圆角半径")
    height: float = Field(default=0.14, description="背景高度")
    width: float = Field(default=0.14, description="背景宽度")
    horizontal: float = Field(default=0.5, description="背景水平偏移")
    vertical: float = Field(default=0.5, description="背景竖直偏移")

    @property
    def horizontal_offset(self) -> float:
        """水平偏移"""
        return self.horizontal * 2 - 1

    @property
    def vertical_offset(self) -> float:
        """垂直偏移"""
        return self.vertical * 2 - 1