from pydantic import BaseModel, Field
from typing import List


class TextBorder(BaseModel):
    """
    文本描边参数
    """
    alpha: float = Field(default=1.0, description="描边不透明度")
    color: List[float] = Field(default=[0.0, 0.0, 0.0], description="描边颜色, RGB三元组, 取值范围为[0, 1]")
    mapped_width: float = Field(default=40.0, description="描边宽度")

    @property
    def width(self) -> float:
        """计算转换后的宽度"""
        return self.mapped_width / 100.0 * 0.2