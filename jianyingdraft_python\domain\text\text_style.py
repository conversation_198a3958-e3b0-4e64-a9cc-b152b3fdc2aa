from pydantic import BaseModel, Field
from typing import List


class TextStyle(BaseModel):
    """
    字体样式类
    """
    size: float = Field(default=8.0, description="字体大小")
    bold: bool = Field(default=False, description="是否加粗")
    italic: bool = Field(default=False, description="是否斜体")
    underline: bool = Field(default=False, description="是否加下划线")
    color: List[float] = Field(default=[1.0, 1.0, 1.0], description="字体颜色, RGB三元组, 取值范围为[0, 1]")
    alpha: float = Field(default=1.0, description="字体不透明度")
    align: int = Field(default=0, description="对齐方式, 0: 左对齐, 1: 居中, 2: 右对齐")
    vertical: bool = Field(default=False, description="是否为竖排文本")
    letter_spacing: int = Field(default=0, description="字符间距")
    line_spacing: int = Field(default=0, description="行间距")
    auto_wrapping: bool = Field(default=False, description="是否自动换行")
    max_line_width: float = Field(default=0.82, description="最大行宽, 取值范围为[0, 1]")