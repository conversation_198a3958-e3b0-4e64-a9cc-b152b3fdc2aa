from pydantic import BaseModel, Field
from typing import Optional
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.utils.time_utils import TimeUtils


class Timerange(BaseModel):
    """
    记录了起始时间及持续长度的时间范围
    时间用 0s, 1s等 表示
    """
    start: Optional[str] = Field(default=None, description="起始时间，当 afterSegmentId 存在时可为空，系统自动计算")
    duration: Optional[str] = Field(default=None, description="持续长度")

    def to_long_dto(self) -> 'TimerangeDto':
        if self.duration is None:
            raise SysException.system_error("duration can not be null")
        if self.start is None:
            raise SysException.system_error("start can not be null when converting to TimerangeDto")
        return TimerangeDto(
            start=TimeUtils.tim(self.start),
            duration=TimeUtils.tim(self.duration)
        )


class TimerangeDto(BaseModel):
    start: int
    duration: int