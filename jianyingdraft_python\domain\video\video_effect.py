from pydantic import BaseModel, Field
from typing import List, Optional
from ..req.resource import Resource


class VideoEffect(BaseModel):
    """
    视频特效类
    """
    effect_type: Resource = Field(description="特效类型")
    params: Optional[List[float]] = Field(default=None, description="特效参数列表")


class VideoEffectReqDto(BaseModel):
    """
    视频特效请求参数
    """
    draft_id: str = Field(description="素材所属的 draftId")
    segment_id: str = Field(description="素材所属的片段Id")
    effects: List[VideoEffect] = Field(description="视频特效参数")