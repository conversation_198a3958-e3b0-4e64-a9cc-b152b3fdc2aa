from pydantic import BaseModel, Field
from typing import List, Optional
from ..req.resource import Resource


class VideoFilter(BaseModel):
    """
    视频滤镜类
    """
    filter_type: Resource = Field(description="滤镜类型")
    intensity: float = Field(default=100.0, description="滤镜强度")


class VideoFilterReqDto(BaseModel):
    """
    视频滤镜请求参数
    """
    draft_id: str = Field(description="素材所属的 draftId")
    segment_id: str = Field(description="素材所属的片段Id")
    filters: List[VideoFilter] = Field(description="滤镜参数")