from pydantic import BaseModel, Field
from typing import Optional
from ..req.resource import Resource


class VideoMask(BaseModel):
    """
    视频蒙版
    """
    mask_type: Resource = Field(description="蒙版类型")
    center_x: float = Field(default=0.0, description="蒙版中心点X坐标")
    center_y: float = Field(default=0.0, description="蒙版中心点Y坐标")
    size: float = Field(default=0.5, description="蒙版的主要尺寸")
    rotation: float = Field(default=0.0, description="蒙版顺时针旋转的角度")
    feather: float = Field(default=0.0, description="蒙版的羽化参数")
    invert: bool = Field(default=False, description="是否反转蒙版")
    rect_width: Optional[float] = Field(default=None, description="矩形蒙版的宽度")
    round_corner: Optional[float] = Field(default=None, description="矩形蒙版的圆角参数")


class VideoMaskReqDto(BaseModel):
    """
    视频蒙版请求参数
    """
    draft_id: str = Field(description="素材所属的 draftId")
    segment_id: str = Field(description="素材所属的片段Id")
    mask_type: Resource = Field(description="蒙版类型")
    center_x: float = Field(default=0.0, description="蒙版中心点X坐标")
    center_y: float = Field(default=0.0, description="蒙版中心点Y坐标")
    size: float = Field(default=0.5, description="蒙版的主要尺寸")
    rotation: float = Field(default=0.0, description="蒙版顺时针旋转的角度")
    feather: float = Field(default=0.0, description="蒙版的羽化参数")
    invert: bool = Field(default=False, description="是否反转蒙版")
    rect_width: Optional[float] = Field(default=None, description="矩形蒙版的宽度")
    round_corner: Optional[float] = Field(default=None, description="矩形蒙版的圆角参数")