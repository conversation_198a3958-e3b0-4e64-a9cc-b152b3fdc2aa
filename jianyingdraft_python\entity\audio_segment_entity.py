from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from jianyingdraft_python.domain.audio.audio_effect import AudioEffect
from jianyingdraft_python.domain.effect.audio_fade_effect import AudioFadeEffect
from jianyingdraft_python.domain.meterial.media_info import MediaInfo
from jianyingdraft_python.domain.timerange import Timerange, TimerangeDto


class AudioKeyframe(BaseModel):
    """
    音频关键帧信息
    """
    time_offset: str = Field(description="关键帧的时间偏移量, 1s,2s")
    volume: float = Field(description="音量在time_offset处的值")


class AudioSegmentEntity(BaseModel):
    """
    音频片段Mongo实体
    """
    id: str = Field(description="主键id")
    draft_id: str = Field(description="素材所属的 draftId")
    target_timerange: Timerange = Field(description="片段在轨道上的目标时间范围")
    real_target_timerange: TimerangeDto = Field(description="实际的片段在轨道上的目标时间范围")
    source_timerange: Optional[Timerange] = Field(default=None, description="截取的素材片段的时间范围")
    real_source_timerange: Optional[TimerangeDto] = Field(default=None, description="实际的截取的素材片段的时间范围")
    speed: float = Field(default=1.0, description="播放速度")
    volume: float = Field(default=1.0, description="音量")
    track_id: Optional[str] = Field(default=None, description="轨道id, 把资源添加到哪个轨道")
    resource_path: str = Field(description="素材实例或素材路径")
    fade_effect: Optional[AudioFadeEffect] = Field(default=None, description="音频淡入淡出特效")
    audio_keyframes: Optional[List[AudioKeyframe]] = Field(default=None, description="音频关键帧列表")
    audio_effects: Optional[List[AudioEffect]] = Field(default=None, description="音频特效列表")
    media_info: MediaInfo = Field(description="素材信息")
    create_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    update_time: datetime = Field(default_factory=datetime.now, description="更新时间")