from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from datetime import datetime


class DraftEntity(BaseModel):
    """
    草稿实体
    """
    id: str = Field(description="主键id")
    draft_name: Optional[str] = Field(default=None, description="草稿名称")
    draft_content: Optional[Dict[str, Any]] = Field(default=None, description="草稿内容")
    width: int = Field(default=1920, description="草稿宽度")
    height: int = Field(default=1080, description="草稿高度")
    fps: int = Field(default=30, description="帧率")
    status: str = Field(default="create", description="状态: create-创建, finished-发布")
    export_count: int = Field(default=0, description="导出次数")
    draft_path: str = Field(description="草稿路径")
    create_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    update_time: datetime = Field(default_factory=datetime.now, description="更新时间")