from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from jianyingdraft_python.domain.clip_settings import ClipSettings
from jianyingdraft_python.domain.req.resource import Resource
from jianyingdraft_python.domain.req.text_animation_and_effect import TextAnimationAndEffect
from jianyingdraft_python.domain.text.text_background import TextBackground
from jianyingdraft_python.domain.text.text_border import TextBorder
from jianyingdraft_python.domain.text.text_style import TextStyle
from jianyingdraft_python.domain.timerange import Timerange, TimerangeDto


class TextSegmentEntity(BaseModel):
    """
    文本片段实体
    """
    id: str = Field(description="主键id")
    content: str = Field(description="文本内容")
    draft_id: str = Field(description="素材所属的 draftId")
    font: Optional[Resource] = Field(default=None, description="字体")
    style: Optional[TextStyle] = Field(default=None, description="样式")
    border: Optional[TextBorder] = Field(default=None, description="边框")
    clip_settings: ClipSettings = Field(default_factory=ClipSettings, description="裁剪设置")
    background: Optional[TextBackground] = Field(default=None, description="背景")
    target_timerange: Optional[Timerange] = Field(default=None, description="目标时间范围")
    real_target_timerange: Optional[TimerangeDto] = Field(default=None, description="实际目标时间范围")
    track_id: Optional[str] = Field(default=None, description="轨道id, 把资源添加到哪个轨道")
    text_animation_and_effects: List[TextAnimationAndEffect] = Field(default_factory=list, description="动画效果和特效列表")
    create_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    update_time: datetime = Field(default_factory=datetime.now, description="更新时间")