from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional


class TrackEntity(BaseModel):
    """
    轨道实体, 只能添加视频, 音频, 文本
    """
    id: str = Field(description="主键id")
    draft_id: str = Field(description="素材所属的 draftId")
    track_type: str = Field(description="轨道类型")
    track_name: str = Field(default=None, description="轨道名称")
    mute: bool = Field(default=False, description="轨道是否静音")
    relative_index: int = Field(default=0, description="相对图层位置, 越高越接近前景")
    absolute_index: Optional[int] = Field(default=None, description="绝对图层位置")
    create_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    update_time: datetime = Field(default_factory=datetime.now, description="更新时间")

    def __init__(self, **data):
        super().__init__(**data)
        if self.track_name is None:
            self.track_name = self.id