from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from jianyingdraft_python.domain.clip_settings import ClipSettings
from jianyingdraft_python.domain.meterial.media_info import MediaInfo
from jianyingdraft_python.domain.timerange import Timerange, TimerangeDto
from jianyingdraft_python.domain.req.background_filling import BackgroundFilling
from jianyingdraft_python.domain.req.transition_type import TransitionType
from jianyingdraft_python.domain.req.video_animation import VideoAnimation
from jianyingdraft_python.domain.video.video_effect import VideoEffect
from jianyingdraft_python.domain.video.video_filter import VideoFilter
from jianyingdraft_python.domain.video.video_mask import VideoMask


class VideoSegmentEntity(BaseModel):
    """
    视频片段实体
    """
    id: str = Field(description="主键id")
    draft_id: str = Field(description="素材所属的 draftId")
    target_timerange: Timerange = Field(description="片段在轨道上的目标时间范围")
    real_target_timerange: TimerangeDto = Field(description="实际片段在轨道上的目标时间范围")
    source_timerange: Optional[Timerange] = Field(default=None, description="截取的素材片段的时间范围")
    real_source_timerange: Optional[TimerangeDto] = Field(default=None, description="实际截取的素材片段的时间范围")
    speed: float = Field(default=1.0, description="播放速度")
    volume: float = Field(default=1.0, description="音量")
    resource_path: str = Field(description="素材实例或素材路径")
    clip_settings: ClipSettings = Field(default_factory=ClipSettings, description="素材片段的图像调节设置")
    background_filling: Optional[BackgroundFilling] = Field(default=None, description="素材片段的背景填充")
    transition_type: Optional[TransitionType] = Field(default=None, description="素材片段的转场")
    animation: Optional[VideoAnimation] = Field(default=None, description="素材片段的动画")
    video_effects: Optional[List[VideoEffect]] = Field(default=None, description="视频特效列表")
    video_filters: Optional[List[VideoFilter]] = Field(default=None, description="视频滤镜列表")
    video_mask: Optional[VideoMask] = Field(default=None, description="视频蒙版")
    track_id: Optional[str] = Field(default=None, description="轨道id, 把资源添加到哪个轨道")
    media_info: MediaInfo = Field(description="素材信息")
    create_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    update_time: datetime = Field(default_factory=datetime.now, description="更新时间")