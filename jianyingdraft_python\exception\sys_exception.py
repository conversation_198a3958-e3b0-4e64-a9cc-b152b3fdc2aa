class SysException(Exception):
    """
    系统统一异常，包含错误码和错误信息，支持静态工厂方法快速创建常见异常。
    """
    
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(message)
    
    @staticmethod
    def invalid_param(msg: str) -> 'SysException':
        """参数非法"""
        return SysException(400, msg)
    
    @staticmethod
    def conflict(msg: str) -> 'SysException':
        """资源冲突（如已存在）"""
        return SysException(409, msg)
    
    @staticmethod
    def system_error(msg: str = "系统异常") -> 'SysException':
        """未知系统错误"""
        return SysException(500, msg)
    
    @staticmethod
    def unauthorized(msg: str = "未授权") -> 'SysException':
        """未授权"""
        return SysException(401, msg)
    
    @staticmethod
    def not_found(msg: str = "资源未找到") -> 'SysException':
        """未找到资源"""
        return SysException(404, msg)