#!/usr/bin/env python3
"""
修复所有接口问题并测试
"""
import sys
import os
import subprocess
import time
import json
import requests
from typing import Dict, Any

# 修复导入路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 直接测试创建草稿
from domain.req.draft_create_req_dto import DraftCreateReqDto
from entity.draft_entity import DraftEntity
from service.draft_service import DraftService

def test_field_mapping():
    """测试字段映射是否正确"""
    print("🔍 测试字段映射...")
    
    # 创建测试DTO
    dto = DraftCreateReqDto(
        width=1920,
        height=1080,
        fps=30,
        name="测试草稿",
        draftPath="./test_data/test_draft"
    )
    
    print(f"DTO字段: {dto.dict()}")
    print(f"DTO.draftPath: {dto.draftPath}")
    
    # 测试映射到实体
    entity = DraftEntity(
        id="test-id",
        draft_name=dto.name,
        width=dto.width,
        height=dto.height,
        fps=dto.fps,
        draft_path=dto.draftPath
    )
    
    print(f"实体字段: {entity.dict()}")
    return True

def test_service():
    """测试服务层"""
    print("🔍 测试服务层...")
    service = DraftService()
    
    dto = DraftCreateReqDto(
        width=1920,
        height=1080,
        fps=30,
        name="测试草稿",
        draftPath="./test_data/test_draft"
    )
    
    try:
        result = service.create_draft_script(dto)
        print(f"✅ 服务测试成功: {result}")
        return result
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_simple_test():
    """运行简单测试"""
    print("🎯 运行接口修复测试...")
    
    # 1. 测试字段映射
    test_field_mapping()
    
    # 2. 测试服务
    result = test_service()
    
    if result:
        print(f"✅ 所有测试通过！草稿ID: {result.draft_id}")
        return True
    else:
        print("❌ 测试失败")
        return False

if __name__ == "__main__":
    success = run_simple_test()
    sys.exit(0 if success else 1)