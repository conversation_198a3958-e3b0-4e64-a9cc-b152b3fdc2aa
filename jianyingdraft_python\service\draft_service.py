import uuid
import os
import json
import zipfile
import io
from typing import Dict, Any, Optional
from jianyingdraft_python.entity.draft_entity import DraftEntity
from jianyingdraft_python.domain.rep.draft_create_rep_dto import DraftCreateRepDto
from jianyingdraft_python.domain.rep.draft_result_res_dto import DraftResultResDto
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.exception.sys_exception import SysException


class DraftService:
    """
    草稿服务类
    """

    # 使用类变量实现简单的持久化存储
    _drafts: Dict[str, DraftEntity] = {}
    _tracks: Dict[str, Any] = {}
    _video_segments: Dict[str, Any] = {}
    _audio_segments: Dict[str, Any] = {}
    _text_segments: Dict[str, Any] = {}

    def __init__(self):
        pass

    def create_draft_script(self, req_dto: DraftCreateReqDto) -> DraftCreateRepDto:
        """创建草稿"""
        draft_id = str(uuid.uuid4()).upper()
        
        draft_entity = DraftEntity(
            id=draft_id,
            draft_name=req_dto.name,
            width=req_dto.width,
            height=req_dto.height,
            fps=req_dto.fps,
            draft_path=req_dto.draftPath  # 这里使用draftPath从DTO映射到draft_path
        )
        
        # 创建草稿目录
        os.makedirs(req_dto.draftPath, exist_ok=True)
        
        # 保存草稿信息
        self._drafts[draft_id] = draft_entity
        
        # 保存草稿JSON文件
        draft_file_path = os.path.join(req_dto.draftPath, "draft.json")
        with open(draft_file_path, 'w', encoding='utf-8') as f:
            # 使用 model_dump 并处理 datetime 序列化
            draft_data = draft_entity.model_dump()
            # 手动处理 datetime 字段
            if 'create_time' in draft_data:
                draft_data['create_time'] = draft_data['create_time'].isoformat()
            if 'update_time' in draft_data:
                draft_data['update_time'] = draft_data['update_time'].isoformat()
            json.dump(draft_data, f, ensure_ascii=False, indent=2)
        
        return DraftCreateRepDto(
            draftId=draft_id,
            draftPath=req_dto.draftPath
        )

    def export_draft_as_zip(self, draft_id: str) -> bytes:
        """导出草稿为zip压缩包"""
        if draft_id not in self._drafts:
            raise SysException.not_found("草稿不存在")

        draft = self._drafts[draft_id]
        
        # 创建内存中的zip文件
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # 添加草稿JSON
            draft_json = json.dumps(draft.dict(), ensure_ascii=False, indent=2)
            zip_file.writestr("draft.json", draft_json)
            
            # 添加轨道信息
            tracks_data = {
                "tracks": [track.dict() for track in self._tracks.values() if track.draft_id == draft_id]
            }
            zip_file.writestr("tracks.json", json.dumps(tracks_data, ensure_ascii=False, indent=2))

            # 添加片段信息
            segments_data = {
                "video_segments": [seg.dict() for seg in self._video_segments.values() if seg.draft_id == draft_id],
                "audio_segments": [seg.dict() for seg in self._audio_segments.values() if seg.draft_id == draft_id],
                "text_segments": [seg.dict() for seg in self._text_segments.values() if seg.draft_id == draft_id]
            }
            zip_file.writestr("segments.json", json.dumps(segments_data, ensure_ascii=False, indent=2))
        
        return zip_buffer.getvalue()

    def get_draft_by_id(self, draft_id: str) -> Optional[DraftEntity]:
        """根据ID获取草稿"""
        return self._drafts.get(draft_id)

    def get_all_drafts(self) -> list[DraftEntity]:
        """获取所有草稿"""
        return list(self._drafts.values())