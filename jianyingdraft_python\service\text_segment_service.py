from typing import Dict, Any
import uuid
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity
from jianyingdraft_python.domain.req.text_segment_add_req_dto import TextSegmentAddReqDto
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.utils.draft_utils import DraftUtils
from jianyingdraft_python.utils.time_utils import TimeUtils


class TextSegmentService:
    """文本片段服务类"""
    
    def __init__(self):
        self.text_segments: Dict[str, TextSegmentEntity] = {}

    def add_text_segment(self, req_dto: TextSegmentAddReqDto) -> TextSegmentEntity:
        """添加文本片段"""
        # 检查时间重叠
        DraftUtils.check_segment_overlap_in_track(
            draft_id=req_dto.draftId,
            track_id=req_dto.trackId,
            new_segment_timerange=req_dto.targetRanger,
            segment_type="text",
            exclude_segment_id=None,
            segments_data={"text_segments": self.text_segments}
        )
        
        # 计算实际时间范围
        target_timerange = DraftUtils.build_complete_target_timerange(
            original_timerange=req_dto.targetRanger,
            after_segment_id=req_dto.afterSegmentId,
            calculated_duration="5s",  # 文本默认时长
            segments_data={"text_segments": self.text_segments}
        )
        
        real_target_timerange = TimeUtils.calculate_timeline(target_timerange)
        
        segment_id = str(uuid.uuid4()).upper()
        segment = TextSegmentEntity(
            id=segment_id,
            content=req_dto.text,
            draft_id=req_dto.draftId,
            style=req_dto.style,
            border=req_dto.border,
            background=req_dto.background,
            target_timerange=target_timerange,
            real_target_timerange=real_target_timerange,
            track_id=req_dto.trackId
        )
        
        self.text_segments[segment_id] = segment
        return segment

    def get_text_segments_by_draft(self, draft_id: str) -> list[TextSegmentEntity]:
        """获取草稿的文本片段"""
        return [seg for seg in self.text_segments.values() if seg.draft_id == draft_id]