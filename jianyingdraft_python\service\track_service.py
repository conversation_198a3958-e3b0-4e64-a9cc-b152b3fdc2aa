from typing import Dict, Any
from jianyingdraft_python.entity.track_entity import TrackEntity
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.exception.sys_exception import SysException
import uuid


class TrackService:
    """轨道服务类"""
    
    def __init__(self):
        self.tracks: Dict[str, TrackEntity] = {}

    def add_track(self, req_dto: TrackAddReqDto) -> TrackEntity:
        """添加轨道"""
        track_id = str(uuid.uuid4()).upper()
        
        track = TrackEntity(
            id=track_id,
            draft_id=req_dto.draftId,
            track_type=req_dto.trackType,
            track_name=req_dto.trackName,
            mute=req_dto.mute,
            relative_index=req_dto.relativeIndex,
            absolute_index=req_dto.absoluteIndex
        )
        
        self.tracks[track_id] = track
        return track

    def get_tracks_by_draft(self, draft_id: str) -> list[TrackEntity]:
        """获取草稿的轨道"""
        return [track for track in self.tracks.values() if track.draft_id == draft_id]

    def delete_track(self, track_id: str):
        """删除轨道"""
        if track_id not in self.tracks:
            raise SysException.not_found("轨道不存在")
        del self.tracks[track_id]