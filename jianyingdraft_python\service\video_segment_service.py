from typing import Dict, Any
import uuid
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.utils.draft_utils import DraftUtils
from jianyingdraft_python.utils.time_utils import TimeUtils


class VideoSegmentService:
    """视频片段服务类"""
    
    def __init__(self):
        self.video_segments: Dict[str, VideoSegmentEntity] = {}

    def add_video_segment(self, req_dto: MediaSegmentAddReqDto) -> VideoSegmentEntity:
        """添加视频片段"""
        # 检查时间重叠
        DraftUtils.check_segment_overlap_in_track(
            draft_id=req_dto.draftId,
            track_id=req_dto.trackId,
            new_segment_timerange=req_dto.targetTimerange,
            segment_type="video",
            exclude_segment_id=None,
            segments_data={"video_segments": self.video_segments}
        )
        
        # 计算实际时间范围
        target_timerange = DraftUtils.build_complete_target_timerange(
            original_timerange=req_dto.targetTimerange,
            after_segment_id=req_dto.afterSegmentId,
            calculated_duration=req_dto.sourceTimerange.duration if req_dto.sourceTimerange else "10s",
            segments_data={"video_segments": self.video_segments}
        )
        
        real_target_timerange = TimeUtils.calculate_timeline(target_timerange)
        
        segment_id = str(uuid.uuid4()).upper()
        segment = VideoSegmentEntity(
            id=segment_id,
            draft_id=req_dto.draftId,
            target_timerange=target_timerange,
            real_target_timerange=real_target_timerange,
            source_timerange=req_dto.sourceTimerange,
            resource_path=req_dto.resourcePath,
            track_id=req_dto.trackId,
            media_info=None,
            clip_settings=req_dto.clipSettings,
            speed=req_dto.speed
        )
        
        self.video_segments[segment_id] = segment
        return segment

    def get_video_segments_by_draft(self, draft_id: str) -> list[VideoSegmentEntity]:
        """获取草稿的视频片段"""
        return [seg for seg in self.video_segments.values() if seg.draft_id == draft_id]