import os
import json
import uuid
from typing import Dict, Any, Optional, List
from pathlib import Path
from jianyingdraft_python.entity.draft_entity import DraftEntity
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity
from jianyingdraft_python.entity.track_entity import TrackEntity


class DataStore:
    """
    数据存储管理器 - 使用JSON文件持久化存储
    """
    
    def __init__(self, data_dir: str = "./data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 数据文件路径
        self.drafts_file = self.data_dir / "drafts.json"
        self.video_segments_file = self.data_dir / "video_segments.json"
        self.audio_segments_file = self.data_dir / "audio_segments.json"
        self.text_segments_file = self.data_dir / "text_segments.json"
        self.tracks_file = self.data_dir / "tracks.json"
        
        # 内存缓存
        self._drafts: Dict[str, DraftEntity] = {}
        self._video_segments: Dict[str, VideoSegmentEntity] = {}
        self._audio_segments: Dict[str, AudioSegmentEntity] = {}
        self._text_segments: Dict[str, TextSegmentEntity] = {}
        self._tracks: Dict[str, TrackEntity] = {}
        
        # 加载数据
        self._load_all_data()
    
    def _load_all_data(self):
        """加载所有数据到内存"""
        self._drafts = self._load_data(self.drafts_file, DraftEntity)
        self._video_segments = self._load_data(self.video_segments_file, VideoSegmentEntity)
        self._audio_segments = self._load_data(self.audio_segments_file, AudioSegmentEntity)
        self._text_segments = self._load_data(self.text_segments_file, TextSegmentEntity)
        self._tracks = self._load_data(self.tracks_file, TrackEntity)
    
    def _load_data(self, file_path: Path, entity_class) -> Dict[str, Any]:
        """从JSON文件加载数据"""
        if not file_path.exists():
            return {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return {k: entity_class(**v) for k, v in data.items()}
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return {}
    
    def _save_data(self, file_path: Path, data: Dict[str, Any]):
        """保存数据到JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump({k: v.dict() for k, v in data.items()}, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving {file_path}: {e}")
    
    def save_draft(self, draft: DraftEntity):
        """保存草稿"""
        self._drafts[draft.id] = draft
        self._save_data(self.drafts_file, self._drafts)
    
    def get_draft(self, draft_id: str) -> Optional[DraftEntity]:
        """获取草稿"""
        return self._drafts.get(draft_id)
    
    def get_all_drafts(self) -> List[DraftEntity]:
        """获取所有草稿"""
        return list(self._drafts.values())
    
    def save_video_segment(self, segment: VideoSegmentEntity):
        """保存视频片段"""
        self._video_segments[segment.id] = segment
        self._save_data(self.video_segments_file, self._video_segments)
    
    def get_video_segments_by_draft(self, draft_id: str) -> List[VideoSegmentEntity]:
        """获取草稿的视频片段"""
        return [seg for seg in self._video_segments.values() if seg.draft_id == draft_id]
    
    def save_audio_segment(self, segment: AudioSegmentEntity):
        """保存音频片段"""
        self._audio_segments[segment.id] = segment
        self._save_data(self.audio_segments_file, self._audio_segments)
    
    def get_audio_segments_by_draft(self, draft_id: str) -> List[AudioSegmentEntity]:
        """获取草稿的音频片段"""
        return [seg for seg in self._audio_segments.values() if seg.draft_id == draft_id]
    
    def save_text_segment(self, segment: TextSegmentEntity):
        """保存文本片段"""
        self._text_segments[segment.id] = segment
        self._save_data(self.text_segments_file, self._text_segments)
    
    def get_text_segments_by_draft(self, draft_id: str) -> List[TextSegmentEntity]:
        """获取草稿的文本片段"""
        return [seg for seg in self._text_segments.values() if seg.draft_id == draft_id]
    
    def save_track(self, track: TrackEntity):
        """保存轨道"""
        self._tracks[track.id] = track
        self._save_data(self.tracks_file, self._tracks)
    
    def get_tracks_by_draft(self, draft_id: str) -> List[TrackEntity]:
        """获取草稿的轨道"""
        return [track for track in self._tracks.values() if track.draft_id == draft_id]
    
    def delete_track(self, track_id: str):
        """删除轨道"""
        if track_id in self._tracks:
            del self._tracks[track_id]
            self._save_data(self.tracks_file, self._tracks)
    
    def get_segments_data(self) -> Dict[str, Any]:
        """获取所有片段数据（用于DraftUtils）"""
        return {
            "video_segments": self._video_segments,
            "audio_segments": self._audio_segments,
            "text_segments": self._text_segments
        }