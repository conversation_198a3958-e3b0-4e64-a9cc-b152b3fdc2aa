"""
运行所有测试的脚本
"""
import subprocess
import sys
import os

def install_requests():
    """安装requests库用于测试"""
    try:
        import requests
        print("✅ requests 已安装")
    except ImportError:
        print("📦 正在安装 requests...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
        print("✅ requests 安装完成")

def run_draft_tests():
    """运行草稿控制器测试"""
    print("🚀 开始运行草稿控制器测试...")
    try:
        # 确保在正确的目录下运行
        test_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(test_dir)
        
        # 运行测试
        result = subprocess.run([sys.executable, "test_draft_controller.py"], 
                              capture_output=True, text=True)
        
        print("=== 测试输出 ===")
        print(result.stdout)
        if result.stderr:
            print("=== 错误输出 ===")
            print(result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 准备测试环境...")
    
    # 安装依赖
    install_requests()
    
    # 运行测试
    success = run_draft_tests()
    
    if success:
        print("✅ 所有测试完成")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
