"""
测试创建草稿接口 - 单独测试
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000"

def test_create_draft():
    """测试创建草稿接口"""
    print("=== 测试创建草稿接口 ===")

    # 准备测试数据 - 根据 DraftCreateReqDto 结构
    # 使用当前目录下的测试路径
    import os
    test_draft_path = os.path.join(os.getcwd(), "test_draft_output")

    test_data = {
        "width": 1920,
        "height": 1080,
        "fps": 30,
        "name": "测试草稿",
        "draftPath": test_draft_path
    }
    
    print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/draft/create",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        except:
            print(f"响应内容 (非JSON): {response.text}")
        
        if response.status_code == 200:
            print("✅ 创建草稿成功")
            return True
        else:
            print("❌ 创建草稿失败")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保服务器已启动在 http://localhost:8000")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_health_check():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保服务器已启动在 http://localhost:8000")
        return False
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试创建草稿接口...")
    
    # 先测试健康检查
    if test_health_check():
        print()
        # 再测试创建草稿
        test_create_draft()
    else:
        print("服务器未启动，无法进行测试")
    
    print("\n测试完成")
