"""
直接测试服务方法 - 更高效
"""
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jianyingdraft_python.service.draft_service import DraftService
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto

def test_create_draft_service():
    """直接测试创建草稿服务方法"""
    print("=== 直接测试创建草稿服务方法 ===")

    # 创建服务实例
    draft_service = DraftService()

    # 准备测试数据
    test_draft_path = os.path.join(os.getcwd(), "test_draft_output")

    req_dto = DraftCreateReqDto(
        width=1920,
        height=1080,
        fps=30,
        name="测试草稿",
        draftPath=test_draft_path
    )

    print(f"请求数据: {req_dto.model_dump()}")

    try:
        # 直接调用服务方法
        result = draft_service.create_draft_script(req_dto)

        print(f"✅ 创建草稿成功")
        print(f"返回结果: {result.model_dump()}")

        # 检查文件是否创建
        draft_file = os.path.join(test_draft_path, "draft.json")
        if os.path.exists(draft_file):
            print(f"✅ 草稿文件已创建: {draft_file}")
            with open(draft_file, 'r', encoding='utf-8') as f:
                content = json.load(f)
                print(f"文件内容: {json.dumps(content, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 草稿文件未创建: {draft_file}")

        return True, result.draftId

    except Exception as e:
        print(f"❌ 创建草稿失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_get_all_drafts_service():
    """测试获取所有草稿服务方法"""
    print("\n=== 测试获取所有草稿服务方法 ===")

    draft_service = DraftService()

    try:
        drafts = draft_service.get_all_drafts()
        print(f"✅ 获取所有草稿成功，共 {len(drafts)} 个草稿")
        for draft in drafts:
            print(f"  - ID: {draft.id}, 名称: {draft.draft_name}, 路径: {draft.draft_path}")
        return True
    except Exception as e:
        print(f"❌ 获取所有草稿失败: {e}")
        return False

def test_get_draft_by_id_service(draft_id):
    """测试根据ID获取草稿服务方法"""
    print(f"\n=== 测试根据ID获取草稿服务方法 (ID: {draft_id}) ===")

    draft_service = DraftService()

    try:
        draft = draft_service.get_draft_by_id(draft_id)
        if draft:
            print(f"✅ 获取草稿成功")
            print(f"草稿信息: ID={draft.id}, 名称={draft.draft_name}, 路径={draft.draft_path}")
            return True
        else:
            print(f"❌ 草稿不存在")
            return False
    except Exception as e:
        print(f"❌ 获取草稿失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始直接测试草稿服务方法...")

    # 1. 测试创建草稿
    success, draft_id = test_create_draft_service()

    if success and draft_id:
        # 2. 测试获取所有草稿
        test_get_all_drafts_service()

        # 3. 测试根据ID获取草稿
        test_get_draft_by_id_service(draft_id)

    print("\n测试完成")
