"""
测试 VideoSegmentService - 视频片段服务
"""
import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.service.video_segment_service import VideoSegmentService
from jianyingdraft_python.service.draft_service import DraftService
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto

def test_add_video_segment_service():
    """直接测试添加视频片段服务方法"""
    print("=== 直接测试添加视频片段服务方法 ===")

    # 先创建一个草稿
    draft_service = DraftService()
    test_draft_path = os.path.join(os.getcwd(), "test_draft_output")

    draft_req = DraftCreateReqDto(
        width=1920,
        height=1080,
        fps=30,
        name="视频测试草稿",
        draftPath=test_draft_path
    )

    draft_result = draft_service.create_draft_script(draft_req)
    draft_id = draft_result.draftId
    print(f"✅ 创建测试草稿成功，ID: {draft_id}")

    # 创建视频片段服务实例
    video_service = VideoSegmentService()

    # 准备视频片段测试数据
    from jianyingdraft_python.domain.timerange import Timerange

    req_dto = MediaSegmentAddReqDto(
        draftId=draft_id,
        targetTimerange=Timerange(start="0s", duration="5s"),
        speed=1.0,
        volume=1.0,
        resourcePath="/test/video.mp4"
    )

    print(f"视频片段请求数据: {req_dto.model_dump()}")

    try:
        # 直接调用服务方法
        result = video_service.add_video_segment(req_dto)

        print(f"✅ 添加视频片段成功")
        print(f"返回结果: {result.model_dump()}")

        return True, result.id, draft_id

    except Exception as e:
        print(f"❌ 添加视频片段失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, draft_id

def test_get_video_segments_service(draft_id):
    """测试获取草稿的视频片段服务方法"""
    print(f"\n=== 测试获取草稿的视频片段服务方法 (草稿ID: {draft_id}) ===")

    video_service = VideoSegmentService()

    try:
        segments = video_service.get_video_segments_by_draft(draft_id)
        print(f"✅ 获取视频片段成功，共 {len(segments)} 个片段")
        for segment in segments:
            print(f"  - ID: {segment.id}, 资源路径: {segment.resource_path}")
        return True
    except Exception as e:
        print(f"❌ 获取视频片段失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始直接测试视频片段服务方法...")

    # 1. 测试添加视频片段
    success, segment_id, draft_id = test_add_video_segment_service()

    if success and segment_id:
        # 2. 测试获取草稿的视频片段
        test_get_video_segments_service(draft_id)

    print("\n测试完成")
