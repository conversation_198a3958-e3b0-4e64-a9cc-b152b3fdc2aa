"""
测试 draft_controller 的接口
"""
import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# API基础URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_root_endpoint():
    """测试根路径接口"""
    print("\n=== 测试根路径接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"根路径测试失败: {e}")
        return False

def test_create_draft():
    """测试创建草稿接口"""
    print("\n=== 测试创建草稿接口 ===")

    # 准备测试数据 - 根据 DraftCreateReqDto 结构
    test_data = {
        "width": 1920,
        "height": 1080,
        "fps": 30,
        "name": "测试草稿",
        "draftPath": "/test/draft/path"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/draft/create",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200 and result.get("data"):
                draft_id = result["data"].get("draft_id")
                print(f"创建成功，草稿ID: {draft_id}")
                return True, draft_id
        
        return False, None
    except Exception as e:
        print(f"创建草稿失败: {e}")
        return False, None

def test_get_all_drafts():
    """测试获取所有草稿接口"""
    print("\n=== 测试获取所有草稿接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/api/draft/all")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取所有草稿失败: {e}")
        return False

def test_get_draft_by_id(draft_id):
    """测试根据ID获取草稿接口"""
    print(f"\n=== 测试根据ID获取草稿接口 (ID: {draft_id}) ===")
    try:
        response = requests.get(f"{BASE_URL}/api/draft/{draft_id}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"根据ID获取草稿失败: {e}")
        return False

def test_export_draft(draft_id):
    """测试导出草稿接口"""
    print(f"\n=== 测试导出草稿接口 (ID: {draft_id}) ===")
    try:
        response = requests.get(f"{BASE_URL}/api/draft/export/{draft_id}")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 保存zip文件
            with open(f"test_export_{draft_id}.zip", "wb") as f:
                f.write(response.content)
            print(f"导出成功，文件大小: {len(response.content)} bytes")
            return True
        else:
            print(f"导出失败: {response.text}")
            return False
    except Exception as e:
        print(f"导出草稿失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试 Draft Controller 接口...")
    
    # 1. 测试健康检查
    if not test_health_check():
        print("❌ 服务器未启动或健康检查失败，请先启动服务器")
        return
    
    # 2. 测试根路径
    test_root_endpoint()
    
    # 3. 测试创建草稿
    success, draft_id = test_create_draft()
    if not success:
        print("❌ 创建草稿失败，无法继续后续测试")
        return
    
    # 4. 测试获取所有草稿
    test_get_all_drafts()
    
    # 5. 测试根据ID获取草稿
    if draft_id:
        test_get_draft_by_id(draft_id)
        
        # 6. 测试导出草稿
        test_export_draft(draft_id)
    
    print("\n=== Draft Controller 测试完成 ===")

if __name__ == "__main__":
    main()
