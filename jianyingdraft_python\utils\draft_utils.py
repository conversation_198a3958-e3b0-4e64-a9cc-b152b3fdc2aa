import logging
from typing import Optional, List
from jianyingdraft_python.domain.timerange import Timerange, TimerangeDto
from jianyingdraft_python.entity.draft_entity import DraftEntity
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)


class DraftUtils:
    """
    草稿工具类 - 提供草稿相关的通用操作
    """

    @staticmethod
    def find_segment_target_timerange(segment_id: str, segments_data: dict) -> Optional[TimerangeDto]:
        """
        根据片段ID查询任意类型的片段，并返回其目标时间范围
        """
        logger.debug(f"查询片段目标时间范围，segmentId: {segment_id}")

        # 从视频片段中查找
        if segment_id in segments_data.get("video_segments", {}):
            segment = segments_data["video_segments"][segment_id]
            logger.debug(f"找到视频片段，segmentId: {segment_id}")
            return segment.real_target_timerange

        # 从音频片段中查找
        if segment_id in segments_data.get("audio_segments", {}):
            segment = segments_data["audio_segments"][segment_id]
            logger.debug(f"找到音频片段，segmentId: {segment_id}")
            return segment.real_target_timerange

        # 从文本片段中查找
        if segment_id in segments_data.get("text_segments", {}):
            segment = segments_data["text_segments"][segment_id]
            logger.debug(f"找到文本片段，segmentId: {segment_id}")
            return segment.real_target_ranger

        logger.warning(f"未找到指定的片段，segmentId: {segment_id}")
        return None

    @staticmethod
    def calculate_start_time_by_after_segment(after_segment_id: Optional[str], segments_data: dict) -> Optional[int]:
        """
        根据 afterSegmentId 计算新片段的起始时间
        """
        if after_segment_id is None or after_segment_id.strip() == "":
            return None

        logger.debug(f"计算新片段起始时间，afterSegmentId: {after_segment_id}")

        target_timerange = DraftUtils.find_segment_target_timerange(after_segment_id, segments_data)
        if target_timerange is None:
            raise SysException.not_found(f"指定的前置片段不存在: {after_segment_id}")

        new_start_time = target_timerange.start + target_timerange.duration
        logger.debug(f"计算得出新片段起始时间: {new_start_time} 微秒，afterSegmentId: {after_segment_id}")

        return new_start_time

    @staticmethod
    def build_complete_target_timerange(
        original_timerange: Timerange,
        after_segment_id: Optional[str],
        calculated_duration: str,
        segments_data: dict
    ) -> Timerange:
        """
        构建完整的目标时间范围
        """
        if original_timerange.start is not None:
            # 如果原始请求中提供了start时间，则使用原始值
            logger.debug(f"使用原始提供的起始时间: {original_timerange.start}")
            start_time = original_timerange.start
        elif after_segment_id is not None and after_segment_id.strip() != "":
            # 如果提供了afterSegmentId，则计算起始时间
            calculated_start_micros = DraftUtils.calculate_start_time_by_after_segment(
                after_segment_id, segments_data
            )
            if calculated_start_micros is None:
                raise SysException.system_error("无法计算片段起始时间")

            # 将微秒转换为字符串格式（秒）
            calculated_start_seconds = calculated_start_micros / TimeUtils.SEC
            start_time_str = f"{calculated_start_seconds}s"
            logger.debug(f"根据afterSegmentId计算的起始时间: {start_time_str}")
            start_time = start_time_str
        else:
            # 如果既没有start也没有afterSegmentId，则默认从0开始
            logger.debug("使用默认起始时间: 0s")
            start_time = "0s"

        return Timerange(
            start=start_time,
            duration=calculated_duration
        )

    @staticmethod
    def check_segment_overlap_in_track(
        draft_id: str,
        track_id: Optional[str],
        new_segment_timerange: Timerange,
        segment_type: str,
        exclude_segment_id: Optional[str],
        segments_data: dict
    ):
        """
        检查同一轨道上的同类型片段是否存在时间重叠
        """
        logger.debug(
            f"检查同类型片段轨道重叠，draftId: {draft_id}, trackId: {track_id}, segmentType: {segment_type}, "
            f"时间范围: start={new_segment_timerange.start}, duration={new_segment_timerange.duration}"
        )

        if new_segment_timerange.start is None or new_segment_timerange.duration is None:
            raise SysException.system_error("时间范围不完整，无法检查重叠")

        # 计算新片段的时间范围（微秒）
        start_str = new_segment_timerange.start
        duration_str = new_segment_timerange.duration
        new_start_micros = TimeUtils.tim(start_str)
        new_end_micros = new_start_micros + TimeUtils.tim(duration_str)

        # 查询同一轨道上的同类型片段
        same_track_segments = []

        # 获取对应类型的片段列表
        if segment_type.lower() == "video":
            video_segments = segments_data.get("video_segments", {})
            for segment_id, segment in video_segments.items():
                if segment.draft_id == draft_id and segment.track_id == track_id:
                    if exclude_segment_id is None or segment_id != exclude_segment_id:
                        same_track_segments.append(segment.real_target_timerange)
            logger.debug(f"找到 {len(video_segments)} 个同轨道视频片段")

        elif segment_type.lower() == "audio":
            audio_segments = segments_data.get("audio_segments", {})
            for segment_id, segment in audio_segments.items():
                if segment.draft_id == draft_id and segment.track_id == track_id:
                    if exclude_segment_id is None or segment_id != exclude_segment_id:
                        same_track_segments.append(segment.real_target_timerange)
            logger.debug(f"找到 {len(audio_segments)} 个同轨道音频片段")

        elif segment_type.lower() == "text":
            text_segments = segments_data.get("text_segments", {})
            for segment_id, segment in text_segments.items():
                if segment.draft_id == draft_id and segment.track_id == track_id:
                    if exclude_segment_id is None or segment_id != exclude_segment_id:
                        if segment.real_target_ranger is not None:
                            same_track_segments.append(segment.real_target_ranger)
            logger.debug(f"找到 {len(text_segments)} 个同轨道文本片段")

        else:
            logger.warning(f"未知的片段类型: {segment_type}")
            raise SysException.invalid_param(f"未知的片段类型: {segment_type}")

        logger.debug(f"同一轨道找到 {len(same_track_segments)} 个同类型现有片段")

        # 检查是否存在重叠
        for existing_segment in same_track_segments:
            existing_start = existing_segment.start
            existing_end = existing_start + existing_segment.duration

            # 重叠检测：新片段的开始时间 < 现有片段的结束时间 且 新片段的结束时间 > 现有片段的开始时间
            is_overlapping = new_start_micros < existing_end and new_end_micros > existing_start

            if is_overlapping:
                logger.warning(
                    f"检测到同类型片段重叠！新{segment_type}片段: {new_start_micros}微秒-{new_end_micros}微秒, "
                    f"现有{segment_type}片段: {existing_start}微秒-{existing_end}微秒"
                )
                raise SysException.conflict(
                    f"{segment_type}片段时间重叠！新片段时间范围 {new_start_micros / TimeUtils.SEC}s-{new_end_micros / TimeUtils.SEC}s "
                    f"与现有{segment_type}片段 {existing_start / TimeUtils.SEC}s-{existing_end / TimeUtils.SEC}s 存在重叠"
                )

        logger.debug("同类型片段轨道重叠检查通过，没有发现重叠片段")