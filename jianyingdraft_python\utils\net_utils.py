from urllib.parse import urlparse


class NetUtils:
    """
    网络工具类
    """

    @staticmethod
    def is_network_path(path: str) -> bool:
        """
        判断路径是否为网络路径

        Args:
            path: 文件路径

        Returns:
            true表示网络路径，false表示本地路径
        """
        try:
            parsed = urlparse(path)
            return parsed.scheme is not None and parsed.scheme.lower() in ["http", "https", "ftp"]
        except Exception:
            return False