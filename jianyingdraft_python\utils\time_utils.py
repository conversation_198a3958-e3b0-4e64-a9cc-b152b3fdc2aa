import logging
from typing import Union, TYPE_CHECKING
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.domain.meterial.media_info import MediaInfo

if TYPE_CHECKING:
    from jianyingdraft_python.domain.timerange import Timerange, TimerangeDto

logger = logging.getLogger(__name__)


class TimeUtils:
    SEC = 1_000_000  # 1秒 = 1,000,000 微秒

    @staticmethod
    def calculate_timeline(timerange) -> 'TimerangeDto':
        """计算时间线"""
        if timerange.duration is None:
            raise SysException.system_error("duration不能为空")
        if timerange.start is None:
            raise SysException.system_error("start不能为空")
        
        start_time_micros = TimeUtils.tim(timerange.start)
        duration_micros = TimeUtils.tim(timerange.duration)
        
        # 延迟导入避免循环导入
        from jianyingdraft_python.domain.timerange import TimerangeDto
        return TimerangeDto(start=start_time_micros, duration=duration_micros)

    @staticmethod
    def hand_duration(source_timerange, media_info: MediaInfo):
        """处理时长"""
        if source_timerange.duration is None and media_info.duration_seconds is not None:
            source_timerange.duration = media_info.duration_seconds
            return source_timerange
        elif source_timerange.duration is not None:
            user_duration = TimeUtils.tim(source_timerange.duration)
            duration_micros = media_info.duration_microseconds
            if duration_micros is not None:
                # 如果用户给的时间超过真正的时长那就取真正的时长
                if user_duration > duration_micros:
                    source_timerange.duration = media_info.duration_seconds
            return source_timerange
        else:
            logger.warning("音频片段时长无效，请检查资源是否合法")
            raise SysException.invalid_param("音频片段时长无效")

    @staticmethod
    def tim(inp: Union[int, float, str]) -> int:
        """时间转换工具"""
        if isinstance(inp, int):
            return inp
        elif isinstance(inp, float):
            return int(inp)
        elif isinstance(inp, str):
            try:
                return int(inp)
            except ValueError:
                pass
            
            sign = 1
            input_string = inp.strip().lower()
            
            if input_string.startswith("-"):
                sign = -1
                input_string = input_string[1:]
            
            total_time = 0
            
            time_units = [
                ('h', 3600 * TimeUtils.SEC),
                ('m', 60 * TimeUtils.SEC),
                ('s', TimeUtils.SEC)
            ]
            
            last_index = 0
            for unit_char, factor in time_units:
                unit_index = input_string.find(unit_char)
                if unit_index == -1:
                    continue
                
                value_string = input_string[last_index:unit_index]
                try:
                    total_time += int(float(value_string) * factor)
                except ValueError as e:
                    raise ValueError(f"Invalid number format in time string: {value_string}") from e
                last_index = unit_index + 1
            
            return total_time * sign
        else:
            raise TypeError("Unsupported input type. Expected str, int, or float.")