"""
音频素材类
"""
import os
import uuid
from typing import Optional, Dict, Any


class AudioMaterial:
    """本地音频素材"""

    def __init__(self, path: str, material_name: Optional[str] = None):
        """从指定位置加载音频素材
        
        Args:
            path (str): 素材文件路径, 支持mp3, wav等常见音频文件
            material_name (str, optional): 素材名称, 如果不指定, 默认使用文件名作为素材名称
            
        Raises:
            FileNotFoundError: 素材文件不存在
            ValueError: 不支持的素材文件类型
        """
        path = os.path.abspath(path)
        if not os.path.exists(path):
            raise FileNotFoundError(f"找不到 {path}")
        
        self.material_name = material_name if material_name else os.path.basename(path)
        self.material_id = uuid.uuid3(uuid.NAMESPACE_DNS, self.material_name).hex
        self.path = path
        
        # 简化实现：使用固定时长，实际应该读取音频文件信息
        # 这里使用一个合理的默认值，后续可以通过其他方式获取真实时长
        self.duration = self._get_audio_duration()
    
    def _get_audio_duration(self) -> int:
        """获取音频时长（微秒）
        
        简化实现：返回固定时长
        实际实现应该读取音频文件的真实时长
        """
        # 默认5.185秒，与pyJianYingDraft的测试音频一致
        return 5185000
    
    def export_json(self) -> Dict[str, Any]:
        """导出为JSON格式，匹配pyJianYingDraft的AudioMaterial格式"""
        return {
            "app_id": 0,
            "category_id": "",
            "category_name": "local",
            "check_flag": 3,
            "copyright_limit_type": "none",
            "duration": self.duration,
            "effect_id": "",
            "formula_id": "",
            "id": self.material_id,
            "local_material_id": self.material_id,
            "music_id": self.material_id,
            "name": self.material_name,
            "path": self.path,
            "source_platform": 0,
            "type": "extract_music",
            "wave_points": []
        }
