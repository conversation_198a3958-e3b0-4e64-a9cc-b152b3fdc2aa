"""
音频片段类
"""
import uuid
from copy import deepcopy
from typing import Optional, Union, List, Dict, Any

from .timerange import Timerange
from .segment import MediaSegment
from .audio_material import AudioMaterial


class AudioFade:
    """音频淡入淡出效果"""
    
    def __init__(self, in_duration: int, out_duration: int):
        """创建音频淡入淡出效果
        
        Args:
            in_duration (int): 淡入时长，微秒
            out_duration (int): 淡出时长，微秒
        """
        self.fade_id = uuid.uuid4().hex
        self.in_duration = in_duration
        self.out_duration = out_duration
    
    def export_json(self) -> Dict[str, Any]:
        """导出为JSON格式"""
        return {
            "id": self.fade_id,
            "type": "audio_fade",
            "fade_in_duration": self.in_duration,
            "fade_out_duration": self.out_duration
        }


class AudioEffect:
    """音频特效基类"""
    
    def __init__(self, effect_name: str, resource_id: str):
        """创建音频特效
        
        Args:
            effect_name (str): 特效名称
            resource_id (str): 资源ID
        """
        self.effect_id = uuid.uuid4().hex
        self.name = effect_name
        self.resource_id = resource_id
    
    def export_json(self) -> Dict[str, Any]:
        """导出为JSON格式"""
        return {
            "id": self.effect_id,
            "type": "audio_effect",
            "name": self.name,
            "resource_id": self.resource_id,
            "category_id": "audio_effect",
            "category_name": "音频特效"
        }


class AudioSegment(MediaSegment):
    """安放在轨道上的一个音频片段"""
    
    def __init__(self, material: Union[AudioMaterial, str], target_timerange: Timerange, *,
                 source_timerange: Optional[Timerange] = None, speed: Optional[float] = None, 
                 volume: float = 1.0, change_pitch: bool = False):
        """利用给定的音频素材构建一个轨道片段, 并指定其时间信息及播放速度/音量
        
        Args:
            material (AudioMaterial or str): 素材实例或素材路径, 若为路径则自动构造素材实例
            target_timerange (Timerange): 片段在轨道上的目标时间范围
            source_timerange (Timerange, optional): 截取的素材片段的时间范围, 默认从开头根据speed截取与target_timerange等长的一部分
            speed (float, optional): 播放速度, 默认为1.0. 此项与source_timerange同时指定时, 将覆盖target_timerange中的时长
            volume (float, optional): 音量, 默认为1.0
            change_pitch (bool, optional): 是否跟随变速改变音调, 默认为否
            
        Raises:
            ValueError: 指定的或计算出的source_timerange超出了素材的时长范围
        """
        if isinstance(material, str):
            material = AudioMaterial(material)
        
        # 处理时间范围和速度的逻辑，与pyJianYingDraft保持一致
        if source_timerange is not None and speed is not None:
            target_timerange = Timerange(target_timerange.start, round(source_timerange.duration / speed))
        elif source_timerange is not None and speed is None:
            speed = source_timerange.duration / target_timerange.duration
        else:  # source_timerange is None
            speed = speed if speed is not None else 1.0
            source_timerange = Timerange(0, round(target_timerange.duration * speed))
        
        if source_timerange.end > material.duration:
            raise ValueError(f"截取的素材时间范围 {source_timerange} 超出了素材时长({material.duration})")
        
        super().__init__(material.material_id, source_timerange, target_timerange, speed, volume, change_pitch)
        
        self.material_instance = deepcopy(material)
        self.fade: Optional[AudioFade] = None
        self.effects: List[AudioEffect] = []
    
    def add_fade(self, in_duration: Union[str, int], out_duration: Union[str, int]) -> "AudioSegment":
        """为音频片段添加淡入淡出效果
        
        Args:
            in_duration (int or str): 音频淡入时长, 单位为微秒, 若为字符串则会调用tim()函数进行解析
            out_duration (int or str): 音频淡出时长, 单位为微秒, 若为字符串则会调用tim()函数进行解析
            
        Raises:
            ValueError: 当前片段已存在淡入淡出效果
        """
        if self.fade is not None:
            raise ValueError("当前片段已存在淡入淡出效果")
        
        from .timerange import tim
        if isinstance(in_duration, str): 
            in_duration = tim(in_duration)
        if isinstance(out_duration, str): 
            out_duration = tim(out_duration)
        
        self.fade = AudioFade(in_duration, out_duration)
        self.extra_material_refs.append(self.fade.fade_id)
        
        return self
    
    def add_effect(self, effect_name: str, resource_id: str) -> "AudioSegment":
        """为音频片段添加音频特效
        
        Args:
            effect_name (str): 特效名称
            resource_id (str): 资源ID
        """
        effect = AudioEffect(effect_name, resource_id)
        self.effects.append(effect)
        self.extra_material_refs.append(effect.effect_id)
        
        return self
    
    def add_keyframe(self, time_offset: Union[str, int], volume: float) -> "AudioSegment":
        """为音频片段创建一个控制音量的关键帧, 并自动加入到关键帧列表中
        
        Args:
            time_offset (int or str): 关键帧的时间偏移量, 单位为微秒, 若为字符串则会调用tim()函数进行解析
            volume (float): 音量在time_offset处的值
        """
        from .timerange import tim
        if isinstance(time_offset, str):
            time_offset = tim(time_offset)
        
        # 简化实现：直接添加到extra_material_refs
        # 实际应该创建KeyframeList对象
        keyframe_id = uuid.uuid4().hex
        self.extra_material_refs.append(keyframe_id)
        
        return self
    
    def export_json(self) -> Dict[str, Any]:
        """导出为JSON格式"""
        json_dict = super().export_json()
        json_dict.update({
            "clip": None,
            "hdr_settings": None
        })
        return json_dict
