"""
草稿文件夹管理
"""
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional

from .draft_script import DraftScript


class DraftFolder:
    """草稿文件夹管理器"""
    
    def __init__(self, base_path: str):
        """
        Args:
            base_path: 草稿文件夹根目录
        """
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)
    
    def create_draft(self, draft_name: str, width: int = 1920, height: int = 1080, 
                    fps: int = 30, allow_replace: bool = False) -> DraftScript:
        """
        创建新草稿
        
        Args:
            draft_name: 草稿名称
            width: 视频宽度
            height: 视频高度
            fps: 帧率
            allow_replace: 是否允许替换同名草稿
        
        Returns:
            DraftScript对象
        """
        # 检查是否存在同名草稿
        existing_draft = self._find_draft_by_name(draft_name)
        if existing_draft:
            if allow_replace:
                # 删除现有草稿
                import shutil
                shutil.rmtree(existing_draft)
            else:
                raise ValueError(f"草稿已存在: {draft_name}")
        
        # 生成草稿ID和路径
        draft_id = uuid.uuid4().hex
        draft_path = self.base_path / draft_id
        draft_path.mkdir(exist_ok=True)
        
        # 创建子文件夹
        (draft_path / "segments").mkdir(exist_ok=True)
        (draft_path / "tracks").mkdir(exist_ok=True)
        (draft_path / "materials").mkdir(exist_ok=True)
        
        # 创建meta.json
        meta = {
            "draft_id": draft_id,
            "draft_name": draft_name,
            "width": width,
            "height": height,
            "fps": fps,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        with open(draft_path / "meta.json", "w", encoding="utf-8") as f:
            json.dump(meta, f, ensure_ascii=False, indent=2)
        
        return DraftScript(draft_path)
    
    def load_template(self, draft_name: str) -> DraftScript:
        """
        加载现有草稿

        Args:
            draft_name: 草稿名称

        Returns:
            DraftScript对象
        """
        draft_path = self._find_draft_by_name(draft_name)
        if not draft_path:
            raise ValueError(f"草稿不存在: {draft_name}")

        return DraftScript(draft_path)
    
    def load_draft_by_id(self, draft_id: str) -> DraftScript:
        """
        通过ID加载草稿
        
        Args:
            draft_id: 草稿ID
        
        Returns:
            DraftScript对象
        """
        draft_path = self.base_path / draft_id
        if not draft_path.exists():
            raise ValueError(f"草稿不存在: {draft_id}")
        
        return DraftScript(draft_path)
    
    def list_drafts(self) -> list:
        """
        列出所有草稿
        
        Returns:
            草稿信息列表
        """
        drafts = []
        for draft_dir in self.base_path.iterdir():
            if draft_dir.is_dir():
                meta_file = draft_dir / "meta.json"
                if meta_file.exists():
                    try:
                        with open(meta_file, "r", encoding="utf-8") as f:
                            meta = json.load(f)
                        drafts.append({
                            "draft_id": meta["draft_id"],
                            "draft_name": meta["draft_name"],
                            "created_at": meta["created_at"],
                            "path": str(draft_dir)
                        })
                    except Exception:
                        continue
        
        return drafts
    
    def _find_draft_by_name(self, draft_name: str) -> Optional[Path]:
        """
        通过名称查找草稿路径
        
        Args:
            draft_name: 草稿名称
        
        Returns:
            草稿路径或None
        """
        for draft_dir in self.base_path.iterdir():
            if draft_dir.is_dir():
                # 尝试查找meta.json或draft_meta_info.json
                meta_file = draft_dir / "meta.json"
                if not meta_file.exists():
                    meta_file = draft_dir / "draft_meta_info.json"

                if meta_file.exists():
                    try:
                        with open(meta_file, "r", encoding="utf-8") as f:
                            meta = json.load(f)
                        # 检查draft_name或直接使用目录名
                        if (meta.get("draft_name") == draft_name or
                            meta.get("draft_id") == draft_name or
                            draft_dir.name == draft_name):
                            return draft_dir
                    except Exception:
                        continue
        
        return None
