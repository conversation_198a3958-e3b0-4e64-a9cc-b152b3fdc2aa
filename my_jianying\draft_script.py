"""
草稿脚本管理
"""
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, List


class DraftScript:
    """草稿脚本管理器"""
    
    def __init__(self, draft_path: Path):
        """
        Args:
            draft_path: 草稿文件夹路径
        """
        self.draft_path = draft_path
        self.draft_id = draft_path.name
        self._load_meta()
        self.tracks = {}  # 轨道信息
        self.segments = []  # 片段数据
        self.materials = {}  # 材料数据

        # 如果存在draft_content.json，则加载已有内容
        self._load_existing_content()
    
    def _load_meta(self):
        """加载草稿元数据"""
        # 尝试加载meta.json，如果不存在则尝试draft_meta_info.json
        meta_file = self.draft_path / "meta.json"
        if not meta_file.exists():
            meta_file = self.draft_path / "draft_meta_info.json"

        if meta_file.exists():
            with open(meta_file, "r", encoding="utf-8") as f:
                self.meta = json.load(f)
        else:
            # 如果都不存在，创建默认meta
            self.meta = {
                "draft_id": self.draft_id,
                "draft_name": self.draft_id,
                "width": 1920,
                "height": 1080,
                "fps": 30
            }

    def _load_existing_content(self):
        """加载已有的草稿内容"""
        draft_content_file = self.draft_path / "draft_content.json"
        if not draft_content_file.exists():
            return  # 如果没有已有内容，跳过

        try:
            with open(draft_content_file, "r", encoding="utf-8") as f:
                content = json.load(f)



            # 加载轨道信息
            for track_data in content.get("tracks", []):
                track_type = track_data.get("type")
                track_id = track_data.get("id")

                if track_type and track_id:
                    self.tracks[track_type] = {
                        "track_id": track_id,
                        "segments": []
                    }

                    # 加载轨道中的片段
                    for segment_data in track_data.get("segments", []):
                        segment_info = {
                            "segment_id": segment_data.get("id"),
                            "material_id": segment_data.get("material_id"),
                            "segment_type": track_type,
                            "target_timerange": segment_data.get("target_timerange"),
                            "source_timerange": segment_data.get("source_timerange"),
                            "speed": segment_data.get("speed", 1.0),
                            "volume": segment_data.get("volume", 1.0),
                            "extra_material_refs": segment_data.get("extra_material_refs", [])
                        }

                        # 根据轨道类型添加特定信息
                        if track_type == "text":
                            # 文本片段的特殊处理
                            pass
                        elif track_type == "video":
                            # 视频片段的特殊处理
                            pass
                        elif track_type == "audio":
                            # 音频片段的特殊处理
                            pass

                        self.segments.append(segment_info)
                        self.tracks[track_type]["segments"].append(segment_info)


            # 加载材料信息
            materials = content.get("materials", {})
            self.existing_materials = {
                "videos": materials.get("videos", []),
                "audios": materials.get("audios", []),
                "texts": materials.get("texts", []),
                "speeds": materials.get("speeds", []),
                "material_animations": materials.get("material_animations", [])
            }



        except Exception as e:
            print(f"⚠️ 加载已有内容失败: {e}")
            # 如果加载失败，继续使用空的状态
    
    def add_track(self, track_type: str) -> str:
        """
        添加轨道
        
        Args:
            track_type: 轨道类型 (video/audio/text)
        
        Returns:
            轨道ID
        """
        track_id = f"{track_type}_{uuid.uuid4().hex[:8]}"
        
        track_data = {
            "track_id": track_id,
            "track_type": track_type,
            "segments": [],
            "created_at": datetime.now().isoformat()
        }
        
        # 保存轨道信息
        track_file = self.draft_path / "tracks" / f"{track_id}.json"
        with open(track_file, "w", encoding="utf-8") as f:
            json.dump(track_data, f, ensure_ascii=False, indent=2)
        
        self.tracks[track_id] = track_data
        return track_id
    
    def add_segment(self, segment):
        """
        添加片段到草稿

        Args:
            segment: 片段对象（VideoSegment或TextSegment）

        Returns:
            片段对象
        """
        # 设置script引用
        segment.script = self

        # 将片段数据添加到segments列表
        segment_data = self._convert_segment_to_data(segment)
        self.segments.append(segment_data)

        return segment

    def _convert_segment_to_data(self, segment) -> Dict[str, Any]:
        """将片段对象转换为数据字典"""
        from .video_segment import VideoSegment
        from .audio_segment import AudioSegment
        from .text_segment import TextSegment

        if isinstance(segment, VideoSegment):
            return {
                "segment_id": segment.segment_id,
                "segment_type": "video",
                "material_id": segment.material_id,
                "material_path": segment.material_path,
                "timerange": segment.timerange,
                "animations": [
                    {
                        "animation_id": anim.animation_id,
                        "animation_type": anim.animation_type,
                        "resource_id": anim.resource_id,
                        "effect_id": anim.effect_id,
                        "duration": anim.duration
                    }
                    for anim in segment.animations
                ],
                "filters": [
                    {
                        "filter_id": filt.filter_id,
                        "filter_type": filt.filter_type,
                        "resource_id": filt.resource_id,
                        "intensity": filt.intensity
                    }
                    for filt in segment.filters
                ],
                "transitions": [
                    {
                        "transition_id": trans.transition_id,
                        "transition_type": trans.transition_type,
                        "resource_id": trans.resource_id
                    }
                    for trans in segment.transitions
                ]
            }
        elif isinstance(segment, TextSegment):
            return {
                "segment_id": segment.segment_id,
                "segment_type": "text",
                "material_id": segment.material_id,
                "text": segment.text,
                "timerange": segment.timerange,
                "style": {
                    "size": segment.style.size,
                    "bold": segment.style.bold,
                    "italic": segment.style.italic,
                    "underline": segment.style.underline,
                    "color": segment.style.color,
                    "alpha": segment.style.alpha,
                    "align": segment.style.align,
                    "vertical": segment.style.vertical,
                    "letter_spacing": segment.style.letter_spacing,
                    "line_spacing": segment.style.line_spacing,
                    "auto_wrapping": segment.style.auto_wrapping,
                    "max_line_width": segment.style.max_line_width
                } if segment.style else None,
                "border": {
                    "alpha": segment.border.alpha,
                    "color": segment.border.color,
                    "width": segment.border.width
                } if segment.border else None,
                "background": {
                    "color": segment.background.color,
                    "style": segment.background.style,
                    "alpha": segment.background.alpha,
                    "round_radius": segment.background.round_radius,
                    "height": segment.background.height,
                    "width": segment.background.width,
                    "horizontal_offset": segment.background.horizontal_offset,
                    "vertical_offset": segment.background.vertical_offset
                } if segment.background else None,
                "animations": [
                    {
                        "animation_id": anim.animation_id,
                        "animation_type": anim.animation_type,
                        "resource_id": anim.resource_id,
                        "effect_id": anim.effect_id,
                        "duration": anim.duration
                    }
                    for anim in segment.animations
                ],
                "bubbles": [
                    {
                        "bubble_id": bubble.global_id,
                        "effect_id": bubble.effect_id,
                        "resource_id": bubble.resource_id
                    }
                    for bubble in segment.bubbles
                ],
                "effects": [
                    {
                        "effect_id": effect.global_id,
                        "effect_type": effect.effect_id,
                        "resource_id": effect.resource_id
                    }
                    for effect in segment.effects
                ]
            }
        elif isinstance(segment, AudioSegment):
            return {
                "segment_id": segment.segment_id,
                "segment_type": "audio",
                "material_id": segment.material_id,
                "material_path": segment.material_instance.path,
                "timerange": segment.target_timerange,
                "source_timerange": segment.source_timerange,
                "speed": segment.speed,
                "volume": segment.volume,
                "change_pitch": segment.change_pitch,
                "fade": {
                    "fade_id": segment.fade.fade_id,
                    "in_duration": segment.fade.in_duration,
                    "out_duration": segment.fade.out_duration
                } if segment.fade else None,
                "effects": [
                    {
                        "effect_id": effect.effect_id,
                        "name": effect.name,
                        "resource_id": effect.resource_id
                    }
                    for effect in segment.effects
                ]
            }
        else:
            raise ValueError(f"不支持的片段类型: {type(segment)}")
    
    def get_segment_by_id(self, segment_id: str) -> Optional['Segment']:
        """
        通过ID获取片段
        
        Args:
            segment_id: 片段ID
        
        Returns:
            片段对象或None
        """
        segments_dir = self.draft_path / "segments"
        
        # 查找对应的片段文件
        for segment_file in segments_dir.glob(f"*_{segment_id}.json"):
            with open(segment_file, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 根据类型创建对应的片段对象
            segment_type = data.get("segment_type")
            if segment_type == "video":
                from .video_segment import VideoSegment
                return VideoSegment.from_dict(data, self)
            elif segment_type == "audio":
                # TODO: 实现AudioSegment
                pass
            elif segment_type == "text":
                # TODO: 实现TextSegment
                pass
        
        return None
    
    def save(self):
        """
        导出为剪映格式的JSON文件
        """
        print(f"🔄 开始导出草稿: {self.meta['draft_name']}")

        # 1. 收集所有片段数据
        all_segments = self._collect_all_segments()
        print(f"📊 收集到 {len(all_segments)} 个片段")

        # 2. 生成draft_content.json
        draft_content = self._generate_draft_content(all_segments)

        # 3. 生成draft_meta_info.json
        draft_meta = self._generate_draft_meta(all_segments)

        # 4. 使用原始的draft_path作为导出目录（不创建新目录）
        export_dir = self.draft_path

        # 6. 保存三个JSON文件
        content_file = export_dir / "draft_content.json"
        meta_info_file = export_dir / "draft_meta_info.json"
        meta_file = export_dir / "meta.json"

        with open(content_file, "w", encoding="utf-8") as f:
            json.dump(draft_content, f, ensure_ascii=False, indent=2)

        with open(meta_info_file, "w", encoding="utf-8") as f:
            json.dump(draft_meta, f, ensure_ascii=False, indent=2)

        # 保存meta.json以便load_template能找到
        with open(meta_file, "w", encoding="utf-8") as f:
            json.dump(self.meta, f, ensure_ascii=False, indent=2)

        print(f"✅ 草稿已导出到: {export_dir}")
        print(f"   - {content_file.name}")
        print(f"   - {meta_file.name}")

        # 自动验证数据结构
        print("🔍 验证数据结构...")
        from .validator import DraftValidator
        validator = DraftValidator()
        is_valid = validator.validate_draft_folder(str(export_dir))

        if is_valid:
            print("✅ 数据结构验证通过！可以在剪映中正常打开")
        else:
            print("❌ 数据结构验证失败！")
            print(validator.get_validation_report())

        return export_dir
    
    def _collect_all_segments(self) -> List[Dict[str, Any]]:
        """收集所有片段数据"""
        # 直接返回存储在内存中的片段数据
        return self.segments
    
    def _generate_draft_content(self, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成draft_content.json的内容 - 完全匹配标准格式"""
        return {
            "canvas_config": {
                "width": self.meta["width"],
                "height": self.meta["height"],
                "ratio": "original"
            },
            "color_space": 0,
            "config": {
                "adjust_max_index": 1,
                "attachment_info": [],
                "combination_max_index": 1,
                "export_range": None,
                "extract_audio_last_index": 1,
                "lyrics_recognition_id": "",
                "lyrics_sync": True,
                "lyrics_taskinfo": [],
                "maintrack_adsorb": True,
                "material_save_mode": 0,
                "multi_language_current": "none",
                "multi_language_list": [],
                "multi_language_main": "none",
                "multi_language_mode": "none",
                "original_sound_last_index": 1,
                "record_audio_last_index": 1,
                "sticker_max_index": 1,
                "subtitle_keywords_config": None,
                "subtitle_recognition_id": "",
                "subtitle_sync": True,
                "subtitle_taskinfo": [],
                "system_font_list": [],
                "video_mute": False,
                "zoom_info_params": None
            },
            "cover": None,
            "create_time": 0,
            "duration": self._calculate_total_duration(segments),
            "extra_info": None,
            "fps": self.meta["fps"],
            "free_render_index_mode_on": False,
            "group_container": None,
            "id": self.draft_id,
            "keyframe_graph_list": [],
            "keyframes": {
                "adjusts": [],
                "audios": [],
                "effects": [],
                "filters": [],
                "handwrites": [],
                "stickers": [],
                "texts": [],
                "videos": []
            },
            "last_modified_platform": {
                "app_id": 3704,
                "app_source": "lv",
                "app_version": "5.9.0",
                "os": "windows"
            },
            "platform": {
                "app_id": 3704,
                "app_source": "lv",
                "app_version": "5.9.0",
                "os": "windows"
            },
            "materials": self._generate_materials_data(segments),
            "mutable_config": None,
            "name": "",
            "new_version": "110.0.0",
            "relationships": [],
            "render_index_track_mode_on": False,
            "retouch_cover": None,
            "source": "default",
            "static_cover_image_path": "",
            "time_marks": None,
            "tracks": self._generate_tracks_data(segments),
            "update_time": 0,
            "version": 360000
        }
    
    def _generate_draft_meta(self, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成draft_meta_info.json的内容"""
        return {
            "draft_fold_path": str(self.draft_path),
            "draft_id": self.draft_id,
            "draft_name": self.meta["draft_name"],
            "draft_root_path": str(self.draft_path.parent),
            "tm_draft_create": int(datetime.now().timestamp()),
            "tm_draft_modified": int(datetime.now().timestamp())
        }

    def _generate_materials_data(self, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成materials数据 - 完全匹配标准格式"""
        materials = {
            "ai_translates": [],
            "audio_balances": [],
            "audio_effects": [],
            "audio_fades": [],
            "audio_track_indexes": [],
            "audios": [],
            "beats": [],
            "canvases": [],
            "chromas": [],
            "color_curves": [],
            "digital_humans": [],
            "drafts": [],
            "effects": [],
            "flowers": [],
            "green_screens": [],
            "handwrites": [],
            "hsl": [],
            "images": [],
            "log_color_wheels": [],
            "loudnesses": [],
            "manual_deformations": [],
            "masks": [],
            "material_animations": [],
            "material_colors": [],
            "multi_language_refs": [],
            "placeholders": [],
            "plugin_effects": [],
            "primary_color_wheels": [],
            "realtime_denoises": [],
            "shapes": [],
            "smart_crops": [],
            "smart_relights": [],
            "sound_channel_mappings": [],
            "sounds": [],
            "speeds": [],
            "stickers": [],
            "tail_leaders": [],
            "text_templates": [],
            "texts": [],
            "time_marks": [],
            "transitions": [],
            "video_effects": [],
            "video_trackings": [],
            "videos": [],
            "vocal_beautifys": [],
            "vocal_separations": []
        }

        # 收集所有素材（按路径去重，确保相同文件只创建一个材料）
        video_materials = {}
        audio_materials = {}
        text_materials = {}
        speed_materials = {}
        video_path_to_material_id = {}  # 路径到材料ID的映射
        audio_path_to_material_id = {}  # 音频路径到材料ID的映射

        # 加载已有的材料
        if hasattr(self, 'existing_materials'):
            # 加载已有的视频材料
            for video_mat in self.existing_materials.get("videos", []):
                material_path = video_mat.get("path")
                material_id = video_mat.get("id")
                if material_path and material_id:
                    video_materials[material_path] = video_mat
                    video_path_to_material_id[material_path] = material_id

            # 加载已有的音频材料
            for audio_mat in self.existing_materials.get("audios", []):
                material_path = audio_mat.get("path")
                material_id = audio_mat.get("id")
                if material_path and material_id:
                    audio_materials[material_path] = audio_mat
                    audio_path_to_material_id[material_path] = material_id

            # 加载已有的文本材料
            for text_mat in self.existing_materials.get("texts", []):
                material_id = text_mat.get("id")
                if material_id:
                    text_materials[material_id] = text_mat

            # 加载已有的速度材料
            for speed_mat in self.existing_materials.get("speeds", []):
                material_id = speed_mat.get("id")
                if material_id:
                    speed_materials[material_id] = speed_mat



        for segment in segments:
            if segment.get("segment_type") == "video":
                material_path = segment.get("material_path")

                # 按路径去重视频材料
                if material_path and material_path not in video_path_to_material_id:
                    # 创建新的视频材料
                    video_material = self._create_video_material(segment)
                    video_materials[material_path] = video_material
                    video_path_to_material_id[material_path] = video_material["id"]

                    # 更新segment的material_id
                    segment["material_id"] = video_material["id"]
                elif material_path in video_path_to_material_id:
                    # 使用已存在的材料ID
                    segment["material_id"] = video_path_to_material_id[material_path]

                # 为每个片段创建speed材料
                segment_id = segment.get('segment_id')
                speed_id = f"speed_{segment_id}"
                if speed_id not in speed_materials:
                    speed_materials[speed_id] = self._create_speed_material(segment, speed_id)

            elif segment.get("segment_type") == "audio":
                material_path = segment.get("material_path")

                # 按路径去重音频材料
                if material_path and material_path not in audio_path_to_material_id:
                    # 创建新的音频材料
                    audio_material = self._create_audio_material(segment)
                    audio_materials[material_path] = audio_material
                    audio_path_to_material_id[material_path] = audio_material["id"]

                    # 更新segment的material_id
                    segment["material_id"] = audio_material["id"]
                elif material_path in audio_path_to_material_id:
                    # 使用已存在的材料ID
                    segment["material_id"] = audio_path_to_material_id[material_path]

                # 为每个音频片段创建speed材料
                segment_id = segment.get('segment_id')
                speed_id = f"speed_{segment_id}"
                if speed_id not in speed_materials:
                    speed_materials[speed_id] = self._create_speed_material(segment, speed_id)

            elif segment.get("segment_type") == "text":
                # 处理文本材料
                material_id = segment.get("material_id")
                if material_id and material_id not in text_materials:
                    text_materials[material_id] = self._create_text_material(segment)

        materials["videos"] = list(video_materials.values())
        materials["audios"] = list(audio_materials.values())
        materials["texts"] = list(text_materials.values())
        materials["speeds"] = list(speed_materials.values())

        # 收集所有动画、滤镜、转场等效果（按resource_id去重）
        animation_materials = {}
        filter_materials = {}
        transition_materials = {}

        for segment in segments:
            if segment.get("segment_type") == "video":
                # 添加动画（按animation_id去重）
                for animation in segment.get("animations", []):
                    animation_id = animation.get("animation_id")
                    if animation_id not in animation_materials:
                        animation_materials[animation_id] = self._create_animation_material(animation)

                # 添加滤镜（按resource_id去重）
                for filter_item in segment.get("filters", []):
                    resource_id = filter_item.get("resource_id")
                    if resource_id not in filter_materials:
                        filter_materials[resource_id] = self._create_filter_material(filter_item)

                # 添加转场（按resource_id去重）
                for transition in segment.get("transitions", []):
                    resource_id = transition.get("resource_id")
                    if resource_id not in transition_materials:
                        transition_materials[resource_id] = self._create_transition_material(transition)

            elif segment.get("segment_type") == "text":
                # 添加文本动画（按animation_id去重）
                for animation in segment.get("animations", []):
                    animation_id = animation.get("animation_id")
                    if animation_id not in animation_materials:
                        animation_materials[animation_id] = self._create_animation_material(animation)

        materials["material_animations"] = list(animation_materials.values())
        materials["effects"] = list(filter_materials.values())
        materials["transitions"] = list(transition_materials.values())

        return materials

    def _create_text_material(self, segment: Dict[str, Any]) -> Dict[str, Any]:
        """创建文本素材数据"""
        import json

        # 获取文本内容和样式
        text_content = segment.get("text", "")
        style = segment.get("style", {})
        border = segment.get("border")
        background = segment.get("background")

        # 构建content JSON - 匹配标准格式
        text_length = len(text_content)

        # 添加样式
        style_data = {
            "fill": {
                "alpha": style.get("alpha", 1.0) if style else 1.0,
                "content": {
                    "render_type": "solid",
                    "solid": {
                        "alpha": style.get("alpha", 1.0) if style else 1.0,
                        "color": list(style.get("color", [1.0, 1.0, 1.0])) if style else [1.0, 1.0, 1.0]
                    }
                }
            },
            "range": [0, text_length],
            "size": style.get("size", 8.0) if style else 8.0,
            "bold": style.get("bold", False) if style else False,
            "italic": style.get("italic", False) if style else False,
            "underline": style.get("underline", False) if style else False,
            "strokes": []
        }

        # 添加描边
        if border:
            stroke_data = {
                "content": {
                    "solid": {
                        "alpha": border.get("alpha", 1.0),
                        "color": list(border.get("color", [0.0, 0.0, 0.0]))
                    }
                },
                "width": border.get("width", 0.08)
            }
            style_data["strokes"].append(stroke_data)

        # 构建最终的content数据 - 标准格式：styles在前，text在后
        content_data = {
            "styles": [style_data],
            "text": text_content
        }

        # 基础文本材料数据
        text_material = {
            "alignment": style.get("align", 0) if style else 0,
            "background_alpha": 1.0,
            "background_color": "#FFFFFF",
            "background_height": 0.14,
            "background_horizontal_offset": 0.0,
            "background_round_radius": 0.0,
            "background_style": 0,
            "background_vertical_offset": 0.0,
            "background_width": 0.14,
            "check_flag": self._calculate_text_check_flag(segment),
            "content": json.dumps(content_data, ensure_ascii=False),
            "font_category": "system",
            "font_id": "",
            "font_path": "",
            "font_resource_id": "",
            "font_size": 8.0,
            "font_title": "",
            "font_url": "",
            "force_apply_line_max_width": False,  # 添加缺少的字段
            "global_alpha": style.get("alpha", 1.0) if style else 1.0,
            "has_shadow": False,
            "id": segment.get("material_id"),
            "initial_scale": 1.0,
            "is_rich_text": False,
            "letter_spacing": (style.get("letter_spacing", 0) * 0.05) if style else 0.0,
            "line_feed": 0,  # 添加缺少的字段
            "line_max_width": style.get("max_line_width", 0.82) if style else 0.82,
            "line_spacing": (style.get("line_spacing", 0) * 0.05 + 0.02) if style else 0.02,
            "preset_category": "",
            "preset_category_id": "",
            "preset_has_set_alignment": False,
            "preset_id": "",
            "preset_index": 0,
            "preset_name": "",
            "shadow_alpha": 0.9,
            "shadow_angle": -45.0,
            "shadow_color": [0.0, 0.0, 0.0],
            "shadow_distance": 5.0,
            "shadow_point": [1.0, 1.0],
            "shadow_smoothing": 1.0,
            "shape_clip_x": False,
            "shape_clip_y": False,
            "sub_type": 0,
            "text_alpha": 1.0,
            "text_color": [1.0, 1.0, 1.0],
            "text_curve": None,
            "text_preset_resource_id": "",
            "text_size": 30,
            "text_to_audio_ids": [],
            "type": "text",
            "typesetting": 1 if (style and style.get("vertical")) else 0,
            "use_effect_default_color": True,
            "words": {
                "end_time": [],
                "start_time": [],
                "text": []
            }
        }

        # 添加背景信息
        if background:
            text_material.update({
                "background_alpha": background.get("alpha", 1.0),
                "background_color": background.get("color", "#FFFFFF"),
                "background_height": background.get("height", 0.14),
                "background_horizontal_offset": background.get("horizontal_offset", 0.0),
                "background_round_radius": background.get("round_radius", 0.0),
                "background_style": background.get("style", 1),
                "background_vertical_offset": background.get("vertical_offset", 0.0),
                "background_width": background.get("width", 0.14)
            })

        return text_material

    def _calculate_text_check_flag(self, segment: Dict[str, Any]) -> int:
        """计算文本材料的check_flag"""
        flag = 7  # 基础文本标志

        # 检查是否有描边
        if segment.get("border"):
            flag |= 8  # 添加描边标志

        # 检查是否有背景
        if segment.get("background"):
            flag |= 16  # 添加背景标志

        return flag

    def _create_speed_material(self, segment: Dict[str, Any], speed_id: str) -> Dict[str, Any]:
        """创建速度素材数据 - 匹配标准格式"""
        return {
            "curve_speed": None,
            "id": speed_id,
            "mode": 0,
            "speed": segment.get("speed", 1.0),
            "type": "speed"
        }

    def _create_video_material(self, segment: Dict[str, Any]) -> Dict[str, Any]:
        """创建视频素材数据"""
        import os
        material_path = segment.get("material_path", "")

        # 获取视频文件的真实时长
        real_duration = self._get_video_duration(material_path)

        # 获取视频文件的真实分辨率
        real_width, real_height = self._get_video_resolution(material_path)

        return {
            "audio_fade": None,
            "category_id": "",
            "category_name": "local",
            "check_flag": 63487,
            "crop": {
                "upper_left_x": 0.0,
                "upper_left_y": 0.0,
                "upper_right_x": 1.0,
                "upper_right_y": 0.0,
                "lower_left_x": 0.0,
                "lower_left_y": 1.0,
                "lower_right_x": 1.0,
                "lower_right_y": 1.0
            },
            "crop_ratio": "free",
            "crop_scale": 1.0,
            "duration": real_duration,
            "height": real_height,
            "id": segment.get("material_id"),
            "local_material_id": "",
            "material_id": segment.get("material_id"),
            "material_name": os.path.basename(material_path) if material_path else "",
            "media_path": "",
            "path": material_path,
            "type": "video",
            "width": real_width
        }

    def _create_audio_material(self, segment: Dict[str, Any]) -> Dict[str, Any]:
        """创建音频素材数据"""
        import os
        material_path = segment.get("material_path", "")

        # 获取音频文件的真实时长（简化实现）
        real_duration = 5185000  # 默认5.185秒，实际应该读取音频文件信息

        return {
            "app_id": 0,
            "category_id": "",
            "category_name": "local",
            "check_flag": 3,
            "copyright_limit_type": "none",
            "duration": real_duration,
            "effect_id": "",
            "formula_id": "",
            "id": segment.get("material_id"),
            "local_material_id": segment.get("material_id"),
            "music_id": segment.get("material_id"),
            "name": os.path.basename(material_path) if material_path else "",
            "path": material_path,
            "source_platform": 0,
            "type": "extract_music",
            "wave_points": []
        }

    def _create_animation_material(self, animation: Dict[str, Any]) -> Dict[str, Any]:
        """创建动画素材数据 - 匹配标准格式"""
        return {
            "id": animation.get("animation_id"),
            "type": "sticker_animation",
            "multi_language_current": "none",
            "animations": [{
                "anim_adjust_params": None,
                "platform": "all",
                "panel": "",
                "material_type": "sticker",
                "name": animation.get("animation_type"),
                "id": animation.get("effect_id", "1644313"),  # 使用effect_id，默认为标准值
                "type": "in",  # 或 "out"，根据动画类型判断
                "resource_id": animation.get("resource_id"),
                "start": 0,
                "duration": 500000  # 默认动画时长，匹配标准
            }]
        }

    def _create_filter_material(self, filter_item: Dict[str, Any]) -> Dict[str, Any]:
        """创建滤镜素材数据 - 匹配标准格式"""
        return {
            "adjust_params": [],
            "algorithm_artifact_path": "",
            "apply_target_type": 0,
            "bloom_params": None,
            "category_id": "",
            "category_name": "",
            "color_match_info": {
                "source_feature_path": "",
                "target_feature_path": "",
                "target_image_path": ""
            },
            "effect_id": filter_item.get("resource_id", ""),  # 使用resource_id作为effect_id
            "enable_skin_tone_correction": False,
            "exclusion_group": [],
            "face_adjust_params": [],
            "formula_id": "",
            "id": filter_item.get("filter_id"),
            "intensity_key": "",
            "multi_language_current": "",
            "name": filter_item.get("filter_type"),
            "panel_id": "",
            "platform": "all",
            "resource_id": filter_item.get("resource_id"),
            "source_platform": 1,
            "sub_type": "none",
            "time_range": None,
            "type": "filter",
            "value": filter_item.get("intensity", 1.0) * 0.01,  # 转换强度值
            "version": ""
        }

    def _create_transition_material(self, transition: Dict[str, Any]) -> Dict[str, Any]:
        """创建转场素材数据 - 匹配标准格式"""
        return {
            "category_id": "",
            "category_name": "",
            "duration": 500000,  # 固定转场时长
            "effect_id": transition.get("resource_id", ""),  # 使用resource_id作为effect_id
            "id": transition.get("transition_id"),
            "is_overlap": True,
            "name": transition.get("transition_type"),
            "platform": "all",
            "resource_id": transition.get("resource_id"),
            "type": "transition"
        }
    
    def _generate_tracks_data(self, segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成轨道数据 - 符合剪映格式"""
        tracks_data = []

        # 合并新片段和已有片段
        all_segments = segments.copy()
        if hasattr(self, 'segments') and self.segments:
            # 添加已有的片段，但避免重复
            existing_segment_ids = {s.get("segment_id") for s in segments}
            for existing_seg in self.segments:
                if existing_seg.get("segment_id") not in existing_segment_ids:
                    all_segments.append(existing_seg)

        # 按类型分组所有片段
        video_segments = [s for s in all_segments if s.get("segment_type") == "video"]
        audio_segments = [s for s in all_segments if s.get("segment_type") == "audio"]
        text_segments = [s for s in all_segments if s.get("segment_type") == "text"]



        # 生成视频轨道
        if video_segments:
            # 使用已有的轨道ID或生成新的
            video_track_id = self.tracks.get("video", {}).get("track_id", uuid.uuid4().hex)
            video_track = {
                "attribute": 0,
                "flag": 0,
                "id": video_track_id,
                "is_default_name": False,
                "name": "video",
                "segments": [self._convert_video_segment_to_track_format(s) for s in video_segments],
                "type": "video"
            }
            tracks_data.append(video_track)

        # 生成文本轨道
        if text_segments:
            # 使用已有的轨道ID或生成新的
            text_track_id = self.tracks.get("text", {}).get("track_id", uuid.uuid4().hex)
            text_track = {
                "attribute": 0,
                "flag": 0,
                "id": text_track_id,
                "is_default_name": False,
                "name": "text",
                "segments": [self._convert_text_segment_to_track_format(s) for s in text_segments],
                "type": "text"
            }
            tracks_data.append(text_track)

        # 生成音频轨道
        if audio_segments:
            # 使用已有的轨道ID或生成新的
            audio_track_id = self.tracks.get("audio", {}).get("track_id", uuid.uuid4().hex)
            audio_track = {
                "attribute": 0,
                "flag": 0,
                "id": audio_track_id,
                "is_default_name": False,
                "name": "audio",
                "segments": [self._convert_audio_segment_to_track_format(s) for s in audio_segments],
                "type": "audio"
            }
            tracks_data.append(audio_track)

        return tracks_data

    def _convert_text_segment_to_track_format(self, segment_data: Dict[str, Any]) -> Dict[str, Any]:
        """将文本片段数据转换为轨道格式"""
        timerange = segment_data.get("timerange")

        # 处理timerange对象
        if hasattr(timerange, 'duration') and hasattr(timerange, 'start'):
            duration = timerange.duration
            start = timerange.start
        else:
            duration = timerange.get("duration", 0) if isinstance(timerange, dict) else 0
            start = timerange.get("start", 0) if isinstance(timerange, dict) else 0

        # 收集文本相关的额外素材引用
        extra_material_refs = []

        # 文本片段引用文本材料本身
        extra_material_refs.append(segment_data.get("material_id"))

        # 添加动画引用
        for animation in segment_data.get("animations", []):
            extra_material_refs.append(animation.get("animation_id"))

        # 添加气泡引用
        for bubble in segment_data.get("bubbles", []):
            extra_material_refs.append(bubble.get("bubble_id"))

        # 添加花字效果引用
        for effect in segment_data.get("effects", []):
            extra_material_refs.append(effect.get("effect_id"))

        return {
            "enable_adjust": True,
            "enable_color_correct_adjust": False,
            "enable_color_curves": True,
            "enable_color_match_adjust": False,
            "enable_color_wheels": True,
            "enable_lut": True,
            "enable_smart_color_adjust": False,
            "last_nonzero_volume": 1.0,
            "reverse": False,
            "track_attribute": 0,
            "track_render_index": 0,
            "visible": True,
            "id": segment_data.get("segment_id"),
            "material_id": segment_data.get("material_id"),
            "target_timerange": {
                "start": start,
                "duration": duration
            },
            "common_keyframes": [],
            "keyframe_refs": [],
            "source_timerange": None,  # 文本片段的source_timerange为null
            "speed": 1.0,
            "volume": 1.0,
            "extra_material_refs": extra_material_refs,
            "is_tone_modify": False,
            "clip": {
                "alpha": 1.0,
                "flip": {
                    "horizontal": False,
                    "vertical": False
                },
                "rotation": 0.0,
                "scale": {
                    "x": 1.0,
                    "y": 1.0
                },
                "transform": {
                    "x": 0.0,
                    "y": 0.0
                }
            },
            "uniform_scale": {
                "on": True,
                "value": 1.0
            },
            "render_index": 15000  # 文本片段的固定render_index
        }

    def _convert_audio_segment_to_track_format(self, segment_data: Dict[str, Any]) -> Dict[str, Any]:
        """将音频片段数据转换为轨道格式"""
        timerange = segment_data.get("timerange")
        source_timerange = segment_data.get("source_timerange")

        # 处理timerange对象
        if hasattr(timerange, 'duration') and hasattr(timerange, 'start'):
            duration = timerange.duration
            start = timerange.start
        else:
            duration = timerange.get("duration", 0) if isinstance(timerange, dict) else 0
            start = timerange.get("start", 0) if isinstance(timerange, dict) else 0

        # 处理source_timerange对象
        source_start = 0
        source_duration = duration
        if source_timerange:
            if hasattr(source_timerange, 'duration') and hasattr(source_timerange, 'start'):
                source_start = source_timerange.start
                source_duration = source_timerange.duration
            else:
                source_start = source_timerange.get("start", 0) if isinstance(source_timerange, dict) else 0
                source_duration = source_timerange.get("duration", duration) if isinstance(source_timerange, dict) else duration

        # 收集音频相关的额外素材引用
        extra_material_refs = []

        # 音频片段引用音频材料本身
        extra_material_refs.append(segment_data.get("material_id"))

        # 添加speed材料引用
        segment_id = segment_data.get('segment_id')
        speed_id = f"speed_{segment_id}"
        extra_material_refs.append(speed_id)

        # 添加淡入淡出引用
        fade = segment_data.get("fade")
        if fade:
            extra_material_refs.append(fade.get("fade_id"))

        # 添加音频特效引用
        for effect in segment_data.get("effects", []):
            extra_material_refs.append(effect.get("effect_id"))

        return {
            "enable_adjust": True,
            "enable_color_correct_adjust": False,
            "enable_color_curves": True,
            "enable_color_match_adjust": False,
            "enable_color_wheels": True,
            "enable_lut": True,
            "enable_smart_color_adjust": False,
            "last_nonzero_volume": segment_data.get("volume", 1.0),
            "reverse": False,
            "track_attribute": 0,
            "track_render_index": 0,
            "visible": True,
            "id": segment_data.get("segment_id"),
            "material_id": segment_data.get("material_id"),
            "target_timerange": {
                "start": start,
                "duration": duration
            },
            "common_keyframes": [],
            "keyframe_refs": [],
            "source_timerange": {
                "start": source_start,
                "duration": source_duration
            } if source_timerange else None,
            "speed": segment_data.get("speed", 1.0),
            "volume": segment_data.get("volume", 1.0),
            "extra_material_refs": extra_material_refs,
            "is_tone_modify": segment_data.get("change_pitch", False),
            "clip": None,  # 音频片段没有clip设置
            "hdr_settings": None,  # 音频片段没有HDR设置
            "render_index": 0  # 音频片段的固定render_index
        }

    def _convert_video_segment_to_track_format(self, segment_data: Dict[str, Any]) -> Dict[str, Any]:
        """将视频片段数据转换为轨道格式 - 完全匹配标准格式"""
        timerange = segment_data.get("timerange")

        # 处理timerange对象
        if hasattr(timerange, 'duration') and hasattr(timerange, 'start'):
            duration = timerange.duration
            start = timerange.start
        elif isinstance(timerange, dict):
            duration = timerange.get("duration", 0)
            start = timerange.get("start", 0)
        else:
            duration = 0
            start = 0

        # 收集所有额外素材引用，按标准格式顺序
        extra_material_refs = []

        # 添加speed引用（必须第一个）
        speed_id = f"speed_{segment_data['segment_id']}"
        extra_material_refs.append(speed_id)

        # 添加动画引用
        for animation in segment_data.get("animations", []):
            extra_material_refs.append(animation.get("animation_id"))

        # 添加滤镜引用
        for filter_item in segment_data.get("filters", []):
            extra_material_refs.append(filter_item.get("filter_id"))

        # 添加转场引用
        for transition in segment_data.get("transitions", []):
            extra_material_refs.append(transition.get("transition_id"))

        # 按标准格式的字段顺序返回
        return {
            "enable_adjust": True,
            "enable_color_correct_adjust": False,
            "enable_color_curves": True,
            "enable_color_match_adjust": False,
            "enable_color_wheels": True,
            "enable_lut": True,
            "enable_smart_color_adjust": False,
            "last_nonzero_volume": 1.0,
            "reverse": False,
            "track_attribute": 0,
            "track_render_index": 0,
            "visible": True,
            "id": segment_data["segment_id"],
            "material_id": segment_data.get("material_id", ""),
            "target_timerange": {
                "start": start,
                "duration": duration
            },
            "common_keyframes": [],
            "keyframe_refs": [],
            "source_timerange": {
                "start": 0,
                "duration": duration  # 使用用户指定的片段时长
            },
            "speed": segment_data.get("speed", 1.0),
            "volume": segment_data.get("volume", 1.0),
            "extra_material_refs": extra_material_refs,
            "is_tone_modify": False,
            "clip": {
                "alpha": 1.0,
                "flip": {
                    "horizontal": False,
                    "vertical": False
                },
                "rotation": 0.0,
                "scale": {
                    "x": 1.0,
                    "y": 1.0
                },
                "transform": {
                    "x": 0.0,
                    "y": 0.0
                }
            },
            "uniform_scale": {
                "on": True,
                "value": 1.0
            },
            "hdr_settings": {
                "intensity": 1.0,
                "mode": 1,
                "nits": 1000
            },
            "render_index": 0
        }
    
    def _calculate_total_duration(self, segments: List[Dict[str, Any]]) -> int:
        """计算总时长"""
        max_end = 0
        for segment in segments:
            timerange = segment.get("timerange")
            if timerange:
                if hasattr(timerange, 'start') and hasattr(timerange, 'duration'):
                    # Timerange对象
                    end = timerange.start + timerange.duration
                elif isinstance(timerange, dict):
                    # 字典格式
                    end = timerange.get("start", 0) + timerange.get("duration", 0)
                else:
                    continue
                max_end = max(max_end, end)
        return max_end
    
    def _add_segment_to_track(self, track_id: str, segment_id: str):
        """将片段添加到轨道"""
        if track_id in self.tracks:
            self.tracks[track_id]["segments"].append(segment_id)
            
            # 更新轨道文件
            track_file = self.draft_path / "tracks" / f"{track_id}.json"
            with open(track_file, "w", encoding="utf-8") as f:
                json.dump(self.tracks[track_id], f, ensure_ascii=False, indent=2)

    def _get_video_duration(self, video_path: str) -> int:
        """获取视频文件的真实时长（微秒）"""
        try:
            # 方法1: 尝试使用cv2
            try:
                import cv2
                cap = cv2.VideoCapture(video_path)
                if cap.isOpened():
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                    if fps > 0 and frame_count > 0:
                        duration_seconds = frame_count / fps
                        cap.release()
                        return int(duration_seconds * 1000000)
                cap.release()
            except ImportError:
                pass

            # 方法2: 尝试使用ffprobe
            try:
                import subprocess
                import json

                cmd = [
                    'ffprobe', '-v', 'quiet', '-print_format', 'json',
                    '-show_format', '-show_streams', video_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    info = json.loads(result.stdout)
                    duration_str = info.get('format', {}).get('duration')
                    if duration_str:
                        duration_seconds = float(duration_str)
                        return int(duration_seconds * 1000000)
            except Exception:
                pass

            # 方法3: 尝试使用moviepy
            try:
                from moviepy.editor import VideoFileClip
                with VideoFileClip(video_path) as clip:
                    duration_seconds = clip.duration
                    return int(duration_seconds * 1000000)
            except ImportError:
                pass

        except Exception as e:
            print(f"⚠️ 无法获取视频时长，使用默认值: {e}")

        # 如果所有方法都失败，返回默认值（5秒）
        print(f"⚠️ 所有方法都无法获取视频时长，使用默认值5秒")
        return 5000000

    def _get_video_resolution(self, video_path: str) -> tuple:
        """获取视频文件的真实分辨率"""
        try:
            # 方法1: 尝试使用cv2
            try:
                import cv2
                cap = cv2.VideoCapture(video_path)
                if cap.isOpened():
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    cap.release()
                    if width > 0 and height > 0:
                        return width, height
                cap.release()
            except ImportError:
                pass

            # 方法2: 尝试使用ffprobe
            try:
                import subprocess
                import json

                cmd = [
                    'ffprobe', '-v', 'quiet', '-print_format', 'json',
                    '-show_streams', '-select_streams', 'v:0', video_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    info = json.loads(result.stdout)
                    streams = info.get('streams', [])
                    if streams:
                        video_stream = streams[0]
                        width = video_stream.get('width', 1920)
                        height = video_stream.get('height', 1080)
                        return width, height
            except Exception:
                pass

            # 方法3: 尝试使用moviepy
            try:
                from moviepy.editor import VideoFileClip
                with VideoFileClip(video_path) as clip:
                    width, height = clip.size
                    return width, height
            except ImportError:
                pass

        except Exception as e:
            print(f"⚠️ 无法获取视频分辨率，使用默认值: {e}")

        # 如果所有方法都失败，返回默认值
        print(f"⚠️ 所有方法都无法获取视频分辨率，使用默认值1920x1080")
        return 1920, 1080
