"""
片段基类
"""
import uuid
from typing import Optional, List, Dict, Any
from .timerange import Timerange


class BaseSegment:
    """片段基类"""
    
    def __init__(self, material_id: str, target_timerange: Timerange):
        self.segment_id = uuid.uuid4().hex
        self.material_id = material_id
        self.target_timerange = target_timerange
        self.common_keyframes = []
    
    @property
    def start(self) -> int:
        """片段开始时间, 单位为微秒"""
        return self.target_timerange.start
    
    @start.setter
    def start(self, value: int):
        self.target_timerange.start = value
    
    @property
    def duration(self) -> int:
        """片段持续时间, 单位为微秒"""
        return self.target_timerange.duration
    
    @duration.setter
    def duration(self, value: int):
        self.target_timerange.duration = value
    
    def export_json(self) -> Dict[str, Any]:
        """返回通用于各种片段的属性"""
        return {
            "enable_adjust": True,
            "enable_color_correct_adjust": False,
            "enable_color_curves": True,
            "enable_color_match_adjust": False,
            "enable_color_wheels": True,
            "enable_lut": True,
            "enable_smart_color_adjust": False,
            "last_nonzero_volume": 1.0,
            "reverse": False,
            "track_attribute": 0,
            "track_render_index": 0,
            "visible": True,
            "id": self.segment_id,
            "material_id": self.material_id,
            "target_timerange": self.target_timerange.to_dict(),
            "common_keyframes": [],
            "keyframe_refs": [],
        }


class MediaSegment(BaseSegment):
    """媒体片段基类"""
    
    def __init__(self, material_id: str, source_timerange: Optional[Timerange], 
                 target_timerange: Timerange, speed: float, volume: float, change_pitch: bool):
        super().__init__(material_id, target_timerange)
        
        self.source_timerange = source_timerange
        self.speed = speed
        self.volume = volume
        self.change_pitch = change_pitch
        self.extra_material_refs = []
    
    def export_json(self) -> Dict[str, Any]:
        """返回媒体片段的属性"""
        json_dict = super().export_json()
        json_dict.update({
            "source_timerange": self.source_timerange.to_dict() if self.source_timerange else None,
            "speed": self.speed,
            "volume": self.volume,
            "extra_material_refs": self.extra_material_refs,
            "is_tone_modify": self.change_pitch
        })
        return json_dict
