"""
my_jianying 文本片段实现
完整移植pyJianYingDraft的文本功能
"""
import uuid
import json
from typing import Dict, Tuple, Any, Optional, Literal, Union
from copy import deepcopy

class TextStyle:
    """文本样式类"""
    
    def __init__(self, *, size: float = 8.0, bold: bool = False, italic: bool = False, underline: bool = False,
                 color: Tuple[float, float, float] = (1.0, 1.0, 1.0), alpha: float = 1.0,
                 align: Literal[0, 1, 2] = 0, vertical: bool = False,
                 letter_spacing: int = 0, line_spacing: int = 0,
                 auto_wrapping: bool = False, max_line_width: float = 0.82):
        """
        Args:
            size: 字体大小, 默认为8.0
            bold: 是否加粗, 默认为否
            italic: 是否斜体, 默认为否
            underline: 是否加下划线, 默认为否
            color: 字体颜色, RGB三元组, 取值范围为[0, 1], 默认为白色
            alpha: 字体不透明度, 取值范围[0, 1], 默认不透明
            align: 对齐方式, 0: 左对齐, 1: 居中, 2: 右对齐, 默认为左对齐
            vertical: 是否为竖排文本, 默认为否
            letter_spacing: 字符间距, 定义与剪映中一致, 默认为0
            line_spacing: 行间距, 定义与剪映中一致, 默认为0
            auto_wrapping: 是否自动换行, 默认关闭
            max_line_width: 每行最大行宽占屏幕宽度比例, 取值范围为[0, 1], 默认为0.82
        """
        self.size = size
        self.bold = bold
        self.italic = italic
        self.underline = underline
        
        self.color = color
        self.alpha = alpha
        
        self.align = align
        self.vertical = vertical
        
        self.letter_spacing = letter_spacing
        self.line_spacing = line_spacing
        
        self.auto_wrapping = auto_wrapping
        self.max_line_width = max_line_width

class TextBorder:
    """文本描边参数"""
    
    def __init__(self, *, alpha: float = 1.0, color: Tuple[float, float, float] = (0.0, 0.0, 0.0), width: float = 40.0):
        """
        Args:
            alpha: 描边不透明度, 取值范围[0, 1], 默认为1.0
            color: 描边颜色, RGB三元组, 取值范围为[0, 1], 默认为黑色
            width: 描边宽度, 与剪映中一致, 取值范围为[0, 100], 默认为40.0
        """
        self.alpha = alpha
        self.color = color
        self.width = width / 100.0 * 0.2  # 映射到剪映格式
    
    def export_json(self) -> Dict[str, Any]:
        """导出JSON数据"""
        return {
            "content": {
                "solid": {
                    "alpha": self.alpha,
                    "color": list(self.color),
                }
            },
            "width": self.width
        }

class TextBackground:
    """文本背景参数"""
    
    def __init__(self, *, color: str, style: Literal[1, 2] = 1, alpha: float = 1.0, round_radius: float = 0.0,
                 height: float = 0.14, width: float = 0.14,
                 horizontal_offset: float = 0.5, vertical_offset: float = 0.5):
        """
        Args:
            color: 背景颜色, 格式为'#RRGGBB'
            style: 背景样式, 1和2分别对应剪映中的两种样式, 默认为1
            alpha: 背景不透明度, 与剪映中一致, 取值范围[0, 1], 默认为1.0
            round_radius: 背景圆角半径, 与剪映中一致, 取值范围[0, 1], 默认为0.0
            height: 背景高度, 与剪映中一致, 取值范围为[0, 1], 默认为0.14
            width: 背景宽度, 与剪映中一致, 取值范围为[0, 1], 默认为0.14
            horizontal_offset: 背景水平偏移, 与剪映中一致, 取值范围为[0, 1], 默认为0.5
            vertical_offset: 背景竖直偏移, 与剪映中一致, 取值范围为[0, 1], 默认为0.5
        """
        self.style = style
        self.alpha = alpha
        self.color = color
        self.round_radius = round_radius
        self.height = height
        self.width = width
        self.horizontal_offset = horizontal_offset * 2 - 1
        self.vertical_offset = vertical_offset * 2 - 1
    
    def export_json(self) -> Dict[str, Any]:
        """生成JSON数据"""
        return {
            "background_style": self.style,
            "background_color": self.color,
            "background_alpha": self.alpha,
            "background_round_radius": self.round_radius,
            "background_height": self.height,
            "background_width": self.width,
            "background_horizontal_offset": self.horizontal_offset,
            "background_vertical_offset": self.vertical_offset,
        }

class TextBubble:
    """文本气泡效果"""
    
    def __init__(self, effect_id: str, resource_id: str):
        self.global_id = uuid.uuid4().hex
        self.effect_id = effect_id
        self.resource_id = resource_id
    
    def export_json(self) -> Dict[str, Any]:
        return {
            "apply_target_type": 0,
            "effect_id": self.effect_id,
            "id": self.global_id,
            "resource_id": self.resource_id,
            "type": "text_shape",
            "value": 1.0,
        }

class TextEffect(TextBubble):
    """文本花字效果"""
    
    def export_json(self) -> Dict[str, Any]:
        ret = super().export_json()
        ret["type"] = "text_effect"
        ret["source_platform"] = 1
        return ret

class TextAnimation:
    """文本动画"""
    
    def __init__(self, animation_type, animation_id: str = None):
        self.animation_id = animation_id or uuid.uuid4().hex
        self.animation_type = animation_type.value.name if hasattr(animation_type, 'value') else str(animation_type)
        self.resource_id = animation_type.value.resource_id if hasattr(animation_type, 'value') else ""
        self.effect_id = animation_type.value.effect_id if hasattr(animation_type, 'value') else ""
        self.duration = getattr(animation_type.value, 'duration', 0.5) if hasattr(animation_type, 'value') else 0.5

class TextSegment:
    """文本片段类"""
    
    def __init__(self, text: str, timerange, *,
                 font = None, style: Optional[TextStyle] = None,
                 border: Optional[TextBorder] = None, background: Optional[TextBackground] = None,
                 segment_id: str = None, script = None):
        """创建文本片段
        
        Args:
            text: 文本内容
            timerange: 片段在轨道上的时间范围
            font: 字体类型, 默认为系统字体
            style: 字体样式, 包含大小/颜色/对齐/透明度等
            border: 文本描边参数, 默认无描边
            background: 文本背景参数, 默认无背景
            segment_id: 片段ID，用于加载已存在的片段
            script: 脚本对象，用于加载已存在的片段
        """
        if segment_id and script:
            # 从已存在的片段加载
            self._load_from_script(segment_id, script)
        else:
            # 创建新片段
            self.segment_id = uuid.uuid4().hex
            self.material_id = uuid.uuid4().hex
            self.text = text
            self.timerange = timerange
            self.font = font
            self.style = style or TextStyle()
            self.border = border
            self.background = background
            
            # 效果列表
            self.animations = []
            self.bubbles = []
            self.effects = []
            
            # 脚本引用
            self.script = script
    
    def _load_from_script(self, segment_id: str, script):
        """从脚本中加载已存在的片段数据"""
        self.segment_id = segment_id
        self.script = script
        
        # 从script的segments数据中查找对应的片段
        segment_data = None
        for seg in script.segments:
            if seg.get("segment_id") == segment_id:
                segment_data = seg
                break
        
        if not segment_data:
            raise ValueError(f"找不到ID为 {segment_id} 的文本片段")
        
        # 加载基本信息
        self.material_id = segment_data.get("material_id")
        self.timerange = segment_data.get("timerange")
        
        # 从materials中加载文本材料信息
        text_material = None
        for material in script.materials.get("texts", []):
            if material.get("id") == self.material_id:
                text_material = material
                break
        
        if text_material:
            # 解析content JSON
            content_str = text_material.get("content", "{}")
            try:
                content = json.loads(content_str)
                self.text = content.get("text", "")
                
                # 解析样式
                styles = content.get("styles", [])
                if styles:
                    style_data = styles[0]
                    fill_data = style_data.get("fill", {}).get("content", {}).get("solid", {})
                    
                    self.style = TextStyle(
                        size=style_data.get("size", 8.0),
                        bold=style_data.get("bold", False),
                        italic=style_data.get("italic", False),
                        underline=style_data.get("underline", False),
                        color=tuple(fill_data.get("color", [1.0, 1.0, 1.0])),
                        alpha=text_material.get("global_alpha", 1.0),
                        align=text_material.get("alignment", 0),
                        vertical=bool(text_material.get("typesetting", 0)),
                        letter_spacing=int(text_material.get("letter_spacing", 0.0) / 0.05),
                        line_spacing=int((text_material.get("line_spacing", 0.02) - 0.02) / 0.05),
                        auto_wrapping=text_material.get("type") == "subtitle",
                        max_line_width=text_material.get("line_max_width", 0.82)
                    )
                    
                    # 解析描边
                    strokes = style_data.get("strokes", [])
                    if strokes:
                        stroke_data = strokes[0]
                        stroke_content = stroke_data.get("content", {}).get("solid", {})
                        self.border = TextBorder(
                            alpha=stroke_content.get("alpha", 1.0),
                            color=tuple(stroke_content.get("color", [0.0, 0.0, 0.0])),
                            width=stroke_data.get("width", 0.08) * 100.0 / 0.2
                        )
                    else:
                        self.border = None
                else:
                    self.style = TextStyle()
                    self.border = None
                
                # 解析背景
                if text_material.get("background_style"):
                    self.background = TextBackground(
                        color=text_material.get("background_color", "#FFFFFF"),
                        style=text_material.get("background_style", 1),
                        alpha=text_material.get("background_alpha", 1.0),
                        round_radius=text_material.get("background_round_radius", 0.0),
                        height=text_material.get("background_height", 0.14),
                        width=text_material.get("background_width", 0.14),
                        horizontal_offset=(text_material.get("background_horizontal_offset", 0.0) + 1) / 2,
                        vertical_offset=(text_material.get("background_vertical_offset", 0.0) + 1) / 2
                    )
                else:
                    self.background = None
                    
            except json.JSONDecodeError:
                self.text = ""
                self.style = TextStyle()
                self.border = None
                self.background = None
        else:
            self.text = ""
            self.style = TextStyle()
            self.border = None
            self.background = None
        
        # 加载动画、气泡、效果
        self.animations = segment_data.get("animations", [])
        self.bubbles = segment_data.get("bubbles", [])
        self.effects = segment_data.get("effects", [])
        
        # 设置字体
        self.font = None
    
    def add_animation(self, animation_type, duration: Union[str, float, None] = None):
        """添加文本动画"""
        animation = TextAnimation(animation_type)
        if duration is not None:
            if isinstance(duration, str):
                # 简单的时间解析
                if duration.endswith('s'):
                    animation.duration = float(duration[:-1])
                else:
                    animation.duration = float(duration) / 1000000  # 微秒转秒
            else:
                animation.duration = duration
        
        self.animations.append(animation)
        
        # 如果有脚本引用，更新脚本数据
        if self.script:
            self._update_script_data()
        
        return self
    
    def add_bubble(self, effect_id: str, resource_id: str):
        """添加气泡效果"""
        bubble = TextBubble(effect_id, resource_id)
        self.bubbles.append(bubble)
        
        if self.script:
            self._update_script_data()
        
        return self
    
    def add_effect(self, effect_id: str):
        """添加花字效果"""
        effect = TextEffect(effect_id, effect_id)
        self.effects.append(effect)
        
        if self.script:
            self._update_script_data()
        
        return self
    
    def _update_script_data(self):
        """更新脚本中的片段数据"""
        if not self.script:
            return
        
        # 更新segments中的数据
        for seg in self.script.segments:
            if seg.get("segment_id") == self.segment_id:
                seg["animations"] = [
                    {
                        "animation_id": anim.animation_id,
                        "animation_type": anim.animation_type,
                        "resource_id": anim.resource_id,
                        "effect_id": anim.effect_id,
                        "duration": anim.duration
                    }
                    for anim in self.animations
                ]
                seg["bubbles"] = [
                    {
                        "bubble_id": bubble.global_id,
                        "effect_id": bubble.effect_id,
                        "resource_id": bubble.resource_id
                    }
                    for bubble in self.bubbles
                ]
                seg["effects"] = [
                    {
                        "effect_id": effect.global_id,
                        "effect_type": effect.effect_id,
                        "resource_id": effect.resource_id
                    }
                    for effect in self.effects
                ]
                break
