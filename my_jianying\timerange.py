"""
时间范围处理
"""
import re
from typing import Union


class Timerange:
    """时间范围类"""
    
    def __init__(self, start: int, duration: int):
        """
        Args:
            start: 开始时间（微秒）
            duration: 持续时间（微秒）
        """
        self.start = start
        self.duration = duration
    
    @property
    def end(self):
        """结束时间"""
        return self.start + self.duration
    
    def to_dict(self):
        """转换为字典"""
        return {
            "start": self.start,
            "duration": self.duration
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建"""
        return cls(data["start"], data["duration"])
    
    def __repr__(self):
        return f"Timerange(start={self.start}, duration={self.duration})"
    
    def __eq__(self, other):
        if not isinstance(other, Timerange):
            return False
        return self.start == other.start and self.duration == other.duration


def parse_time_string(time_str: str) -> int:
    """
    解析时间字符串为微秒
    支持格式: "1s", "1.5s", "500ms", "1m30s"
    """
    if isinstance(time_str, (int, float)):
        return int(time_str * 1000000)  # 假设输入是秒

    time_str = time_str.strip().lower()
    total_microseconds = 0

    # 简化的解析逻辑
    if 'ms' in time_str:
        # 毫秒
        ms_value = float(time_str.replace('ms', ''))
        total_microseconds = int(ms_value * 1000)
    elif 's' in time_str:
        # 秒
        s_value = float(time_str.replace('s', ''))
        total_microseconds = int(s_value * 1000000)
    elif 'm' in time_str:
        # 分钟
        m_value = float(time_str.replace('m', ''))
        total_microseconds = int(m_value * 60 * 1000000)
    else:
        # 纯数字，假设是秒
        try:
            seconds = float(time_str)
            total_microseconds = int(seconds * 1000000)
        except ValueError:
            raise ValueError(f"无法解析时间字符串: {time_str}")

    return total_microseconds


def trange(start: Union[str, int, float], end_or_duration: Union[str, int, float]) -> Timerange:
    """
    创建时间范围
    
    Args:
        start: 开始时间
        end_or_duration: 结束时间或持续时间
    
    Returns:
        Timerange对象
    """
    start_microseconds = parse_time_string(start)
    end_or_duration_microseconds = parse_time_string(end_or_duration)
    
    # 如果end_or_duration比start大很多，认为是结束时间；否则认为是持续时间
    if end_or_duration_microseconds > start_microseconds:
        duration = end_or_duration_microseconds - start_microseconds
    else:
        duration = end_or_duration_microseconds
    
    return Timerange(start_microseconds, duration)


def tim(time_str: Union[str, int, float]) -> int:
    """
    解析时间字符串为微秒
    这是parse_time_string的别名，用于与pyJianYingDraft保持一致

    Args:
        time_str: 时间字符串或数值

    Returns:
        微秒数
    """
    return parse_time_string(time_str)
