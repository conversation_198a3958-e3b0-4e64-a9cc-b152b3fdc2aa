"""
剪映草稿数据结构验证器
"""
import json
from pathlib import Path
from typing import Dict, Any, List, Optional


class DraftValidator:
    """剪映草稿数据结构验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def validate_draft_folder(self, draft_folder_path: str) -> bool:
        """
        验证草稿文件夹的完整性
        
        Args:
            draft_folder_path: 草稿文件夹路径
            
        Returns:
            验证是否通过
        """
        self.errors.clear()
        self.warnings.clear()
        
        draft_path = Path(draft_folder_path)
        
        # 检查必需文件
        content_file = draft_path / "draft_content.json"
        meta_file = draft_path / "draft_meta_info.json"
        
        if not content_file.exists():
            self.errors.append("缺少 draft_content.json 文件")
            return False
        
        if not meta_file.exists():
            self.errors.append("缺少 draft_meta_info.json 文件")
            return False
        
        # 验证JSON格式
        try:
            with open(content_file, "r", encoding="utf-8") as f:
                content_data = json.load(f)
        except Exception as e:
            self.errors.append(f"draft_content.json 格式错误: {e}")
            return False
        
        try:
            with open(meta_file, "r", encoding="utf-8") as f:
                meta_data = json.load(f)
        except Exception as e:
            self.errors.append(f"draft_meta_info.json 格式错误: {e}")
            return False
        
        # 验证数据结构
        self._validate_content_structure(content_data)
        self._validate_meta_structure(meta_data)
        
        return len(self.errors) == 0
    
    def _validate_content_structure(self, data: Dict[str, Any]):
        """验证 draft_content.json 的数据结构"""
        
        # 检查必需的顶级字段
        required_fields = [
            "canvas_config", "create_time", "duration", "fps", "id", 
            "materials", "tracks", "version"
        ]
        
        for field in required_fields:
            if field not in data:
                self.errors.append(f"draft_content.json 缺少必需字段: {field}")
        
        # 验证 canvas_config
        if "canvas_config" in data:
            canvas = data["canvas_config"]
            if not isinstance(canvas, dict):
                self.errors.append("canvas_config 必须是对象")
            else:
                for field in ["width", "height"]:
                    if field not in canvas:
                        self.errors.append(f"canvas_config 缺少字段: {field}")
                    elif not isinstance(canvas[field], int):
                        self.errors.append(f"canvas_config.{field} 必须是整数")
        
        # 验证 materials
        if "materials" in data:
            self._validate_materials_structure(data["materials"])
        
        # 验证 tracks
        if "tracks" in data:
            self._validate_tracks_structure(data["tracks"])
        
        # 验证数据类型
        if "duration" in data and not isinstance(data["duration"], int):
            self.errors.append("duration 必须是整数")
        
        if "fps" in data and not isinstance(data["fps"], int):
            self.errors.append("fps 必须是整数")
    
    def _validate_materials_structure(self, materials: Dict[str, Any]):
        """验证 materials 结构"""
        if not isinstance(materials, dict):
            self.errors.append("materials 必须是对象")
            return
        
        # 检查必需的材料类型
        required_material_types = [
            "audios", "canvases", "chromas", "effects", "handwrites", 
            "images", "material_animations", "sounds", "stickers", 
            "texts", "transitions", "videos"
        ]
        
        for material_type in required_material_types:
            if material_type not in materials:
                self.warnings.append(f"materials 缺少材料类型: {material_type}")
            elif not isinstance(materials[material_type], list):
                self.errors.append(f"materials.{material_type} 必须是数组")
        
        # 验证视频材料
        if "videos" in materials:
            for i, video in enumerate(materials["videos"]):
                self._validate_video_material(video, f"materials.videos[{i}]")
        
        # 验证效果材料
        if "effects" in materials:
            for i, effect in enumerate(materials["effects"]):
                self._validate_effect_material(effect, f"materials.effects[{i}]")
        
        # 验证动画材料
        if "material_animations" in materials:
            for i, animation in enumerate(materials["material_animations"]):
                self._validate_animation_material(animation, f"materials.material_animations[{i}]")
    
    def _validate_video_material(self, video: Dict[str, Any], path: str):
        """验证视频材料结构"""
        required_fields = ["id", "path", "duration", "width", "height", "type"]
        
        for field in required_fields:
            if field not in video:
                self.errors.append(f"{path} 缺少字段: {field}")
        
        if "type" in video and video["type"] != "video":
            self.errors.append(f"{path}.type 必须是 'video'")
        
        if "duration" in video and not isinstance(video["duration"], int):
            self.errors.append(f"{path}.duration 必须是整数")
        
        if "width" in video and not isinstance(video["width"], int):
            self.errors.append(f"{path}.width 必须是整数")
        
        if "height" in video and not isinstance(video["height"], int):
            self.errors.append(f"{path}.height 必须是整数")
    
    def _validate_effect_material(self, effect: Dict[str, Any], path: str):
        """验证效果材料结构"""
        required_fields = ["id", "name", "type"]
        
        for field in required_fields:
            if field not in effect:
                self.errors.append(f"{path} 缺少字段: {field}")
        
        if "intensity" in effect and not isinstance(effect["intensity"], (int, float)):
            self.errors.append(f"{path}.intensity 必须是数字")
    
    def _validate_animation_material(self, animation: Dict[str, Any], path: str):
        """验证动画材料结构"""
        required_fields = ["id", "type"]
        
        for field in required_fields:
            if field not in animation:
                self.errors.append(f"{path} 缺少字段: {field}")
        
        if "animations" in animation and not isinstance(animation["animations"], list):
            self.errors.append(f"{path}.animations 必须是数组")
    
    def _validate_tracks_structure(self, tracks: List[Dict[str, Any]]):
        """验证轨道结构"""
        if not isinstance(tracks, list):
            self.errors.append("tracks 必须是数组")
            return
        
        for i, track in enumerate(tracks):
            self._validate_track(track, f"tracks[{i}]")
    
    def _validate_track(self, track: Dict[str, Any], path: str):
        """验证单个轨道结构"""
        required_fields = ["id", "type", "segments"]
        
        for field in required_fields:
            if field not in track:
                self.errors.append(f"{path} 缺少字段: {field}")
        
        if "segments" in track:
            if not isinstance(track["segments"], list):
                self.errors.append(f"{path}.segments 必须是数组")
            else:
                for j, segment in enumerate(track["segments"]):
                    self._validate_segment(segment, f"{path}.segments[{j}]")
    
    def _validate_segment(self, segment: Dict[str, Any], path: str):
        """验证片段结构"""
        required_fields = [
            "id", "material_id", "target_timerange", "source_timerange", 
            "speed", "volume", "visible"
        ]
        
        for field in required_fields:
            if field not in segment:
                self.errors.append(f"{path} 缺少字段: {field}")
        
        # 验证时间范围
        for timerange_field in ["target_timerange", "source_timerange"]:
            if timerange_field in segment:
                timerange = segment[timerange_field]
                # source_timerange可以为null（特别是文本片段）
                if timerange is None and timerange_field == "source_timerange":
                    continue
                if not isinstance(timerange, dict):
                    self.errors.append(f"{path}.{timerange_field} 必须是对象")
                else:
                    for field in ["start", "duration"]:
                        if field not in timerange:
                            self.errors.append(f"{path}.{timerange_field} 缺少字段: {field}")
                        elif not isinstance(timerange[field], int):
                            self.errors.append(f"{path}.{timerange_field}.{field} 必须是整数")
    
    def _validate_meta_structure(self, data: Dict[str, Any]):
        """验证 draft_meta_info.json 的数据结构"""
        required_fields = [
            "draft_id", "draft_name", "tm_draft_create", "tm_draft_modified"
        ]
        
        for field in required_fields:
            if field not in data:
                self.errors.append(f"draft_meta_info.json 缺少必需字段: {field}")
    
    def get_validation_report(self) -> str:
        """获取验证报告"""
        report = []
        
        if self.errors:
            report.append("❌ 验证失败，发现以下错误:")
            for error in self.errors:
                report.append(f"  - {error}")
        
        if self.warnings:
            report.append("⚠️ 发现以下警告:")
            for warning in self.warnings:
                report.append(f"  - {warning}")
        
        if not self.errors and not self.warnings:
            report.append("✅ 验证通过，数据结构完全正确！")
        elif not self.errors:
            report.append("✅ 验证通过，但有一些警告")
        
        return "\n".join(report)


def validate_draft(draft_folder_path: str) -> bool:
    """
    验证草稿文件夹
    
    Args:
        draft_folder_path: 草稿文件夹路径
        
    Returns:
        验证是否通过
    """
    validator = DraftValidator()
    result = validator.validate_draft_folder(draft_folder_path)
    print(validator.get_validation_report())
    return result
