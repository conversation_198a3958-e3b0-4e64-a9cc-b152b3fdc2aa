"""
视频片段处理
"""
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, List

from .timerange import Timerange


class VideoAnimation:
    """视频动画类"""
    
    def __init__(self, animation_type: str, resource_id: str, duration: Optional[int] = None):
        self.animation_type = animation_type
        self.resource_id = resource_id
        self.duration = duration
        self.animation_id = uuid.uuid4().hex
        self.added_at = datetime.now().isoformat()
    
    def to_dict(self):
        return {
            "animation_id": self.animation_id,
            "animation_type": self.animation_type,
            "resource_id": self.resource_id,
            "duration": self.duration,
            "added_at": self.added_at
        }
    
    @classmethod
    def from_dict(cls, data):
        instance = cls(data["animation_type"], data["resource_id"], data.get("duration"))
        instance.animation_id = data.get("animation_id", uuid.uuid4().hex)
        instance.added_at = data.get("added_at", datetime.now().isoformat())
        return instance


class VideoFilter:
    """视频滤镜类"""
    
    def __init__(self, filter_type: str, resource_id: str, intensity: float = 1.0):
        self.filter_type = filter_type
        self.resource_id = resource_id
        self.intensity = intensity
        self.filter_id = uuid.uuid4().hex
        self.added_at = datetime.now().isoformat()
    
    def to_dict(self):
        return {
            "filter_id": self.filter_id,
            "filter_type": self.filter_type,
            "resource_id": self.resource_id,
            "intensity": self.intensity,
            "added_at": self.added_at
        }
    
    @classmethod
    def from_dict(cls, data):
        instance = cls(data["filter_type"], data["resource_id"], data.get("intensity", 1.0))
        instance.filter_id = data.get("filter_id", uuid.uuid4().hex)
        instance.added_at = data.get("added_at", datetime.now().isoformat())
        return instance


class VideoTransition:
    """视频转场类"""
    
    def __init__(self, transition_type: str, resource_id: str, duration: Optional[int] = None):
        self.transition_type = transition_type
        self.resource_id = resource_id
        self.duration = duration
        self.transition_id = uuid.uuid4().hex
        self.added_at = datetime.now().isoformat()
    
    def to_dict(self):
        return {
            "transition_id": self.transition_id,
            "transition_type": self.transition_type,
            "resource_id": self.resource_id,
            "duration": self.duration,
            "added_at": self.added_at
        }
    
    @classmethod
    def from_dict(cls, data):
        instance = cls(data["transition_type"], data["resource_id"], data.get("duration"))
        instance.transition_id = data.get("transition_id", uuid.uuid4().hex)
        instance.added_at = data.get("added_at", datetime.now().isoformat())
        return instance


class VideoSegment:
    """视频片段类"""
    
    def __init__(self, material_path: Optional[str] = None, timerange: Optional[Timerange] = None, 
                 *, segment_id: Optional[str] = None, script: Optional['DraftScript'] = None):
        """
        创建视频片段
        
        Args:
            material_path: 素材路径（新建模式）
            timerange: 时间范围（新建模式）
            segment_id: 片段ID（加载模式）
            script: 脚本对象（加载模式）
        """
        if segment_id and script:
            # ID加载模式：从文件加载
            self._load_from_file(segment_id, script)
        else:
            # 新建模式
            if not material_path or not timerange:
                raise ValueError("新建模式下必须提供material_path和timerange")
            
            self.segment_id = uuid.uuid4().hex
            self.segment_type = "video"
            self.material_path = material_path
            self.timerange = timerange
            self.material_id = uuid.uuid4().hex  # 素材ID
            self.speed = 1.0
            self.volume = 1.0
            
            # 效果列表
            self.animations = []
            self.filters = []
            self.transitions = []
            self.extra_material_refs = []
            
            # 脚本引用
            self._script = None
            
            # 创建时间
            self.created_at = datetime.now().isoformat()
            self.updated_at = datetime.now().isoformat()
    
    def _load_from_file(self, segment_id: str, script: 'DraftScript'):
        """从JSON文件加载片段数据"""
        segment_file = script.draft_path / "segments" / f"video_{segment_id}.json"
        
        if not segment_file.exists():
            raise ValueError(f"片段文件不存在: {segment_file}")
        
        with open(segment_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        # 恢复所有属性
        self.segment_id = data["segment_id"]
        self.segment_type = data["segment_type"]
        self.material_path = data["material_path"]
        self.timerange = Timerange.from_dict(data["timerange"])
        self.material_id = data.get("material_id", uuid.uuid4().hex)
        self.speed = data.get("speed", 1.0)
        self.volume = data.get("volume", 1.0)
        
        # 恢复效果
        self.animations = [VideoAnimation.from_dict(a) for a in data.get("animations", [])]
        self.filters = [VideoFilter.from_dict(f) for f in data.get("filters", [])]
        self.transitions = [VideoTransition.from_dict(t) for t in data.get("transitions", [])]
        self.extra_material_refs = data.get("extra_material_refs", [])
        
        # 时间信息
        self.created_at = data.get("created_at", datetime.now().isoformat())
        self.updated_at = data.get("updated_at", datetime.now().isoformat())
        
        # 脚本引用
        self._script = script
    
    def add_animation(self, animation_type, duration: Optional[int] = None):
        """
        添加动画
        
        Args:
            animation_type: 动画类型对象（应该有name和resource_id属性）
            duration: 动画时长
        
        Returns:
            self（支持链式调用）
        """
        # 处理动画类型
        if hasattr(animation_type, 'value'):
            # 枚举类型
            anim_name = animation_type.value.name
            resource_id = animation_type.value.resource_id
        else:
            # 字符串类型
            anim_name = str(animation_type)
            resource_id = str(animation_type)
        
        animation = VideoAnimation(anim_name, resource_id, duration)
        self.animations.append(animation)
        self.extra_material_refs.append(animation.animation_id)
        
        # 更新时间
        self.updated_at = datetime.now().isoformat()
        
        # 立即保存到文件
        if self._script:
            self._save_to_file()
            self._validate_data()

        print(f"✅ 添加动画: {anim_name}")
        return self
    
    def add_filter(self, filter_type, intensity: float = 1.0):
        """
        添加滤镜
        
        Args:
            filter_type: 滤镜类型对象
            intensity: 滤镜强度
        
        Returns:
            self（支持链式调用）
        """
        # 处理滤镜类型
        if hasattr(filter_type, 'value'):
            # 枚举类型
            filter_name = filter_type.value.name
            resource_id = filter_type.value.resource_id
        else:
            # 字符串类型
            filter_name = str(filter_type)
            resource_id = str(filter_type)
        
        video_filter = VideoFilter(filter_name, resource_id, intensity)
        self.filters.append(video_filter)
        self.extra_material_refs.append(video_filter.filter_id)
        
        # 更新时间
        self.updated_at = datetime.now().isoformat()
        
        # 立即保存到文件
        if self._script:
            self._save_to_file()
            self._validate_data()

        print(f"✅ 添加滤镜: {filter_name} (强度: {intensity})")
        return self
    
    def add_transition(self, transition_type, duration: Optional[int] = None):
        """
        添加转场
        
        Args:
            transition_type: 转场类型对象
            duration: 转场时长
        
        Returns:
            self（支持链式调用）
        """
        # 处理转场类型
        if hasattr(transition_type, 'value'):
            # 枚举类型
            transition_name = transition_type.value.name
            resource_id = transition_type.value.resource_id
        else:
            # 字符串类型
            transition_name = str(transition_type)
            resource_id = str(transition_type)
        
        transition = VideoTransition(transition_name, resource_id, duration)
        self.transitions.append(transition)
        self.extra_material_refs.append(transition.transition_id)
        
        # 更新时间
        self.updated_at = datetime.now().isoformat()
        
        # 立即保存到文件
        if self._script:
            self._save_to_file()
            self._validate_data()

        print(f"✅ 添加转场: {transition_name}")
        return self
    
    def _save_to_file(self):
        """保存到JSON文件"""
        if not self._script:
            return
        
        segment_file = self._script.draft_path / "segments" / f"video_{self.segment_id}.json"
        
        with open(segment_file, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)

    def _validate_data(self):
        """验证片段数据的完整性"""
        if not self._script:
            return

        # 验证基本字段
        if not self.segment_id:
            print("⚠️ 警告: 片段ID为空")

        if not self.material_id:
            print("⚠️ 警告: 素材ID为空")

        if not self.timerange:
            print("⚠️ 警告: 时间范围为空")

        # 验证时间范围合理性
        if self.timerange and self.timerange.duration <= 0:
            print("⚠️ 警告: 时间范围持续时间无效")

        # 验证效果数据
        for animation in self.animations:
            if not animation.animation_id:
                print("⚠️ 警告: 动画ID为空")
            if not animation.resource_id:
                print("⚠️ 警告: 动画资源ID为空")

        for filter_item in self.filters:
            if not filter_item.filter_id:
                print("⚠️ 警告: 滤镜ID为空")
            if not filter_item.resource_id:
                print("⚠️ 警告: 滤镜资源ID为空")

        for transition in self.transitions:
            if not transition.transition_id:
                print("⚠️ 警告: 转场ID为空")
            if not transition.resource_id:
                print("⚠️ 警告: 转场资源ID为空")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "segment_id": self.segment_id,
            "segment_type": self.segment_type,
            "material_path": self.material_path,
            "material_id": self.material_id,
            "timerange": self.timerange.to_dict(),
            "speed": self.speed,
            "volume": self.volume,
            "animations": [a.to_dict() for a in self.animations],
            "filters": [f.to_dict() for f in self.filters],
            "transitions": [t.to_dict() for t in self.transitions],
            "extra_material_refs": self.extra_material_refs,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], script: 'DraftScript') -> 'VideoSegment':
        """从字典创建实例"""
        instance = cls.__new__(cls)
        
        instance.segment_id = data["segment_id"]
        instance.segment_type = data["segment_type"]
        instance.material_path = data["material_path"]
        instance.material_id = data.get("material_id", uuid.uuid4().hex)
        instance.timerange = Timerange.from_dict(data["timerange"])
        instance.speed = data.get("speed", 1.0)
        instance.volume = data.get("volume", 1.0)
        
        # 恢复效果
        instance.animations = [VideoAnimation.from_dict(a) for a in data.get("animations", [])]
        instance.filters = [VideoFilter.from_dict(f) for f in data.get("filters", [])]
        instance.transitions = [VideoTransition.from_dict(t) for t in data.get("transitions", [])]
        instance.extra_material_refs = data.get("extra_material_refs", [])
        
        # 时间信息
        instance.created_at = data.get("created_at", datetime.now().isoformat())
        instance.updated_at = data.get("updated_at", datetime.now().isoformat())
        
        # 脚本引用
        instance._script = script
        
        return instance
