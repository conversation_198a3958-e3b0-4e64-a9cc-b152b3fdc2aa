"""
my_jianying 使用示例
演示如何使用 my_jianying 创建剪映项目
"""
import my_jianying as mj
import os

# 模拟效果类型（实际使用时需要根据剪映资源定义）
class MockAnimationType:
    def __init__(self, name, resource_id):
        self.value = MockValue(name, resource_id)

class MockValue:
    def __init__(self, name, resource_id):
        self.name = name
        self.resource_id = resource_id

# 定义效果类型
class MyIntroType:
    斜切 = MockAnimationType("斜切", "7210657307938525751")
    缩小 = MockAnimationType("缩小", "7210657307938525751")

class MyFilterType:
    亮肤 = MockAnimationType("亮肤", "7127655008715230495")
    冷白 = MockAnimationType("冷白", "7127655008715230495")

class MyTransitionType:
    信号故障 = MockAnimationType("信号故障", "7288149307197231676")
    闪白 = MockAnimationType("闪白", "7288149307197231676")

def demo_single_video():
    """示例1：单个视频片段"""
    print("=== 示例1：单个视频片段 ===")
    
    # 视频文件路径（请替换为您的实际视频文件）
    video_path = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4"
    
    # 创建输出目录
    os.makedirs("output/my_jianying", exist_ok=True)
    
    try:
        # 1. 创建草稿文件夹
        draft_folder = mj.DraftFolder("output/my_jianying")
        
        # 2. 创建新的草稿项目
        script = draft_folder.create_draft("单个视频示例", 1920, 1080, allow_replace=True)
        
        # 3. 添加视频轨道
        script.add_track("video")
        
        # 4. 创建视频片段（使用前3秒）
        video = mj.VideoSegment(video_path, mj.trange("0s", "3s"))
        
        # 5. 添加效果
        video.add_animation(MyIntroType.斜切)
        video.add_filter(MyFilterType.亮肤, 0.8)
        video.add_transition(MyTransitionType.信号故障)
        
        # 6. 添加片段到项目
        script.add_segment(video)
        
        # 7. 保存项目
        project_path = script.save()
        print(f"✅ 项目已保存到: {project_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例1失败: {e}")
        return False

def demo_multiple_videos():
    """示例2：多个视频片段"""
    print("\n=== 示例2：多个视频片段 ===")
    
    video_path = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4"
    
    try:
        # 创建项目
        draft_folder = mj.DraftFolder("output/my_jianying")
        script = draft_folder.create_draft("多片段示例", 1920, 1080, allow_replace=True)
        script.add_track("video")
        
        # 创建第一个片段
        video1 = mj.VideoSegment(video_path, mj.trange("0s", "2.5s"))
        video1.add_animation(MyIntroType.斜切)
        video1.add_filter(MyFilterType.亮肤, 0.8)
        video1.add_transition(MyTransitionType.信号故障)
        
        # 创建第二个片段
        video2 = mj.VideoSegment(video_path, mj.trange("2.5s", "2.5s"))
        video2.add_filter(MyFilterType.冷白, 0.6)
        video2.add_animation(MyIntroType.缩小)
        
        # 添加到项目
        script.add_segment(video1).add_segment(video2)
        
        # 保存项目
        project_path = script.save()
        print(f"✅ 项目已保存到: {project_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例2失败: {e}")
        return False

def demo_save_load_cycle():
    """示例3：保存-加载-修改循环"""
    print("\n=== 示例3：保存-加载-修改循环 ===")
    
    video_path = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4"
    
    try:
        # 创建初始项目
        draft_folder = mj.DraftFolder("output/my_jianying")
        script = draft_folder.create_draft("循环示例", 1920, 1080, allow_replace=True)
        script.add_track("video")
        
        # 创建视频片段
        video = mj.VideoSegment(video_path, mj.trange("0s", "3s"))
        video_id = video.segment_id  # 保存片段ID
        script.add_segment(video)
        
        # 第一步：添加动画并保存
        print("  第一步：添加动画")
        video.add_animation(MyIntroType.斜切)
        script.save()
        
        # 第二步：重新加载并添加滤镜
        print("  第二步：重新加载并添加滤镜")
        script = draft_folder.load_template("循环示例")
        video = mj.VideoSegment(segment_id=video_id, script=script)
        video.add_filter(MyFilterType.亮肤, 0.8)
        script.save()
        
        # 第三步：再次加载并添加转场
        print("  第三步：再次加载并添加转场")
        script = draft_folder.load_template("循环示例")
        video = mj.VideoSegment(segment_id=video_id, script=script)
        video.add_transition(MyTransitionType.信号故障)
        video= mj.VideoSegment(video_path, mj.trange("3s", "3s"))
        script.add_segment(video)
        project_path = script.save()
        
        print(f"✅ 循环修改完成，项目保存到: {project_path}")
        
        # 验证最终状态
        print("  验证最终状态:")
        print(f"    动画数量: {len(video.animations)}")
        print(f"    滤镜数量: {len(video.filters)}")
        print(f"    转场数量: {len(video.transitions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例3失败: {e}")
        return False

def demo_text_features():
    """示例4：文本功能演示"""
    print("\n=== 示例4：文本功能演示 ===")

    try:
        # 创建项目
        draft_folder = mj.DraftFolder("./output")
        script = draft_folder.create_draft("文本功能示例", 1920, 1080, allow_replace=True)
        script.add_track("text")

        # 创建基础文本
        basic_text = mj.TextSegment(
            text="基础文本",
            timerange=mj.trange("0s", "2s")
        )
        script.add_segment(basic_text)

        # 创建带样式的文本
        style = mj.TextStyle(
            size=15.0,
            bold=True,
            color=(1.0, 0.0, 0.0),  # 红色
            align=1  # 居中
        )

        styled_text = mj.TextSegment(
            text="带样式文本",
            timerange=mj.trange("2s", "2s"),
            style=style
        )
        script.add_segment(styled_text)

        # 创建带描边的文本
        border = mj.TextBorder(
            alpha=1.0,
            color=(0.0, 0.0, 0.0),  # 黑色描边
            width=50.0
        )

        border_text = mj.TextSegment(
            text="带描边文本",
            timerange=mj.trange("4s", "2s"),
            border=border
        )
        script.add_segment(border_text)

        # 创建带背景的文本
        background = mj.TextBackground(
            color="#FFFF00",  # 黄色背景
            alpha=0.8,
            style=1
        )

        bg_text = mj.TextSegment(
            text="带背景文本",
            timerange=mj.trange("6s", "2s"),
            background=background
        )
        script.add_segment(bg_text)

        # 保存项目
        project_path = script.save()
        print(f"✅ 文本功能示例已保存到: {project_path}")

        # 显示项目统计
        print("  项目统计:")
        print(f"    文本片段数量: 4")
        print(f"    基础文本: 1个")
        print(f"    带样式文本: 1个")
        print(f"    带描边文本: 1个")
        print(f"    带背景文本: 1个")

        return True

    except Exception as e:
        print(f"❌ 示例4失败: {e}")
        return False

def demo_complex_project():
    """示例5：复杂项目"""
    print("\n=== 示例5：复杂项目 ===")

    video_path = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4"

    try:
        # 创建项目
        draft_folder = mj.DraftFolder("./demo_output")
        script = draft_folder.create_draft("复杂项目示例", 1920, 1080, allow_replace=True)
        script.add_track("video")

        # 创建多个片段，每个都有不同的效果组合
        segments = []

        for i in range(3):
            start_time = i * 1.5
            video = mj.VideoSegment(video_path, mj.trange(f"{start_time}s", "1.5s"))

            # 根据索引添加不同效果
            if i == 0:
                video.add_animation(MyIntroType.斜切)
                video.add_filter(MyFilterType.亮肤, 0.8)
            elif i == 1:
                video.add_filter(MyFilterType.冷白, 0.6)
                video.add_transition(MyTransitionType.信号故障)
            else:
                video.add_animation(MyIntroType.缩小)
                video.add_transition(MyTransitionType.闪白)

            script.add_segment(video)
            segments.append(video)

        # 保存项目
        project_path = script.save()
        print(f"✅ 复杂项目已保存到: {project_path}")

        # 显示项目统计
        print("  项目统计:")
        print(f"    片段数量: {len(segments)}")
        total_animations = sum(len(seg.animations) for seg in segments)
        total_filters = sum(len(seg.filters) for seg in segments)
        total_transitions = sum(len(seg.transitions) for seg in segments)
        print(f"    总动画数: {total_animations}")
        print(f"    总滤镜数: {total_filters}")
        print(f"    总转场数: {total_transitions}")

        return True

    except Exception as e:
        print(f"❌ 示例5失败: {e}")
        return False

def main():
    """运行所有示例"""
    print("🎬 my_jianying 使用示例")
    print("=" * 50)
    
    # 运行所有示例
    results = []
    # results.append(demo_single_video())
    # results.append(demo_multiple_videos())
    # results.append(demo_save_load_cycle())
    results.append(demo_text_features())
    # results.append(demo_complex_project())

    # 总结
    print("\n" + "=" * 50)
    print("📊 示例运行总结:")
    success_count = sum(results)
    total_count = len(results)
    
    print(f"  成功: {success_count}/{total_count}")
    print(f"  成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有示例都运行成功！")
        print("📁 生成的项目文件保存在 ./demo_output/ 目录下")
        print("💡 您可以在剪映中打开这些项目文件进行查看和编辑")
    else:
        print("⚠️ 部分示例运行失败，请检查错误信息")
    
    print("\n📖 更多使用方法请参考 my_jianying使用说明.md")

if __name__ == "__main__":
    main()
