# my_jianying 使用说明

## 📖 简介

my_jianying 是一个用于生成剪映草稿文件的 Python 库，可以通过代码创建包含视频片段、动画、滤镜、转场等效果的剪映项目。

## 🚀 快速开始

### 基本使用流程

```python
import my_jianying as mj

# 1. 创建草稿文件夹
draft_folder = mj.DraftFolder("./my_projects")

# 2. 创建新的草稿项目
script = draft_folder.create_draft("我的视频项目", 1920, 1080, allow_replace=True)

# 3. 添加视频轨道
script.add_track("video")

# 4. 创建视频片段
video = mj.VideoSegment("path/to/video.mp4", mj.trange("0s", "5s"))

# 5. 添加片段到项目
script.add_segment(video)

# 6. 保存项目
project_path = script.save()
print(f"项目已保存到: {project_path}")
```

## 📁 核心组件

### 1. DraftFolder - 草稿文件夹管理

```python
# 创建草稿文件夹
draft_folder = mj.DraftFolder("./output/my_projects")

# 创建新草稿
script = draft_folder.create_draft(
    name="项目名称",           # 项目名称
    width=1920,              # 视频宽度
    height=1080,             # 视频高度
    allow_replace=True       # 是否允许覆盖已存在的项目
)

# 加载已存在的草稿
script = draft_folder.load_template("项目名称")
```

### 2. DraftScript - 草稿脚本

```python
# 添加轨道
script.add_track("video")    # 视频轨道
script.add_track("audio")    # 音频轨道
script.add_track("text")     # 文字轨道

# 添加片段
script.add_segment(video_segment)

# 保存项目
project_path = script.save()
```

### 3. VideoSegment - 视频片段

```python
# 创建视频片段
video = mj.VideoSegment(
    video_path="path/to/video.mp4",     # 视频文件路径
    timerange=mj.trange("0s", "5s")     # 时间范围：从0秒开始，持续5秒
)

# 或者通过segment_id加载已存在的片段
video = mj.VideoSegment(segment_id="existing_id", script=script)
```

### 4. TextSegment - 文本片段

```python
# 创建基础文本片段
text = mj.TextSegment(
    text="Hello World",                 # 文本内容
    timerange=mj.trange("0s", "3s")     # 时间范围：从0秒开始，持续3秒
)

# 创建带样式的文本片段
style = mj.TextStyle(
    size=12.0,                          # 字体大小
    bold=True,                          # 粗体
    color=(1.0, 0.0, 0.0),             # 红色
    align=1                             # 居中对齐
)

text = mj.TextSegment(
    text="Styled Text",
    timerange=mj.trange("0s", "3s"),
    style=style
)

# 或者通过segment_id加载已存在的片段
text = mj.TextSegment(segment_id="existing_id", script=script)
```

### 5. TextStyle - 文本样式

```python
# 创建文本样式
style = mj.TextStyle(
    size=12.0,                          # 字体大小
    bold=True,                          # 是否粗体
    italic=False,                       # 是否斜体
    underline=False,                    # 是否下划线
    color=(1.0, 0.0, 0.0),             # 字体颜色 RGB (红色)
    alpha=1.0,                          # 透明度 (0.0-1.0)
    align=1,                            # 对齐方式: 0=左对齐, 1=居中, 2=右对齐
    vertical=False,                     # 是否竖排文本
    letter_spacing=0,                   # 字符间距
    line_spacing=0,                     # 行间距
    auto_wrapping=False,                # 是否自动换行
    max_line_width=0.82                 # 最大行宽比例
)
```

### 6. TextBorder - 文本描边

```python
# 创建文本描边
border = mj.TextBorder(
    alpha=1.0,                          # 描边透明度
    color=(0.0, 0.0, 0.0),             # 描边颜色 RGB (黑色)
    width=50.0                          # 描边宽度 (0-100)
)
```

### 7. TextBackground - 文本背景

```python
# 创建文本背景
background = mj.TextBackground(
    color="#FF0000",                    # 背景颜色 (十六进制)
    style=1,                            # 背景样式 (1或2)
    alpha=0.8,                          # 背景透明度
    round_radius=0.2,                   # 圆角半径
    height=0.14,                        # 背景高度比例
    width=0.14,                         # 背景宽度比例
    horizontal_offset=0.5,              # 水平偏移
    vertical_offset=0.5                 # 垂直偏移
)
```

### 8. trange - 时间范围

```python
# 不同的时间格式
mj.trange("0s", "5s")        # 从0秒开始，持续5秒
mj.trange("1.5s", "3s")      # 从1.5秒开始，持续3秒
mj.trange("0s", "0.5s")      # 从0秒开始，持续0.5秒
```

## 🎨 添加效果

### 动画效果

```python
# 模拟动画类型（需要根据实际情况定义）
class MyIntroType:
    斜切 = MockAnimationType("斜切", "7210657307938525751")
    缩小 = MockAnimationType("缩小", "7210657307938525751")

# 添加动画
video.add_animation(MyIntroType.斜切)
video.add_animation(MyIntroType.缩小)
```

### 滤镜效果

```python
# 模拟滤镜类型
class MyFilterType:
    亮肤 = MockAnimationType("亮肤", "7127655008715230495")
    冷白 = MockAnimationType("冷白", "7127655008715230495")

# 添加滤镜（带强度）
video.add_filter(MyFilterType.亮肤, 0.8)    # 强度0.8
video.add_filter(MyFilterType.冷白, 0.6)    # 强度0.6
```

### 转场效果

```python
# 模拟转场类型
class MyTransitionType:
    信号故障 = MockAnimationType("信号故障", "7288149307197231676")
    闪白 = MockAnimationType("闪白", "7288149307197231676")

# 添加转场
video.add_transition(MyTransitionType.信号故障)
video.add_transition(MyTransitionType.闪白)
```

## 📝 文本效果

### 文本动画

```python
# 模拟文本动画类型
class MyTextIntro:
    弹入 = MockAnimationType("弹入", "7210657307938525751")
    打字机_I = MockAnimationType("打字机_I", "7210657307938525751")

# 添加文本动画
text.add_animation(MyTextIntro.弹入)
text.add_animation(MyTextIntro.打字机_I, duration="1s")  # 指定动画时长
```

### 文本气泡和花字

```python
# 添加气泡效果
text.add_bubble(effect_id="bubble_effect_id", resource_id="bubble_resource_id")

# 添加花字效果
text.add_effect(effect_id="text_effect_id")
```

## 📝 完整示例

### 示例1：单个视频片段

```python
import my_jianying as mj

# 创建项目
draft_folder = mj.DraftFolder("./my_projects")
script = draft_folder.create_draft("单个视频示例", 1920, 1080, allow_replace=True)
script.add_track("video")

# 创建视频片段
video = mj.VideoSegment("video.mp4", mj.trange("0s", "3s"))

# 添加效果
video.add_animation(MyIntroType.斜切)
video.add_filter(MyFilterType.亮肤, 0.8)
video.add_transition(MyTransitionType.信号故障)

# 添加到项目并保存
script.add_segment(video)
project_path = script.save()
print(f"✅ 项目已保存: {project_path}")
```

### 示例2：多个视频片段

```python
import my_jianying as mj

# 创建项目
draft_folder = mj.DraftFolder("./my_projects")
script = draft_folder.create_draft("多片段示例", 1920, 1080, allow_replace=True)
script.add_track("video")

# 创建第一个片段
video1 = mj.VideoSegment("video.mp4", mj.trange("0s", "2.5s"))
video1.add_animation(MyIntroType.斜切)
video1.add_filter(MyFilterType.亮肤, 0.8)
video1.add_transition(MyTransitionType.信号故障)

# 创建第二个片段
video2 = mj.VideoSegment("video.mp4", mj.trange("2.5s", "2.5s"))
video2.add_filter(MyFilterType.冷白, 0.6)
video2.add_animation(MyIntroType.缩小)

# 添加到项目
script.add_segment(video1).add_segment(video2)

# 保存项目
project_path = script.save()
print(f"✅ 项目已保存: {project_path}")
```

### 示例3：文本功能完整示例

```python
import my_jianying as mj

# 创建文本项目
draft_folder = mj.DraftFolder("./my_projects")
script = draft_folder.create_draft("文本示例", 1920, 1080, allow_replace=True)
script.add_track("text")

# 创建复杂样式
style = mj.TextStyle(
    size=15.0,
    bold=True,
    italic=True,
    color=(0.0, 1.0, 0.0),  # 绿色
    alpha=0.9,
    align=1,  # 居中
    letter_spacing=5,
    line_spacing=10
)

# 创建描边
border = mj.TextBorder(
    alpha=1.0,
    color=(0.0, 0.0, 1.0),  # 蓝色描边
    width=30.0
)

# 创建背景
background = mj.TextBackground(
    color="#FFFF00",  # 黄色背景
    alpha=0.7,
    style=2,
    round_radius=0.2
)

# 创建文本片段
text = mj.TextSegment(
    text="复杂文本效果",
    timerange=mj.trange("0s", "4s"),
    style=style,
    border=border,
    background=background
)

# 添加动画
text.add_animation(MyTextIntro.打字机_I)

# 添加到项目并保存
script.add_segment(text)
project_path = script.save()
print(f"✅ 文本项目已保存: {project_path}")
```

### 示例4：保存-加载-修改循环

```python
import my_jianying as mj

# 创建初始项目
draft_folder = mj.DraftFolder("./my_projects")
script = draft_folder.create_draft("循环示例", 1920, 1080, allow_replace=True)
script.add_track("video")

# 创建视频片段
video = mj.VideoSegment("video.mp4", mj.trange("0s", "3s"))
video_id = video.segment_id  # 保存片段ID
script.add_segment(video)

# 第一次保存
video.add_animation(MyIntroType.斜切)
script.save()

# 重新加载并继续修改
script = draft_folder.load_template("循环示例")
video = mj.VideoSegment(segment_id=video_id, script=script)
video.add_filter(MyFilterType.亮肤, 0.8)
script.save()

# 再次加载并添加转场
script = draft_folder.load_template("循环示例")
video = mj.VideoSegment(segment_id=video_id, script=script)
video.add_transition(MyTransitionType.信号故障)
script.save()

print("✅ 循环修改完成")
```

## 📂 输出结构

生成的项目文件结构：
```
my_projects/
└── 项目名称/
    ├── draft_content.json      # 主要内容数据
    └── draft_meta_info.json    # 元数据信息
```

## ⚠️ 注意事项

### 1. 视频文件路径
- 确保视频文件路径正确且文件存在
- 支持相对路径和绝对路径
- 建议使用绝对路径以避免路径问题

### 2. 时间范围
- 时间格式支持：`"0s"`, `"1.5s"`, `"10s"` 等
- 确保时间范围不超过视频文件的实际长度
- 起始时间和持续时间都必须为正数

### 3. 效果类型
- 动画、滤镜、转场的类型需要根据实际的剪映资源定义
- resource_id 需要与剪映中的实际资源ID对应
- 效果强度范围通常为 0.0 到 1.0

### 4. ID管理
- 系统自动生成唯一的segment_id和material_id
- 不要手动设置ID，避免冲突
- 使用segment_id可以在保存-加载循环中引用特定片段

## 🔧 高级功能

### 材料共享
- 相同路径的视频文件自动共享材料，节省空间
- 相同的效果（动画、滤镜、转场）自动去重

### 数据验证
- 自动验证生成的JSON结构
- 确保数据格式符合剪映要求
- 验证ID引用的完整性

### 错误处理
- 自动处理视频文件信息获取失败的情况
- 提供默认值确保项目可以正常创建
- 详细的错误信息和警告提示

## 🎯 最佳实践

1. **项目组织**：为不同类型的项目创建不同的文件夹
2. **命名规范**：使用有意义的项目名称
3. **效果适度**：不要在单个片段上添加过多效果
4. **测试验证**：创建项目后在剪映中测试打开
5. **备份重要项目**：定期备份重要的项目文件

## 📞 支持

如果遇到问题或需要帮助：
1. 检查视频文件路径和格式
2. 确认时间范围设置正确
3. 验证效果类型和resource_id
4. 查看控制台输出的错误信息

## 📚 API 参考

### DraftFolder 类

```python
class DraftFolder:
    def __init__(self, folder_path: str)
    def create_draft(self, name: str, width: int, height: int, allow_replace: bool = False) -> DraftScript
    def load_template(self, name: str) -> DraftScript
```

### DraftScript 类

```python
class DraftScript:
    def add_track(self, track_type: str) -> DraftScript
    def add_segment(self, segment) -> DraftScript
    def save() -> str
```

### VideoSegment 类

```python
class VideoSegment:
    def __init__(self, video_path: str = None, timerange = None, segment_id: str = None, script = None)
    def add_animation(self, animation_type) -> VideoSegment
    def add_filter(self, filter_type, intensity: float = 1.0) -> VideoSegment
    def add_transition(self, transition_type) -> VideoSegment

    # 属性
    @property
    def segment_id(self) -> str
    @property
    def animations(self) -> List
    @property
    def filters(self) -> List
    @property
    def transitions(self) -> List
```

### TextSegment 类

```python
class TextSegment:
    def __init__(self, text: str, timerange, *, font=None, style=None, border=None, background=None, segment_id: str = None, script = None)
    def add_animation(self, animation_type, duration=None) -> TextSegment
    def add_bubble(self, effect_id: str, resource_id: str) -> TextSegment
    def add_effect(self, effect_id: str) -> TextSegment

    # 属性
    @property
    def segment_id(self) -> str
    @property
    def animations(self) -> List
    @property
    def bubbles(self) -> List
    @property
    def effects(self) -> List
```

### TextStyle 类

```python
class TextStyle:
    def __init__(self, *, size: float = 8.0, bold: bool = False, italic: bool = False, underline: bool = False,
                 color: Tuple[float, float, float] = (1.0, 1.0, 1.0), alpha: float = 1.0,
                 align: Literal[0, 1, 2] = 0, vertical: bool = False,
                 letter_spacing: int = 0, line_spacing: int = 0,
                 auto_wrapping: bool = False, max_line_width: float = 0.82)
```

### TextBorder 类

```python
class TextBorder:
    def __init__(self, *, alpha: float = 1.0, color: Tuple[float, float, float] = (0.0, 0.0, 0.0), width: float = 40.0)
```

### TextBackground 类

```python
class TextBackground:
    def __init__(self, *, color: str, style: Literal[1, 2] = 1, alpha: float = 1.0, round_radius: float = 0.0,
                 height: float = 0.14, width: float = 0.14,
                 horizontal_offset: float = 0.5, vertical_offset: float = 0.5)
```

### 工具函数

```python
def trange(start: str, duration: str) -> dict
    """创建时间范围对象

    Args:
        start: 起始时间，如 "0s", "1.5s"
        duration: 持续时间，如 "5s", "2.5s"

    Returns:
        时间范围字典
    """
```

## 🔍 故障排除

### 常见问题

**Q: 生成的项目在剪映中无法打开？**
A: 检查视频文件路径是否正确，确保文件存在且格式支持。

**Q: 时间范围设置无效？**
A: 确保时间格式正确（如"5s"），且不超过视频实际长度。

**Q: 效果没有生效？**
A: 检查效果类型的resource_id是否正确，确保与剪映资源库匹配。

**Q: 保存后重新加载数据丢失？**
A: 确保使用正确的segment_id，检查项目是否正确保存。

### 调试技巧

1. **启用详细日志**：查看控制台输出的详细信息
2. **检查JSON文件**：直接查看生成的draft_content.json文件
3. **分步测试**：先创建简单项目，逐步添加复杂功能
4. **对比标准**：与pyJianYingDraft生成的文件对比

---

**my_jianying 让您可以通过代码轻松创建专业的剪映项目！** 🎬
