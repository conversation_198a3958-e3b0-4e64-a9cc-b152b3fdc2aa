{"canvas_config": {"width": 1920, "height": 1080, "ratio": "original"}, "color_space": 0, "config": {"adjust_max_index": 1, "attachment_info": [], "combination_max_index": 1, "export_range": null, "extract_audio_last_index": 1, "lyrics_recognition_id": "", "lyrics_sync": true, "lyrics_taskinfo": [], "maintrack_adsorb": true, "material_save_mode": 0, "multi_language_current": "none", "multi_language_list": [], "multi_language_main": "none", "multi_language_mode": "none", "original_sound_last_index": 1, "record_audio_last_index": 1, "sticker_max_index": 1, "subtitle_keywords_config": null, "subtitle_recognition_id": "", "subtitle_sync": true, "subtitle_taskinfo": [], "system_font_list": [], "video_mute": false, "zoom_info_params": null}, "cover": null, "create_time": 0, "duration": 10000000, "extra_info": null, "fps": 30, "free_render_index_mode_on": false, "group_container": null, "id": "9d9a163d9d4c40dbb1ce989bc6d211fd", "keyframe_graph_list": [], "keyframes": {"adjusts": [], "audios": [], "effects": [], "filters": [], "handwrites": [], "stickers": [], "texts": [], "videos": []}, "last_modified_platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "materials": {"ai_translates": [], "audio_balances": [], "audio_effects": [], "audio_fades": [], "audio_track_indexes": [], "audios": [{"app_id": 0, "category_id": "", "category_name": "local", "check_flag": 3, "copyright_limit_type": "none", "duration": 5185000, "effect_id": "", "formula_id": "", "id": "ffdb7367953f379daddd00230650c504", "local_material_id": "ffdb7367953f379daddd00230650c504", "music_id": "ffdb7367953f379daddd00230650c504", "name": "audio.mp3", "path": "D:\\pythonProject\\MyProject\\pyJianYingDraft\\readme_assets\\tutorial\\audio.mp3", "source_platform": 0, "type": "extract_music", "wave_points": []}], "beats": [], "canvases": [], "chromas": [], "color_curves": [], "digital_humans": [], "drafts": [], "effects": [], "flowers": [], "green_screens": [], "handwrites": [], "hsl": [], "images": [], "log_color_wheels": [], "loudnesses": [], "manual_deformations": [], "masks": [], "material_animations": [], "material_colors": [], "multi_language_refs": [], "placeholders": [], "plugin_effects": [], "primary_color_wheels": [], "realtime_denoises": [], "shapes": [], "smart_crops": [], "smart_relights": [], "sound_channel_mappings": [], "sounds": [], "speeds": [{"curve_speed": null, "id": "speed_df8786908c7a428a8eaf910992ee36a2", "mode": 0, "speed": 1.0, "type": "speed"}, {"curve_speed": null, "id": "speed_7c45de9f3f944e63bb3cf54c027c3543", "mode": 0, "speed": 1.0, "type": "speed"}, {"curve_speed": null, "id": "speed_896bcc19b76c4866ab33ea9e7ec4616f", "mode": 0, "speed": 1.5, "type": "speed"}, {"curve_speed": null, "id": "speed_b75a1e4e13b64223be8d64895e9e1b44", "mode": 0, "speed": 1.0, "type": "speed"}, {"curve_speed": null, "id": "speed_51a9894c160b49ff946f99449fda375f", "mode": 0, "speed": 1.0, "type": "speed"}], "stickers": [], "tail_leaders": [], "text_templates": [], "texts": [], "time_marks": [], "transitions": [], "video_effects": [], "video_trackings": [], "videos": [], "vocal_beautifys": [], "vocal_separations": []}, "mutable_config": null, "name": "", "new_version": "110.0.0", "relationships": [], "render_index_track_mode_on": false, "retouch_cover": null, "source": "default", "static_cover_image_path": "", "time_marks": null, "tracks": [{"attribute": 0, "flag": 0, "id": "0f91ad63b3e9401ea941b17a88b05229", "is_default_name": false, "name": "audio", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "df8786908c7a428a8eaf910992ee36a2", "material_id": "ffdb7367953f379daddd00230650c504", "target_timerange": {"start": 0, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 2000000}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["ffdb7367953f379daddd00230650c504", "speed_df8786908c7a428a8eaf910992ee36a2"], "is_tone_modify": false, "clip": null, "hdr_settings": null, "render_index": 0}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 0.6, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "7c45de9f3f944e63bb3cf54c027c3543", "material_id": "ffdb7367953f379daddd00230650c504", "target_timerange": {"start": 2000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 2000000}, "speed": 1.0, "volume": 0.6, "extra_material_refs": ["ffdb7367953f379daddd00230650c504", "speed_7c45de9f3f944e63bb3cf54c027c3543"], "is_tone_modify": false, "clip": null, "hdr_settings": null, "render_index": 0}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "896bcc19b76c4866ab33ea9e7ec4616f", "material_id": "ffdb7367953f379daddd00230650c504", "target_timerange": {"start": 4000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 3000000}, "speed": 1.5, "volume": 1.0, "extra_material_refs": ["ffdb7367953f379daddd00230650c504", "speed_896bcc19b76c4866ab33ea9e7ec4616f"], "is_tone_modify": false, "clip": null, "hdr_settings": null, "render_index": 0}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "b75a1e4e13b64223be8d64895e9e1b44", "material_id": "ffdb7367953f379daddd00230650c504", "target_timerange": {"start": 6000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 1000000, "duration": 2000000}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["ffdb7367953f379daddd00230650c504", "speed_b75a1e4e13b64223be8d64895e9e1b44"], "is_tone_modify": false, "clip": null, "hdr_settings": null, "render_index": 0}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 0.8, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "51a9894c160b49ff946f99449fda375f", "material_id": "ffdb7367953f379daddd00230650c504", "target_timerange": {"start": 8000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 2000000}, "speed": 1.0, "volume": 0.8, "extra_material_refs": ["ffdb7367953f379daddd00230650c504", "speed_51a9894c160b49ff946f99449fda375f", "67a1b27457f84e6985d16000062f91f6"], "is_tone_modify": false, "clip": null, "hdr_settings": null, "render_index": 0}], "type": "audio"}], "update_time": 0, "version": 360000}