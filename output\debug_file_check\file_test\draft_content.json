{"canvas_config": {"width": 1920, "height": 1080, "ratio": "original"}, "color_space": 0, "config": {"adjust_max_index": 1, "attachment_info": [], "combination_max_index": 1, "export_range": null, "extract_audio_last_index": 1, "lyrics_recognition_id": "", "lyrics_sync": true, "lyrics_taskinfo": [], "maintrack_adsorb": true, "material_save_mode": 0, "multi_language_current": "none", "multi_language_list": [], "multi_language_main": "none", "multi_language_mode": "none", "original_sound_last_index": 1, "record_audio_last_index": 1, "sticker_max_index": 1, "subtitle_keywords_config": null, "subtitle_recognition_id": "", "subtitle_sync": true, "subtitle_taskinfo": [], "system_font_list": [], "video_mute": false, "zoom_info_params": null}, "cover": null, "create_time": 0, "duration": 3000000, "extra_info": null, "fps": 30, "free_render_index_mode_on": false, "group_container": null, "id": "60bc751af3714e9fb74927f877ecd4c8", "keyframe_graph_list": [], "keyframes": {"adjusts": [], "audios": [], "effects": [], "filters": [], "handwrites": [], "stickers": [], "texts": [], "videos": []}, "last_modified_platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "materials": {"ai_translates": [], "audio_balances": [], "audio_effects": [], "audio_fades": [], "audio_track_indexes": [], "audios": [], "beats": [], "canvases": [], "chromas": [], "color_curves": [], "digital_humans": [], "drafts": [], "effects": [], "flowers": [], "green_screens": [], "handwrites": [], "hsl": [], "images": [], "log_color_wheels": [], "loudnesses": [], "manual_deformations": [], "masks": [], "material_animations": [], "material_colors": [], "multi_language_refs": [], "placeholders": [], "plugin_effects": [], "primary_color_wheels": [], "realtime_denoises": [], "shapes": [], "smart_crops": [], "smart_relights": [], "sound_channel_mappings": [], "sounds": [], "speeds": [{"curve_speed": null, "id": "speed_27f6782606d8456f8022f11d87cb5297", "mode": 0, "speed": 1.0, "type": "speed"}], "stickers": [], "tail_leaders": [], "text_templates": [], "texts": [], "time_marks": [], "transitions": [], "video_effects": [], "video_trackings": [], "videos": [{"audio_fade": null, "category_id": "", "category_name": "local", "check_flag": 63487, "crop": {"upper_left_x": 0.0, "upper_left_y": 0.0, "upper_right_x": 1.0, "upper_right_y": 0.0, "lower_left_x": 0.0, "lower_left_y": 1.0, "lower_right_x": 1.0, "lower_right_y": 1.0}, "crop_ratio": "free", "crop_scale": 1.0, "duration": 5000000, "height": 1080, "id": "5b7bd02be59042549cbf2341dcc5c2eb", "local_material_id": "", "material_id": "5b7bd02be59042549cbf2341dcc5c2eb", "material_name": "video.mp4", "media_path": "", "path": "./readme_assets/tutorial/video.mp4", "type": "video", "width": 1920}], "vocal_beautifys": [], "vocal_separations": []}, "mutable_config": null, "name": "", "new_version": "110.0.0", "relationships": [], "render_index_track_mode_on": false, "retouch_cover": null, "source": "default", "static_cover_image_path": "", "time_marks": null, "tracks": [{"attribute": 0, "flag": 0, "id": "fff31ac92eef46b796b6c5f7ad01012d", "is_default_name": false, "name": "video", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "27f6782606d8456f8022f11d87cb5297", "material_id": "5b7bd02be59042549cbf2341dcc5c2eb", "target_timerange": {"start": 0, "duration": 3000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 3000000}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["speed_27f6782606d8456f8022f11d87cb5297"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "hdr_settings": {"intensity": 1.0, "mode": 1, "nits": 1000}, "render_index": 0}], "type": "video"}], "update_time": 0, "version": 360000}