{"canvas_config": {"width": 1920, "height": 1080, "ratio": "original"}, "color_space": 0, "config": {"adjust_max_index": 1, "attachment_info": [], "combination_max_index": 1, "export_range": null, "extract_audio_last_index": 1, "lyrics_recognition_id": "", "lyrics_sync": true, "lyrics_taskinfo": [], "maintrack_adsorb": true, "material_save_mode": 0, "multi_language_current": "none", "multi_language_list": [], "multi_language_main": "none", "multi_language_mode": "none", "original_sound_last_index": 1, "record_audio_last_index": 1, "sticker_max_index": 1, "subtitle_keywords_config": null, "subtitle_recognition_id": "", "subtitle_sync": true, "subtitle_taskinfo": [], "system_font_list": [], "video_mute": false, "zoom_info_params": null}, "cover": null, "create_time": 0, "duration": 5000000, "extra_info": null, "fps": 30, "free_render_index_mode_on": false, "group_container": null, "id": "946ff21c77194818aa0ae41192d1d9c8", "keyframe_graph_list": [], "keyframes": {"adjusts": [], "audios": [], "effects": [], "filters": [], "handwrites": [], "stickers": [], "texts": [], "videos": []}, "last_modified_platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "materials": {"ai_translates": [], "audio_balances": [], "audio_effects": [], "audio_fades": [], "audio_track_indexes": [], "audios": [{"app_id": 0, "category_id": "", "category_name": "local", "check_flag": 3, "copyright_limit_type": "none", "duration": 5185000, "effect_id": "", "formula_id": "", "id": "ffdb7367953f379daddd00230650c504", "local_material_id": "ffdb7367953f379daddd00230650c504", "music_id": "ffdb7367953f379daddd00230650c504", "name": "audio.mp3", "path": "D:\\pythonProject\\MyProject\\pyJianYingDraft\\readme_assets\\tutorial\\audio.mp3", "source_platform": 0, "type": "extract_music", "wave_points": []}], "beats": [], "canvases": [], "chromas": [], "color_curves": [], "digital_humans": [], "drafts": [], "effects": [], "flowers": [], "green_screens": [], "handwrites": [], "hsl": [], "images": [], "log_color_wheels": [], "loudnesses": [], "manual_deformations": [], "masks": [], "material_animations": [], "material_colors": [], "multi_language_refs": [], "placeholders": [], "plugin_effects": [], "primary_color_wheels": [], "realtime_denoises": [], "shapes": [], "smart_crops": [], "smart_relights": [], "sound_channel_mappings": [], "sounds": [], "speeds": [{"curve_speed": null, "id": "speed_e14b1998f6254ba39f45851f2ecc6db7", "mode": 0, "speed": 1.0, "type": "speed"}, {"curve_speed": null, "id": "speed_e8dde5c0e2854c579fbbf758610fc8ae", "mode": 0, "speed": 1.0, "type": "speed"}], "stickers": [], "tail_leaders": [], "text_templates": [], "texts": [{"alignment": 0, "background_alpha": 1.0, "background_color": "#FFFFFF", "background_height": 0.14, "background_horizontal_offset": 0.0, "background_round_radius": 0.0, "background_style": 0, "background_vertical_offset": 0.0, "background_width": 0.14, "check_flag": 7, "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 0.0, 0.0]}}}, \"range\": [0, 6], \"size\": 20.0, \"bold\": true, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"第一阶段文本\"}", "font_category": "system", "font_id": "", "font_path": "", "font_resource_id": "", "font_size": 8.0, "font_title": "", "font_url": "", "force_apply_line_max_width": false, "global_alpha": 1.0, "has_shadow": false, "id": "e0cd903f48cb45cf852ecf255bd5c48e", "initial_scale": 1.0, "is_rich_text": false, "letter_spacing": 0.0, "line_feed": 0, "line_max_width": 0.82, "line_spacing": 0.02, "preset_category": "", "preset_category_id": "", "preset_has_set_alignment": false, "preset_id": "", "preset_index": 0, "preset_name": "", "shadow_alpha": 0.9, "shadow_angle": -45.0, "shadow_color": [0.0, 0.0, 0.0], "shadow_distance": 5.0, "shadow_point": [1.0, 1.0], "shadow_smoothing": 1.0, "shape_clip_x": false, "shape_clip_y": false, "sub_type": 0, "text_alpha": 1.0, "text_color": [1.0, 1.0, 1.0], "text_curve": null, "text_preset_resource_id": "", "text_size": 30, "text_to_audio_ids": [], "type": "text", "typesetting": 0, "use_effect_default_color": true, "words": {"end_time": [], "start_time": [], "text": []}}, {"alignment": 0, "background_alpha": 1.0, "background_color": "#FFFFFF", "background_height": 0.14, "background_horizontal_offset": 0.0, "background_round_radius": 0.0, "background_style": 0, "background_vertical_offset": 0.0, "background_width": 0.14, "check_flag": 7, "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 6], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"第二阶段文本\"}", "font_category": "system", "font_id": "", "font_path": "", "font_resource_id": "", "font_size": 8.0, "font_title": "", "font_url": "", "force_apply_line_max_width": false, "global_alpha": 1.0, "has_shadow": false, "id": "495766809d4c4cf09c48af9d1f122e59", "initial_scale": 1.0, "is_rich_text": false, "letter_spacing": 0.0, "line_feed": 0, "line_max_width": 0.82, "line_spacing": 0.02, "preset_category": "", "preset_category_id": "", "preset_has_set_alignment": false, "preset_id": "", "preset_index": 0, "preset_name": "", "shadow_alpha": 0.9, "shadow_angle": -45.0, "shadow_color": [0.0, 0.0, 0.0], "shadow_distance": 5.0, "shadow_point": [1.0, 1.0], "shadow_smoothing": 1.0, "shape_clip_x": false, "shape_clip_y": false, "sub_type": 0, "text_alpha": 1.0, "text_color": [1.0, 1.0, 1.0], "text_curve": null, "text_preset_resource_id": "", "text_size": 30, "text_to_audio_ids": [], "type": "text", "typesetting": 0, "use_effect_default_color": true, "words": {"end_time": [], "start_time": [], "text": []}}], "time_marks": [], "transitions": [], "video_effects": [], "video_trackings": [], "videos": [{"audio_fade": null, "category_id": "", "category_name": "local", "check_flag": 63487, "crop": {"upper_left_x": 0.0, "upper_left_y": 0.0, "upper_right_x": 1.0, "upper_right_y": 0.0, "lower_left_x": 0.0, "lower_left_y": 1.0, "lower_right_x": 1.0, "lower_right_y": 1.0}, "crop_ratio": "free", "crop_scale": 1.0, "duration": 5000000, "height": 1080, "id": "c6b8e34944394b019b71f5cf56feeb39", "local_material_id": "", "material_id": "c6b8e34944394b019b71f5cf56feeb39", "material_name": "video.mp4", "media_path": "", "path": "./readme_assets/tutorial/video.mp4", "type": "video", "width": 1920}], "vocal_beautifys": [], "vocal_separations": []}, "mutable_config": null, "name": "", "new_version": "110.0.0", "relationships": [], "render_index_track_mode_on": false, "retouch_cover": null, "source": "default", "static_cover_image_path": "", "time_marks": null, "tracks": [{"attribute": 0, "flag": 0, "id": "9a47e976a46349d9907be567186caf6f", "is_default_name": false, "name": "video", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "e14b1998f6254ba39f45851f2ecc6db7", "material_id": "c6b8e34944394b019b71f5cf56feeb39", "target_timerange": {"start": 0, "duration": 0}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 0}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["speed_e14b1998f6254ba39f45851f2ecc6db7"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "hdr_settings": {"intensity": 1.0, "mode": 1, "nits": 1000}, "render_index": 0}], "type": "video"}, {"attribute": 0, "flag": 0, "id": "d68aa8ca22c04e2a8ae0c181df3a6271", "is_default_name": false, "name": "text", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "a855f0686c434628971669b4ddc366b1", "material_id": "e0cd903f48cb45cf852ecf255bd5c48e", "target_timerange": {"start": 0, "duration": 0}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["e0cd903f48cb45cf852ecf255bd5c48e"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "fbd01720fcff47aab0aad6cc4cddcf87", "material_id": "495766809d4c4cf09c48af9d1f122e59", "target_timerange": {"start": 3000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["495766809d4c4cf09c48af9d1f122e59"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}], "type": "text"}, {"attribute": 0, "flag": 0, "id": "b0de21b11de44b0b83f442a01738edc8", "is_default_name": false, "name": "audio", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 0.7, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "e8dde5c0e2854c579fbbf758610fc8ae", "material_id": "ffdb7367953f379daddd00230650c504", "target_timerange": {"start": 2000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 2000000}, "speed": 1.0, "volume": 0.7, "extra_material_refs": ["ffdb7367953f379daddd00230650c504", "speed_e8dde5c0e2854c579fbbf758610fc8ae"], "is_tone_modify": false, "clip": null, "hdr_settings": null, "render_index": 0}], "type": "audio"}], "update_time": 0, "version": 360000}