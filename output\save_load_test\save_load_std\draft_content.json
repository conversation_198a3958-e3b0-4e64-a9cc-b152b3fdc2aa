{"canvas_config": {"width": 1920, "height": 1080, "ratio": "original"}, "color_space": 0, "config": {"adjust_max_index": 1, "attachment_info": [], "combination_max_index": 1, "export_range": null, "extract_audio_last_index": 1, "lyrics_recognition_id": "", "lyrics_sync": true, "lyrics_taskinfo": [], "maintrack_adsorb": true, "material_save_mode": 0, "multi_language_current": "none", "multi_language_list": [], "multi_language_main": "none", "multi_language_mode": "none", "original_sound_last_index": 1, "record_audio_last_index": 1, "sticker_max_index": 1, "subtitle_keywords_config": null, "subtitle_recognition_id": "", "subtitle_sync": true, "subtitle_taskinfo": [], "system_font_list": [], "video_mute": false, "zoom_info_params": null}, "cover": null, "create_time": 0, "duration": 5000000, "extra_info": null, "fps": 30, "free_render_index_mode_on": false, "group_container": null, "id": "91E08AC5-22FB-47e2-9AA0-7DC300FAEA2B", "keyframe_graph_list": [], "keyframes": {"adjusts": [], "audios": [], "effects": [], "filters": [], "handwrites": [], "stickers": [], "texts": [], "videos": []}, "last_modified_platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "materials": {"ai_translates": [], "audio_balances": [], "audio_effects": [], "audio_fades": [], "audio_track_indexes": [], "audios": [{"app_id": 0, "category_id": "", "category_name": "local", "check_flag": 3, "copyright_limit_type": "none", "duration": 5185000, "effect_id": "", "formula_id": "", "id": "ffdb7367953f379daddd00230650c504", "local_material_id": "ffdb7367953f379daddd00230650c504", "music_id": "ffdb7367953f379daddd00230650c504", "name": "audio.mp3", "path": "D:\\pythonProject\\MyProject\\pyJianYingDraft\\readme_assets\\tutorial\\audio.mp3", "source_platform": 0, "type": "extract_music", "wave_points": []}], "beats": [], "canvases": [], "chromas": [], "color_curves": [], "digital_humans": [], "drafts": [], "effects": [], "flowers": [], "green_screens": [], "handwrites": [], "hsl": [], "images": [], "log_color_wheels": [], "loudnesses": [], "manual_deformations": [], "masks": [], "material_animations": [], "material_colors": [], "multi_language_refs": [], "placeholders": [], "plugin_effects": [], "primary_color_wheels": [], "realtime_denoises": [], "shapes": [], "smart_crops": [], "smart_relights": [], "sound_channel_mappings": [], "speeds": [{"curve_speed": null, "id": "c2af4dc2bf684f9eb51aed85d9381dee", "mode": 0, "speed": 1.0, "type": "speed"}, {"curve_speed": null, "id": "f7eff72096764cae806b34ac93527a8e", "mode": 0, "speed": 1.0, "type": "speed"}], "stickers": [], "tail_leaders": [], "text_templates": [], "texts": [{"id": "012e499612eb48798b7c17242e9c9f8e", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 6], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"第二阶段文本\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 1.0}, {"id": "497eb95e2bdc45b39745624eb0e67b9a", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 0.0, 0.0]}}}, \"range\": [0, 6], \"size\": 20.0, \"bold\": true, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"第一阶段文本\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 1.0}], "time_marks": [], "transitions": [], "video_effects": [], "video_trackings": [], "videos": [{"audio_fade": null, "category_id": "", "category_name": "local", "check_flag": 63487, "crop": {"upper_left_x": 0.0, "upper_left_y": 0.0, "upper_right_x": 1.0, "upper_right_y": 0.0, "lower_left_x": 0.0, "lower_left_y": 1.0, "lower_right_x": 1.0, "lower_right_y": 1.0}, "crop_ratio": "free", "crop_scale": 1.0, "duration": 5000000, "height": 1080, "id": "e940887fd7b137cb9aac108c247075d7", "local_material_id": "", "material_id": "e940887fd7b137cb9aac108c247075d7", "material_name": "video.mp4", "media_path": "", "path": "D:\\pythonProject\\MyProject\\pyJianYingDraft\\readme_assets\\tutorial\\video.mp4", "type": "video", "width": 2046}], "vocal_beautifys": [], "vocal_separations": []}, "mutable_config": null, "name": "", "new_version": "110.0.0", "relationships": [], "render_index_track_mode_on": false, "retouch_cover": null, "source": "default", "static_cover_image_path": "", "time_marks": null, "tracks": [{"attribute": 0, "flag": 0, "id": "fc1d26f8a4284909a449a3523252654e", "is_default_name": false, "name": "video", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "561a37a3f6df4f8e96067497fff386c4", "material_id": "e940887fd7b137cb9aac108c247075d7", "target_timerange": {"start": 0, "duration": 3000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 3000000}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["f7eff72096764cae806b34ac93527a8e"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "hdr_settings": {"intensity": 1.0, "mode": 1, "nits": 1000}, "render_index": 0}], "type": "video"}, {"attribute": 0, "flag": 0, "id": "bee8d90c0c4e4ca5bd6fe098c5c352ca", "is_default_name": false, "name": "audio", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "4b0f71acaaff4ff1924cf65146b4e29a", "material_id": "ffdb7367953f379daddd00230650c504", "target_timerange": {"start": 2000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 2000000}, "speed": 1.0, "volume": 0.7, "extra_material_refs": ["c2af4dc2bf684f9eb51aed85d9381dee"], "is_tone_modify": false, "clip": null, "hdr_settings": null, "render_index": 0}], "type": "audio"}, {"attribute": 0, "flag": 0, "id": "963b2ff1e04446d7866aeb7e1d3d4f8e", "is_default_name": false, "name": "text", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "df0ed73f15b8472fb81c5f18a256b504", "material_id": "497eb95e2bdc45b39745624eb0e67b9a", "target_timerange": {"start": 1000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["10064158c5074d589ea48e6efbd1005c"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}], "type": "text"}, {"attribute": 0, "flag": 0, "id": "32875579d87e4ac4939bbe4a099a9acb", "is_default_name": false, "name": "text", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "6e50ea086e81431497732bee7505cd63", "material_id": "012e499612eb48798b7c17242e9c9f8e", "target_timerange": {"start": 3000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["7a2a4c1e233f4d4fa3b66ed13842e319"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}], "type": "text"}], "update_time": 0, "version": 360000}