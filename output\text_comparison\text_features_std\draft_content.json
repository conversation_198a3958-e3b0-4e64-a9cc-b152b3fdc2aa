{"canvas_config": {"width": 1920, "height": 1080, "ratio": "original"}, "color_space": 0, "config": {"adjust_max_index": 1, "attachment_info": [], "combination_max_index": 1, "export_range": null, "extract_audio_last_index": 1, "lyrics_recognition_id": "", "lyrics_sync": true, "lyrics_taskinfo": [], "maintrack_adsorb": true, "material_save_mode": 0, "multi_language_current": "none", "multi_language_list": [], "multi_language_main": "none", "multi_language_mode": "none", "original_sound_last_index": 1, "record_audio_last_index": 1, "sticker_max_index": 1, "subtitle_keywords_config": null, "subtitle_recognition_id": "", "subtitle_sync": true, "subtitle_taskinfo": [], "system_font_list": [], "video_mute": false, "zoom_info_params": null}, "cover": null, "create_time": 0, "duration": 12000000, "extra_info": null, "fps": 30, "free_render_index_mode_on": false, "group_container": null, "id": "91E08AC5-22FB-47e2-9AA0-7DC300FAEA2B", "keyframe_graph_list": [], "keyframes": {"adjusts": [], "audios": [], "effects": [], "filters": [], "handwrites": [], "stickers": [], "texts": [], "videos": []}, "last_modified_platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "materials": {"ai_translates": [], "audio_balances": [], "audio_effects": [], "audio_fades": [], "audio_track_indexes": [], "audios": [], "beats": [], "canvases": [], "chromas": [], "color_curves": [], "digital_humans": [], "drafts": [], "effects": [], "flowers": [], "green_screens": [], "handwrites": [], "hsl": [], "images": [], "log_color_wheels": [], "loudnesses": [], "manual_deformations": [], "masks": [], "material_animations": [{"id": "bb912f54dadd4a8bb46dc6d3d1957b9d", "type": "sticker_animation", "multi_language_current": "none", "animations": [{"anim_adjust_params": null, "platform": "all", "panel": "", "material_type": "sticker", "name": "弹入", "id": "1644313", "type": "in", "resource_id": "6887482184844710413", "start": 0, "duration": 500000}]}, {"id": "75857f91541c424abf1bc8f058f8538e", "type": "sticker_animation", "multi_language_current": "none", "animations": [{"anim_adjust_params": null, "platform": "all", "panel": "", "material_type": "sticker", "name": "弹入", "id": "1644313", "type": "in", "resource_id": "6887482184844710413", "start": 0, "duration": 500000}]}], "material_colors": [], "multi_language_refs": [], "placeholders": [], "plugin_effects": [], "primary_color_wheels": [], "realtime_denoises": [], "shapes": [], "smart_crops": [], "smart_relights": [], "sound_channel_mappings": [], "speeds": [], "stickers": [], "tail_leaders": [], "text_templates": [], "texts": [{"id": "8cd2b3f4670e4455a58eebdd69b93ed3", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 7], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"1. 基础文本\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 1.0}, {"id": "d0e691457efc49dbbd4bf34adaf3b409", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 0.0, 0.0]}}}, \"range\": [0, 8], \"size\": 15.0, \"bold\": true, \"italic\": true, \"underline\": false, \"strokes\": []}], \"text\": \"2. 带样式文本\"}", "typesetting": 0, "alignment": 1, "letter_spacing": 0.25, "line_spacing": 0.52, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 0.9}, {"id": "640ebfb2777c45a496934d95d86c5c23", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 8], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": [{\"content\": {\"solid\": {\"alpha\": 1.0, \"color\": [0.0, 0.0, 1.0]}}, \"width\": 0.12}]}], \"text\": \"3. 带描边文本\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 15, "type": "text", "global_alpha": 1.0}, {"id": "8274bb20a31943c2979ca78c1395da15", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 8], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"4. 带背景文本\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 23, "type": "text", "global_alpha": 1.0, "background_style": 2, "background_color": "#00FF00", "background_alpha": 0.7, "background_round_radius": 0.3, "background_height": 0.14, "background_width": 0.14, "background_horizontal_offset": 0.0, "background_vertical_offset": 0.0}, {"id": "7b8fe82b3bf24b1796d42f137e0c6e35", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 8], \"size\": 8.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"5. 带动画文本\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "text", "global_alpha": 1.0}, {"id": "491518ac85fa419f8b7929c2ec48f67a", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 0.0]}}}, \"range\": [0, 9], \"size\": 18.0, \"bold\": true, \"italic\": true, \"underline\": false, \"strokes\": [{\"content\": {\"solid\": {\"alpha\": 0.8, \"color\": [1.0, 0.0, 1.0]}}, \"width\": 0.08000000000000002}]}], \"text\": \"6. 复杂文本效果\"}", "typesetting": 0, "alignment": 2, "letter_spacing": 0.15000000000000002, "line_spacing": 0.42000000000000004, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 31, "type": "text", "global_alpha": 0.95, "background_style": 1, "background_color": "#FF8000", "background_alpha": 0.6, "background_round_radius": 0.25, "background_height": 0.14, "background_width": 0.14, "background_horizontal_offset": 0.0, "background_vertical_offset": 0.0}], "time_marks": [], "transitions": [], "video_effects": [], "video_trackings": [], "videos": [], "vocal_beautifys": [], "vocal_separations": []}, "mutable_config": null, "name": "", "new_version": "110.0.0", "relationships": [], "render_index_track_mode_on": false, "retouch_cover": null, "source": "default", "static_cover_image_path": "", "time_marks": null, "tracks": [{"attribute": 0, "flag": 0, "id": "3e1172d6e62444f2a53541676909e3d1", "is_default_name": false, "name": "text", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "84b7d291925448b7b2995d6eec43f8fa", "material_id": "8cd2b3f4670e4455a58eebdd69b93ed3", "target_timerange": {"start": 0, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["01cd816e6ae34092a15d47361867018a"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "4d0d383c8afe4cf7b30e81bfe09292e4", "material_id": "d0e691457efc49dbbd4bf34adaf3b409", "target_timerange": {"start": 2000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["7ebc2b8ea29345e5b1bc6e67fe1e1aec"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "161388bd94db4cbaa616313b87892276", "material_id": "640ebfb2777c45a496934d95d86c5c23", "target_timerange": {"start": 4000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["e055f518508b48c69765c8d68c08810b"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "40463dd7db3c4ef8aef62aa5a155ce7a", "material_id": "8274bb20a31943c2979ca78c1395da15", "target_timerange": {"start": 6000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["50aa8f1840604fac9c26e6e8a381dbea"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "ef1b73462e6948e6aa617de31a5ee9c4", "material_id": "7b8fe82b3bf24b1796d42f137e0c6e35", "target_timerange": {"start": 8000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["b715171b7c164612acdd614279bc538d", "bb912f54dadd4a8bb46dc6d3d1957b9d"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "b922f667016b4f698199ea7e0747d625", "material_id": "491518ac85fa419f8b7929c2ec48f67a", "target_timerange": {"start": 10000000, "duration": 2000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["a9a565e3a89c443a8c659873e77eb1e2", "75857f91541c424abf1bc8f058f8538e"], "is_tone_modify": false, "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15000}], "type": "text"}], "update_time": 0, "version": 360000}