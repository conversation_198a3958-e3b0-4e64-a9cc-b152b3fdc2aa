"""
完整的文本功能对比测试
创建两个相同的项目：一个用my_jianying，一个用标准pyJianYingDraft
然后对比它们的差异
"""
import my_jianying as mj
import pyJianYingDraft as draft
from pyJianYingDraft import TextSegment, TextStyle, TextBorder, TextBackground, TextIntro, trange as std_trange
import os
import json

# 模拟文本动画类型
class MockTextAnimationType:
    def __init__(self, name, resource_id, effect_id="", duration=0.5):
        self.value = MockValue(name, resource_id, effect_id, duration)

class MockValue:
    def __init__(self, name, resource_id, effect_id="", duration=0.5):
        self.name = name
        self.resource_id = resource_id
        self.effect_id = effect_id
        self.duration = duration

# 定义文本动画类型 - 使用与标准库一致的ID
class MyTextIntro:
    弹入 = MockTextAnimationType("弹入", "6887482184844710413", "1644313", 0.5)
    打字机_I = MockTextAnimationType("打字机_I", "6887482184844710413", "1644313", 1.0)

def create_my_jianying_project():
    """创建my_jianying版本的项目"""
    print("🔄 创建my_jianying版本的项目...")

    try:
        # 创建输出目录
        os.makedirs("./output/text_comparison", exist_ok=True)

        # 创建项目
        draft_folder = mj.DraftFolder("./output/text_comparison")
        script = draft_folder.create_draft("text_features_my", 1920, 1080, allow_replace=True)
        script.add_track("text")
        
        # 1. 基础文本 (0-2秒)
        print("  添加基础文本...")
        basic_text = mj.TextSegment(
            text="1. 基础文本",
            timerange=mj.trange("0s", "2s")
        )
        script.add_segment(basic_text)
        
        # 2. 带样式文本 (2-4秒)
        print("  添加带样式文本...")
        style = mj.TextStyle(
            size=15.0,
            bold=True,
            italic=True,
            color=(1.0, 0.0, 0.0),  # 红色
            alpha=0.9,
            align=1,  # 居中
            letter_spacing=5,
            line_spacing=10
        )
        styled_text = mj.TextSegment(
            text="2. 带样式文本",
            timerange=mj.trange("2s", "2s"),
            style=style
        )
        script.add_segment(styled_text)
        
        # 3. 带描边文本 (4-6秒)
        print("  添加带描边文本...")
        border = mj.TextBorder(
            alpha=1.0,
            color=(0.0, 0.0, 1.0),  # 蓝色描边
            width=60.0
        )
        border_text = mj.TextSegment(
            text="3. 带描边文本",
            timerange=mj.trange("4s", "2s"),
            border=border
        )
        script.add_segment(border_text)
        
        # 4. 带背景文本 (6-8秒)
        print("  添加带背景文本...")
        background = mj.TextBackground(
            color="#00FF00",  # 绿色背景
            alpha=0.7,
            style=2,
            round_radius=0.3
        )
        bg_text = mj.TextSegment(
            text="4. 带背景文本",
            timerange=mj.trange("6s", "2s"),
            background=background
        )
        script.add_segment(bg_text)
        
        # 5. 带动画文本 (8-10秒)
        print("  添加带动画文本...")
        animation_text = mj.TextSegment(
            text="5. 带动画文本",
            timerange=mj.trange("8s", "2s")
        )
        animation_text.add_animation(MyTextIntro.弹入)
        script.add_segment(animation_text)
        
        # 6. 复杂文本 - 所有效果组合 (10-12秒)
        print("  添加复杂文本（所有效果组合）...")
        complex_style = mj.TextStyle(
            size=18.0,
            bold=True,
            italic=True,
            color=(1.0, 1.0, 0.0),  # 黄色
            alpha=0.95,
            align=2,  # 右对齐
            letter_spacing=3,
            line_spacing=8
        )
        complex_border = mj.TextBorder(
            alpha=0.8,
            color=(1.0, 0.0, 1.0),  # 紫色描边
            width=40.0
        )
        complex_background = mj.TextBackground(
            color="#FF8000",  # 橙色背景
            alpha=0.6,
            style=1,
            round_radius=0.25
        )
        complex_text = mj.TextSegment(
            text="6. 复杂文本效果",
            timerange=mj.trange("10s", "2s"),
            style=complex_style,
            border=complex_border,
            background=complex_background
        )
        complex_text.add_animation(MyTextIntro.弹入)
        # 为了与标准库保持一致，只添加一个动画
        script.add_segment(complex_text)
        
        # 保存项目
        project_path = script.save()
        print(f"✅ my_jianying项目已保存到: {project_path}")

        return project_path

    except Exception as e:
        print(f"❌ my_jianying项目创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_standard_project():
    """创建标准pyJianYingDraft版本的项目"""
    print("🔄 创建标准pyJianYingDraft版本的项目...")

    try:
        # 创建项目
        draft_folder = draft.DraftFolder("./output/text_comparison")
        script = draft_folder.create_draft("text_features_std", 1920, 1080, allow_replace=True)
        script.add_track(draft.TrackType.text)

        # 1. 基础文本 (0-2秒)
        print("  添加基础文本...")
        basic_text = TextSegment(
            text="1. 基础文本",
            timerange=std_trange("0s", "2s")
        )
        script.add_segment(basic_text)

        # 2. 带样式文本 (2-4秒)
        print("  添加带样式文本...")
        style = TextStyle(
            size=15.0,
            bold=True,
            italic=True,
            color=(1.0, 0.0, 0.0),  # 红色
            alpha=0.9,
            align=1,  # 居中
            letter_spacing=5,
            line_spacing=10
        )
        styled_text = TextSegment(
            text="2. 带样式文本",
            timerange=std_trange("2s", "2s"),
            style=style
        )
        script.add_segment(styled_text)

        # 3. 带描边文本 (4-6秒)
        print("  添加带描边文本...")
        border = TextBorder(
            alpha=1.0,
            color=(0.0, 0.0, 1.0),  # 蓝色描边
            width=60.0
        )
        border_text = TextSegment(
            text="3. 带描边文本",
            timerange=std_trange("4s", "2s"),
            border=border
        )
        script.add_segment(border_text)

        # 4. 带背景文本 (6-8秒)
        print("  添加带背景文本...")
        background = TextBackground(
            color="#00FF00",  # 绿色背景
            alpha=0.7,
            style=2,
            round_radius=0.3
        )
        bg_text = TextSegment(
            text="4. 带背景文本",
            timerange=std_trange("6s", "2s"),
            background=background
        )
        script.add_segment(bg_text)

        # 5. 带动画文本 (8-10秒)
        print("  添加带动画文本...")
        animation_text = TextSegment(
            text="5. 带动画文本",
            timerange=std_trange("8s", "2s")
        )
        animation_text.add_animation(TextIntro.弹入)
        script.add_segment(animation_text)

        # 6. 复杂文本 - 所有效果组合 (10-12秒)
        print("  添加复杂文本（所有效果组合）...")
        complex_style = TextStyle(
            size=18.0,
            bold=True,
            italic=True,
            color=(1.0, 1.0, 0.0),  # 黄色
            alpha=0.95,
            align=2,  # 右对齐
            letter_spacing=3,
            line_spacing=8
        )
        complex_border = TextBorder(
            alpha=0.8,
            color=(1.0, 0.0, 1.0),  # 紫色描边
            width=40.0
        )
        complex_background = TextBackground(
            color="#FF8000",  # 橙色背景
            alpha=0.6,
            style=1,
            round_radius=0.25
        )
        complex_text = TextSegment(
            text="6. 复杂文本效果",
            timerange=std_trange("10s", "2s"),
            style=complex_style,
            border=complex_border,
            background=complex_background
        )
        complex_text.add_animation(TextIntro.弹入)
        # 标准库不允许同一片段添加两个相同类型的动画，所以只添加一个
        script.add_segment(complex_text)

        # 保存项目
        project_path = script.save()
        print(f"✅ 标准项目已保存到: {project_path}")

        # 返回正确的路径
        return "./output/text_comparison/text_features_std"

    except Exception as e:
        print(f"❌ 标准项目创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_project(project_path, project_name):
    """分析生成的项目"""
    if not project_path:
        return None

    print(f"\n📊 {project_name}项目分析:")

    try:
        json_file = os.path.join(project_path, "draft_content.json")
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"   - 文本材料数量: {len(data['materials']['texts'])}")
        print(f"   - 动画材料数量: {len(data['materials']['material_animations'])}")
        print(f"   - 轨道数量: {len(data['tracks'])}")
        print(f"   - 轨道片段数量: {len(data['tracks'][0]['segments']) if data['tracks'] else 0}")
        print(f"   - 项目总时长: {data['duration']} (微秒) = {data['duration']/1000000:.1f}秒")

        print(f"\n📝 文本内容和效果:")
        for i, text_material in enumerate(data['materials']['texts']):
            content = json.loads(text_material['content'])
            check_flag = text_material['check_flag']
            text_content = content['text']

            # 解析check_flag
            effects = []
            if check_flag & 7:
                effects.append('基础文本')
            if check_flag & 8:
                effects.append('描边')
            if check_flag & 16:
                effects.append('背景')

            print(f"   {i+1}. \"{text_content}\"")
            print(f"      效果: {' + '.join(effects)} (check_flag: {check_flag})")

        return data

    except Exception as e:
        print(f"❌ {project_name}项目分析失败: {e}")
        return None

def compare_projects(my_data, std_data):
    """对比两个项目的差异"""
    print(f"\n🔍 项目对比分析:")

    if not my_data or not std_data:
        print("❌ 无法对比，项目数据缺失")
        return

    # 基础数据对比
    print(f"📊 基础数据对比:")
    my_texts = len(my_data['materials']['texts'])
    std_texts = len(std_data['materials']['texts'])
    print(f"   文本材料数量: my_jianying={my_texts}, 标准={std_texts} {'✅' if my_texts == std_texts else '❌'}")

    my_anims = len(my_data['materials']['material_animations'])
    std_anims = len(std_data['materials']['material_animations'])
    print(f"   动画材料数量: my_jianying={my_anims}, 标准={std_anims} {'✅' if my_anims == std_anims else '❌'}")

    my_duration = my_data['duration']
    std_duration = std_data['duration']
    print(f"   项目时长: my_jianying={my_duration}, 标准={std_duration} {'✅' if my_duration == std_duration else '❌'}")

    # check_flag对比
    print(f"\n📝 文本check_flag对比:")
    all_flags_match = True
    for i, (my_text, std_text) in enumerate(zip(my_data['materials']['texts'], std_data['materials']['texts'])):
        my_content = json.loads(my_text['content'])
        std_content = json.loads(std_text['content'])
        my_flag = my_text['check_flag']
        std_flag = std_text['check_flag']

        match = my_flag == std_flag
        all_flags_match = all_flags_match and match

        print(f"   {i+1}. \"{my_content['text']}\": my={my_flag}, std={std_flag} {'✅' if match else '❌'}")

    # 总结
    print(f"\n🎯 对比总结:")
    if my_texts == std_texts and my_duration == std_duration and all_flags_match:
        if my_anims == std_anims:
            print(f"🎉 完美匹配！两个项目完全一致！")
        else:
            print(f"✅ 核心功能一致！仅动画材料数量有微小差异")
    else:
        print(f"⚠️ 发现差异，需要进一步检查")

    print(f"\n📁 项目文件位置:")
    print(f"   my_jianying版本: ./output/text_comparison/text_features_my/")
    print(f"   标准版本: ./output/text_comparison/text_features_std/")
    print(f"🎬 两个项目都可以在剪映中打开查看效果差异")

def main():
    """主函数"""
    print("=== 完整文本功能对比测试 ===")
    print("创建两个相同的项目：一个用my_jianying，一个用标准pyJianYingDraft")
    print("然后对比它们的差异\n")

    # 创建两个项目
    my_path = create_my_jianying_project()
    std_path = create_standard_project()

    # 分析两个项目
    my_data = analyze_project(my_path, "my_jianying")
    std_data = analyze_project(std_path, "标准")

    # 对比项目
    compare_projects(my_data, std_data)

    print(f"\n🎯 测试完成！")
    print(f"✅ 功能验证:")
    print(f"   - 基础文本创建")
    print(f"   - 文本样式（大小、颜色、粗体、斜体、对齐、间距）")
    print(f"   - 文本描边效果")
    print(f"   - 文本背景效果")
    print(f"   - 文本动画效果")
    print(f"   - 复杂文本（多种效果组合）")
    print(f"   - 多个文本片段时间轴管理")
    print(f"   - 项目总时长计算")
    print(f"⏱️ 每个项目时长: 12秒")
    print(f"📝 每个项目包含6个不同的文本效果")

if __name__ == "__main__":
    main()
