# my_jianying 快速入门

## 🚀 5分钟上手

### 第一步：导入库

```python
import my_jianying as mj
```

### 第二步：创建项目

```python
# 创建草稿文件夹
draft_folder = mj.DraftFolder("./my_projects")

# 创建新项目
script = draft_folder.create_draft("我的第一个项目", 1920, 1080, allow_replace=True)
script.add_track("video")
```

### 第三步：添加视频

```python
# 创建视频片段
video = mj.VideoSegment("your_video.mp4", mj.trange("0s", "5s"))

# 添加到项目
script.add_segment(video)
```

### 第四步：添加效果（可选）

```python
# 定义效果类型（示例）
class MyEffects:
    斜切 = MockAnimationType("斜切", "7210657307938525751")
    亮肤 = MockAnimationType("亮肤", "7127655008715230495")

# 添加效果
video.add_animation(MyEffects.斜切)
video.add_filter(MyEffects.亮肤, 0.8)
```

### 第五步：保存项目

```python
# 保存并获取路径
project_path = script.save()
print(f"项目已保存到: {project_path}")
```

## 🎯 完整示例

```python
import my_jianying as mj

# 创建项目
draft_folder = mj.DraftFolder("./my_projects")
script = draft_folder.create_draft("快速示例", 1920, 1080, allow_replace=True)
script.add_track("video")

# 添加视频片段
video = mj.VideoSegment("video.mp4", mj.trange("0s", "3s"))
script.add_segment(video)

# 保存项目
project_path = script.save()
print(f"✅ 完成！项目保存在: {project_path}")
```

## 📁 输出结果

运行后会在指定目录生成：
```
my_projects/
└── 快速示例/
    ├── draft_content.json      # 主要内容
    └── draft_meta_info.json    # 元数据
```

## 🎬 在剪映中打开

1. 打开剪映专业版
2. 选择"打开项目"
3. 导航到生成的项目文件夹
4. 选择项目文件夹即可打开

## 📖 下一步

- 查看 `my_jianying使用说明.md` 了解详细功能
- 运行 `my_jianying_demo.py` 查看更多示例
- 尝试添加动画、滤镜、转场等效果

---

**开始您的剪映自动化之旅！** 🎥
